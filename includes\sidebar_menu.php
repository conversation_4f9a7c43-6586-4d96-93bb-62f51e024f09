<?php
// Obtener la página actual para marcar el menú activo
$current_page = basename($_SERVER['PHP_SELF']);
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

// Función para verificar si un menú está activo
function isActive($page, $dir = null) {
    global $current_page, $current_dir;
    if ($dir) {
        return ($current_dir === $dir) ? 'active' : '';
    }
    return ($current_page === $page) ? 'active' : '';
}

// Función para verificar si un menú debe estar abierto
function isMenuOpen($dir) {
    global $current_dir;
    return ($current_dir === $dir) ? 'menu-open' : '';
}
?>

<!-- Dashboard Principal -->
<li class="nav-item">
    <a href="<?= url('') ?>" class="nav-link <?= isActive('index.php') ?>">
        <i class="nav-icon bi bi-speedometer"></i>
        <p>Dashboard</p>
    </a>
</li>

<?php if ($usuario['rol'] === 'admin'): ?>
    <!-- <PERSON><PERSON> de Administrador -->
    <li class="nav-header">ADMINISTRACIÓN</li>
    
    <li class="nav-item <?= isMenuOpen('admin') ?>">
        <a href="#" class="nav-link <?= isActive('', 'admin') ?>">
            <i class="nav-icon bi bi-gear-fill"></i>
            <p>
                Administración
                <i class="nav-arrow bi bi-chevron-right"></i>
            </p>
        </a>
        <ul class="nav nav-treeview">
            <li class="nav-item">
                <a href="<?= url('admin/empleados.php') ?>" class="nav-link <?= isActive('empleados.php') ?>">
                    <i class="nav-icon bi bi-people-fill"></i>
                    <p>Empleados</p>
                </a>
            </li>
            <li class="nav-item">
                <a href="<?= url('admin/categorias.php') ?>" class="nav-link <?= isActive('categorias.php') ?>">
                    <i class="nav-icon bi bi-tags"></i>
                    <p>Categorías</p>
                </a>
            </li>
            <li class="nav-item">
                <a href="<?= url('admin/productos.php') ?>" class="nav-link <?= isActive('productos.php') ?>">
                    <i class="nav-icon bi bi-grid-3x3-gap"></i>
                    <p>Productos</p>
                </a>
            </li>
            <li class="nav-item">
                <a href="<?= url('admin/inventario.php') ?>" class="nav-link <?= isActive('inventario.php') ?>">
                    <i class="nav-icon bi bi-box-seam"></i>
                    <p>Inventario</p>
                </a>
            </li>
            <li class="nav-item">
                <a href="<?= url('admin/compras.php') ?>" class="nav-link <?= isActive('compras.php') ?>">
                    <i class="nav-icon bi bi-cart-plus"></i>
                    <p>Compras</p>
                </a>
            </li>
            <li class="nav-item">
                <a href="<?= url('admin/reportes.php') ?>" class="nav-link <?= isActive('reportes.php') ?>">
                    <i class="nav-icon bi bi-graph-up"></i>
                    <p>Reportes</p>
                </a>
            </li>
            <li class="nav-item">
                <a href="<?= url('admin/configuracion.php') ?>" class="nav-link <?= isActive('configuracion.php') ?>">
                    <i class="nav-icon bi bi-gear"></i>
                    <p>Configuración</p>
                </a>
            </li>
        </ul>
    </li>
    
    <!-- Ver todas las áreas -->
    <li class="nav-item">
        <a href="<?= url('admin/ordenes_general.php') ?>" class="nav-link <?= isActive('ordenes_general.php') ?>">
            <i class="nav-icon bi bi-list-check"></i>
            <p>Todas las Órdenes</p>
        </a>
    </li>

<?php elseif ($usuario['rol'] === 'mesero'): ?>
    <!-- Menú de Mesero -->
    <li class="nav-header">ÁREA DE MESEROS</li>
    
    <li class="nav-item">
        <a href="<?= url('meseros/tomar_orden.php') ?>" class="nav-link <?= isActive('tomar_orden.php') ?>">
            <i class="nav-icon bi bi-plus-circle-fill"></i>
            <p>Tomar Orden</p>
        </a>
    </li>
    
    <li class="nav-item">
        <a href="<?= url('meseros/ver_ordenes.php') ?>" class="nav-link <?= isActive('ver_ordenes.php') ?>">
            <i class="nav-icon bi bi-list-ul"></i>
            <p>Mis Órdenes</p>
        </a>
    </li>

<?php elseif ($usuario['rol'] === 'cocina'): ?>
    <!-- Menú de Cocina -->
    <li class="nav-header">ÁREA DE COCINA</li>
    
    <li class="nav-item">
        <a href="<?= url('cocina/ordenes.php') ?>" class="nav-link <?= isActive('ordenes.php') ?>">
            <i class="nav-icon bi bi-fire"></i>
            <p>Órdenes de Cocina</p>
        </a>
    </li>
    
    <li class="nav-item">
        <a href="<?= url('cocina/preparacion.php') ?>" class="nav-link <?= isActive('preparacion.php') ?>">
            <i class="nav-icon bi bi-clock-fill"></i>
            <p>En Preparación</p>
        </a>
    </li>

<?php elseif ($usuario['rol'] === 'bebidas'): ?>
    <!-- Menú de Bebidas/Bar -->
    <li class="nav-header">ÁREA DE BEBIDAS</li>
    
    <li class="nav-item">
        <a href="<?= url('bebidas/ordenes.php') ?>" class="nav-link <?= isActive('ordenes.php') ?>">
            <i class="nav-icon bi bi-cup-straw"></i>
            <p>Órdenes de Bebidas</p>
        </a>
    </li>
    
    <li class="nav-item">
        <a href="<?= url('bebidas/preparacion.php') ?>" class="nav-link <?= isActive('preparacion.php') ?>">
            <i class="nav-icon bi bi-clock-fill"></i>
            <p>En Preparación</p>
        </a>
    </li>

<?php endif; ?>

<!-- Menús comunes para todos los roles -->
<li class="nav-header">GENERAL</li>

<li class="nav-item">
    <a href="<?= url('auth/logout.php') ?>" class="nav-link">
        <i class="nav-icon bi bi-box-arrow-right"></i>
        <p>Cerrar Sesión</p>
    </a>
</li>
