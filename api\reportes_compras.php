<?php
// Determinar el tipo de respuesta
$view = $_GET['view'] ?? 'json';
if ($view === 'html') {
    header('Content-Type: text/html; charset=utf-8');
} else {
    header('Content-Type: application/json');
}

// Iniciar sesión y conectar a la base de datos
// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();
require_once '../config/db.php';

// Verificar que el usuario esté logueado y sea administrador
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden acceder a reportes']);
    exit();
}

// Obtener parámetros
$fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-m-d', strtotime('-30 days'));
$fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-d');
$proveedor_id = $_GET['proveedor_id'] ?? '';
$estado = $_GET['estado'] ?? '';
$tipo = $_GET['tipo'] ?? 'resumen';

// Validar fechas
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_inicio) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_fin)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Formato de fecha inválido']);
    exit();
}

try {
    // Construir consulta base
    $where_conditions = ["c.fecha_compra >= ? AND c.fecha_compra <= ?"];
    $params = [$fecha_inicio, $fecha_fin];

    if (!empty($proveedor_id)) {
        $where_conditions[] = "c.proveedor_id = ?";
        $params[] = $proveedor_id;
    }

    if (!empty($estado)) {
        $where_conditions[] = "c.estado = ?";
        $params[] = $estado;
    }

    $where_clause = implode(' AND ', $where_conditions);

    // Obtener resumen ejecutivo
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_compras,
            COALESCE(SUM(c.total), 0) as monto_total,
            COALESCE(AVG(c.total), 0) as promedio_compra,
            COUNT(DISTINCT c.proveedor_id) as proveedores_activos
        FROM compras c
        WHERE $where_clause
    ");
    $stmt->execute($params);
    $resumen = $stmt->fetch(PDO::FETCH_ASSOC);

    // Obtener compras detalladas
    $stmt = $pdo->prepare("
        SELECT 
            c.*,
            p.nombre as proveedor_nombre,
            p.contacto as proveedor_contacto,
            u.nombre as usuario_nombre
        FROM compras c
        LEFT JOIN proveedores p ON c.proveedor_id = p.id
        LEFT JOIN usuarios u ON c.usuario_id = u.id
        WHERE $where_clause
        ORDER BY c.fecha_compra DESC, c.id DESC
    ");
    $stmt->execute($params);
    $compras = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Datos adicionales según el tipo de reporte
    $datos_adicionales = [];

    if ($tipo === 'por_proveedor') {
        // Reporte por proveedor
        $stmt = $pdo->prepare("
            SELECT 
                p.nombre as proveedor_nombre,
                COUNT(c.id) as total_compras,
                COALESCE(SUM(c.total), 0) as monto_total,
                COALESCE(AVG(c.total), 0) as promedio_compra,
                MIN(c.fecha_compra) as primera_compra,
                MAX(c.fecha_compra) as ultima_compra
            FROM compras c
            LEFT JOIN proveedores p ON c.proveedor_id = p.id
            WHERE $where_clause
            GROUP BY c.proveedor_id, p.nombre
            ORDER BY monto_total DESC
        ");
        $stmt->execute($params);
        $datos_adicionales['por_proveedor'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    if ($tipo === 'por_producto') {
        // Reporte por producto
        $stmt = $pdo->prepare("
            SELECT 
                pr.nombre as producto_nombre,
                pr.categoria,
                SUM(dc.cantidad) as cantidad_total,
                COALESCE(SUM(dc.subtotal), 0) as monto_total,
                COALESCE(AVG(dc.precio_unitario), 0) as precio_promedio,
                COUNT(DISTINCT c.id) as compras_involucradas
            FROM compras c
            INNER JOIN detalle_compras dc ON c.id = dc.compra_id
            INNER JOIN productos pr ON dc.producto_id = pr.id
            WHERE $where_clause
            GROUP BY dc.producto_id, pr.nombre, pr.categoria
            ORDER BY monto_total DESC
        ");
        $stmt->execute($params);
        $datos_adicionales['por_producto'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Estadísticas por estado
    $stmt = $pdo->prepare("
        SELECT 
            c.estado,
            COUNT(*) as cantidad,
            COALESCE(SUM(c.total), 0) as monto_total
        FROM compras c
        WHERE $where_clause
        GROUP BY c.estado
        ORDER BY cantidad DESC
    ");
    $stmt->execute($params);
    $datos_adicionales['por_estado'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Estadísticas por mes (si el rango es mayor a 30 días)
    $dias_diferencia = (strtotime($fecha_fin) - strtotime($fecha_inicio)) / (60 * 60 * 24);
    if ($dias_diferencia > 30) {
        $stmt = $pdo->prepare("
            SELECT 
                DATE_FORMAT(c.fecha_compra, '%Y-%m') as mes,
                COUNT(*) as cantidad,
                COALESCE(SUM(c.total), 0) as monto_total
            FROM compras c
            WHERE $where_clause
            GROUP BY DATE_FORMAT(c.fecha_compra, '%Y-%m')
            ORDER BY mes DESC
        ");
        $stmt->execute($params);
        $datos_adicionales['por_mes'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Preparar datos para respuesta
    $data = [
        'success' => true,
        'resumen' => $resumen,
        'compras' => $compras,
        'tipo_reporte' => $tipo,
        'periodo' => [
            'fecha_inicio' => $fecha_inicio,
            'fecha_fin' => $fecha_fin
        ],
        'filtros' => [
            'proveedor_id' => $proveedor_id,
            'estado' => $estado
        ],
        'datos_adicionales' => $datos_adicionales
    ];

    // Respuesta según el tipo solicitado
    if ($view === 'html') {
        include 'views/reporte_compras_html.php';
    } else {
        echo json_encode($data);
    }

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Error en la base de datos: ' . $e->getMessage()
    ]);
}
?>
