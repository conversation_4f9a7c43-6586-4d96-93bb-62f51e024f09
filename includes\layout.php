<?php
// Incluir configuración de sesiones
require_once __DIR__ . '/../config/session_config.php';

// Verificar si el usuario está logueado
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
if (!isset($_SESSION['usuario_id'])) {
    header('Location: ' . url('auth/login.php'));
    exit();
}

// Obtener información del usuario (usar datos existentes si están disponibles)
if (isset($usuario_actual)) {
    // Usar los datos del usuario que ya se obtuvieron en el archivo principal
    $usuario = $usuario_actual;
} else {
    // Si no hay datos del usuario, obtenerlos de la base de datos
    if (!isset($pdo)) {
        require_once __DIR__ . '/../config/db.php';
    }
    $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ?");
    $stmt->execute([$_SESSION['usuario_id']]);
    $usuario = $stmt->fetch();
}

if (!$usuario) {
    session_destroy();
    header('Location: ' . url('auth/login.php'));
    exit();
}
?>
<!doctype html>
<html lang="es">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title><?= isset($page_title) ? $page_title : 'Sistema Restaurante' ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="author" content="Sistema Restaurante" />
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Third Party Plugins -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/overlayscrollbars@2.10.1/styles/overlayscrollbars.min.css" crossorigin="anonymous" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" crossorigin="anonymous" />
    
    <!-- AdminLTE CSS -->
    <link rel="stylesheet" href="/Restaurante/assets/adminlte/css/adminlte.min.css" />
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/Restaurante/assets/css/custom.css" />
    
    <?php if (isset($extra_css)): ?>
        <?= $extra_css ?>
    <?php endif; ?>
</head>

<body class="layout-fixed sidebar-expand-lg bg-body-tertiary">
    <div class="app-wrapper">
        <!-- Header -->
        <nav class="app-header navbar navbar-expand">
            <div class="container-fluid">
                <!-- Start Navbar Links -->
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" data-lte-toggle="sidebar" href="#" role="button">
                            <i class="bi bi-list fs-5"></i>
                        </a>
                    </li>
                    <li class="nav-item d-none d-md-block">
                        <a href="/Restaurante/" class="nav-link fw-medium">Inicio</a>
                    </li>
                </ul>
                
                <!-- End Navbar Links -->
                <ul class="navbar-nav ms-auto">

                    <!-- User Menu -->
                    <li class="nav-item dropdown user-menu">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <img src="/Restaurante/assets/adminlte/assets/img/avatar.png" class="user-image rounded-circle shadow" alt="User Image" />
                            <span class="d-none d-md-inline"><?= htmlspecialchars($usuario['nombre']) ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-lg dropdown-menu-end">
                            <li class="user-header text-bg-primary">
                                <img src="/Restaurante/assets/adminlte/assets/img/avatar.png" class="rounded-circle shadow" alt="User Image" />
                                <p>
                                    <?= htmlspecialchars($usuario['nombre']) ?>
                                    <small><?= ucfirst($usuario['rol']) ?></small>
                                </p>
                            </li>
                            <li class="user-footer">
                                <a href="/Restaurante/auth/logout.php" class="btn btn-default btn-flat float-end">Cerrar Sesión</a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Sidebar -->
        <aside class="app-sidebar">
            <div class="sidebar-brand">
                <a href="/Restaurante/" class="brand-link text-decoration-none">
                    <img src="/Restaurante/assets/adminlte/assets/img/AdminLTELogo.png" alt="Logo" class="brand-image" />
                    <span class="brand-text">Restaurante</span>
                </a>
            </div>
            
            <div class="sidebar-wrapper">
                <nav class="mt-2">
                    <ul class="nav sidebar-menu flex-column" data-lte-toggle="treeview" role="menu" data-accordion="false">
                        <?php include __DIR__ . '/sidebar_menu.php'; ?>
                    </ul>
                </nav>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="app-main">
            <div class="app-content-header">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-sm-6">
                            <h3 class="mb-0"><?= isset($page_title) ? $page_title : 'Dashboard' ?></h3>
                        </div>
                        <div class="col-sm-6">
                            <ol class="breadcrumb float-sm-end">
                                <?php if (isset($breadcrumbs)): ?>
                                    <?php foreach ($breadcrumbs as $breadcrumb): ?>
                                        <?php if (isset($breadcrumb['url'])): ?>
                                            <li class="breadcrumb-item"><a href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['title'] ?></a></li>
                                        <?php else: ?>
                                            <li class="breadcrumb-item active"><?= $breadcrumb['title'] ?></li>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <li class="breadcrumb-item"><a href="/Restaurante/">Inicio</a></li>
                                    <li class="breadcrumb-item active"><?= isset($page_title) ? $page_title : 'Dashboard' ?></li>
                                <?php endif; ?>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="app-content">
                <div class="container-fluid">
                    <?php if (isset($content)): ?>
                        <?= $content ?>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/overlayscrollbars@2.10.1/browser/overlayscrollbars.browser.es6.min.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.min.js" crossorigin="anonymous"></script>
    <script src="/Restaurante/assets/adminlte/js/adminlte.min.js"></script>
    
    <!-- Custom JS -->
    <script src="/Restaurante/assets/js/app.js"></script>
    
    <?php if (isset($extra_js)): ?>
        <?= $extra_js ?>
    <?php endif; ?>
    
    <script>
        // Initialize AdminLTE
        // OverlayScrollbars deshabilitado temporalmente para evitar errores
        /*
        const { OverlayScrollbars } = OverlayScrollbarsGlobal;
        if (OverlayScrollbars) {
            OverlayScrollbars(document.querySelectorAll('.sidebar-wrapper'), {});
        }
        */
    </script>
</body>
</html>
