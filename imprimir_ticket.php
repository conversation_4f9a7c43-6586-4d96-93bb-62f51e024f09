<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket - Cuenta #<?= $_GET['cuenta_id'] ?? 'N/A' ?></title>
    <style>
        /* Estilos para impresión */
        @media print {
            body { margin: 0; }
            .no-print { display: none !important; }
            .ticket { box-shadow: none !important; }
        }
        
        body {
            font-family: 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .ticket {
            max-width: 300px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            border-radius: 8px;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px dashed #333;
            padding-bottom: 15px;
            margin-bottom: 15px;
        }
        
        .logo {
            max-width: 80px;
            height: auto;
            margin-bottom: 10px;
        }
        
        .restaurant-name {
            font-size: 18px;
            font-weight: bold;
            margin: 5px 0;
        }
        
        .restaurant-info {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }
        
        .ticket-info {
            margin: 15px 0;
            font-size: 12px;
        }
        
        .ticket-info div {
            margin: 3px 0;
        }
        
        .products {
            border-top: 1px dashed #333;
            border-bottom: 1px dashed #333;
            padding: 10px 0;
            margin: 15px 0;
        }
        
        .product {
            margin: 8px 0;
            font-size: 12px;
        }
        
        .product-line {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }
        
        .product-name {
            font-weight: bold;
            flex: 1;
        }
        
        .product-price {
            margin-left: 10px;
            white-space: nowrap;
        }
        
        .product-details {
            font-size: 11px;
            color: #666;
            margin-top: 2px;
        }
        
        .product-notes {
            font-size: 11px;
            color: #888;
            font-style: italic;
            margin-top: 2px;
        }
        
        .totals {
            margin: 15px 0;
            font-size: 12px;
        }
        
        .total-line {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }
        
        .total-line.final {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #333;
            padding-top: 5px;
            margin-top: 10px;
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 2px dashed #333;
        }
        
        .thank-you {
            font-size: 11px;
            color: #666;
            line-height: 1.4;
            margin: 10px 0;
        }
        
        .signature-area {
            margin-top: 20px;
            padding: 15px 0;
            border-top: 1px dashed #333;
        }
        
        .signature-line {
            border-bottom: 1px solid #333;
            height: 30px;
            margin: 10px 0;
        }
        
        .signature-label {
            font-size: 10px;
            color: #666;
            text-align: center;
        }
        
        .actions {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 5px;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        
        .error {
            text-align: center;
            padding: 50px;
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="actions no-print">
        <button class="btn" onclick="window.print()">
            🖨️ Imprimir Ticket
        </button>
        <button class="btn btn-secondary" onclick="window.close()">
            ❌ Cerrar
        </button>
    </div>

    <div id="ticketContent">
        <div class="loading">
            <div>⏳ Cargando ticket...</div>
        </div>
    </div>

    <script>
        // Obtener ID de cuenta de la URL
        const urlParams = new URLSearchParams(window.location.search);
        const cuentaId = urlParams.get('cuenta_id');
        const autoPrint = urlParams.get('auto_print') === '1';

        if (!cuentaId) {
            document.getElementById('ticketContent').innerHTML = `
                <div class="error">
                    <div>❌ Error: ID de cuenta no proporcionado</div>
                </div>
            `;
        } else {
            cargarTicket(cuentaId);
        }
        
        function cargarTicket(cuentaId) {
            fetch(`/Restaurante/api/generar_ticket.php?cuenta_id=${cuentaId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        mostrarTicket(data.ticket);

                        // Auto-imprimir si se especifica
                        if (autoPrint) {
                            setTimeout(() => {
                                window.print();
                            }, 1000);
                        }
                    } else {
                        document.getElementById('ticketContent').innerHTML = `
                            <div class="error">
                                <div>❌ Error: ${data.message}</div>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('ticketContent').innerHTML = `
                        <div class="error">
                            <div>❌ Error de conexión</div>
                        </div>
                    `;
                });
        }
        
        function mostrarTicket(ticket) {
            const fecha = new Date(ticket.cuenta.fecha_creacion);
            const fechaFormateada = fecha.toLocaleDateString('es-GT') + ' ' + fecha.toLocaleTimeString('es-GT');
            
            let productosHtml = '';
            ticket.productos.forEach(producto => {
                productosHtml += `
                    <div class="product">
                        <div class="product-line">
                            <div class="product-name">${producto.nombre}</div>
                            <div class="product-price">${ticket.totales.moneda} ${parseFloat(producto.subtotal).toFixed(2)}</div>
                        </div>
                        <div class="product-details">
                            ${producto.cantidad} x ${ticket.totales.moneda} ${parseFloat(producto.precio_unitario).toFixed(2)}
                        </div>
                        ${producto.notas ? `<div class="product-notes">Nota: ${producto.notas}</div>` : ''}
                    </div>
                `;
            });
            
            document.getElementById('ticketContent').innerHTML = `
                <div class="ticket">
                    <div class="header">
                        ${ticket.restaurante.logo ? `<img src="${ticket.restaurante.logo}" alt="Logo" class="logo">` : ''}
                        <div class="restaurant-name">${ticket.restaurante.nombre}</div>
                        <div class="restaurant-info">
                            ${ticket.restaurante.direccion}<br>
                            Tel: ${ticket.restaurante.telefono}
                        </div>
                    </div>
                    
                    <div class="ticket-info">
                        <div><strong>Fecha:</strong> ${fechaFormateada}</div>
                        <div><strong>Cuenta #:</strong> ${ticket.cuenta.id}</div>
                        <div><strong>Mesa:</strong> ${ticket.cuenta.numero_mesa}</div>
                        <div><strong>Cliente:</strong> ${ticket.cuenta.nombre_cliente}</div>
                        <div><strong>Mesero:</strong> ${ticket.cuenta.mesero_nombre}</div>
                    </div>
                    
                    <div class="products">
                        ${productosHtml}
                    </div>
                    
                    <div class="totals">
                        <div class="total-line">
                            <span>Subtotal:</span>
                            <span>${ticket.totales.moneda} ${parseFloat(ticket.totales.subtotal).toFixed(2)}</span>
                        </div>
                        <div class="total-line">
                            <span>Propina Sugerida (${ticket.totales.propina_porcentaje}%):</span>
                            <span>${ticket.totales.moneda} ${parseFloat(ticket.totales.propina_monto).toFixed(2)}</span>
                        </div>
                        ${ticket.totales.descuento_monto > 0 ? `
                        <div class="total-line">
                            <span>Descuento${ticket.totales.descuento_motivo ? ' (' + ticket.totales.descuento_motivo + ')' : ''}:</span>
                            <span>-${ticket.totales.moneda} ${parseFloat(ticket.totales.descuento_monto).toFixed(2)}</span>
                        </div>
                        ` : ''}
                        <div class="total-line final">
                            <span>Total Final:</span>
                            <span>${ticket.totales.moneda} ${parseFloat(ticket.totales.total_final).toFixed(2)}</span>
                        </div>
                    </div>
                    
                    <div class="footer">
                        <div class="thank-you">
                            ${ticket.mensaje_cortesia}
                        </div>
                        
                        <div class="signature-area">
                            <div style="font-size: 11px; margin-bottom: 10px;">
                                <strong>Área para Firma (Pago con Tarjeta):</strong>
                            </div>
                            <div class="signature-line"></div>
                            <div class="signature-label">Firma del Cliente</div>
                        </div>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
