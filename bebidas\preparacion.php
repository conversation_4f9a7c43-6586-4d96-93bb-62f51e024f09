<?php
// Configurar variables para el layout
$page_title = 'Bebidas en Preparación - Bar';
$breadcrumbs = [
    ['title' => 'Inicio', 'url' => '/Restaurante/'],
    ['title' => 'Bebidas', 'url' => '/Restaurante/bebidas/'],
    ['title' => 'En Preparación']
];

// Iniciar el buffer de contenido
ob_start();

// Conectar a la base de datos
session_start();
require_once '../config/db.php';

// Verificar que sea personal de bebidas
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'bebidas'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    echo '<div class="alert alert-danger">Acceso denegado. Solo personal de bebidas puede acceder.</div>';
    $content = ob_get_clean();
    include '../includes/layout.php';
    exit();
}

// Obtener bebidas en preparación
try {
    $stmt = $pdo->query("
        SELECT d.*, o.mesa, o.fecha_hora, o.hash_mesa, p.nombre as producto_nombre,
               p.descripcion, u.nombre as mesero_nombre, m.numero_mesa,
               TIMESTAMPDIFF(MINUTE, d.fecha_actualizacion, NOW()) as tiempo_preparacion
        FROM detalle_orden d
        INNER JOIN ordenes o ON d.orden_id = o.id
        INNER JOIN productos p ON d.producto_id = p.id
        INNER JOIN usuarios u ON o.mesero_id = u.id
        INNER JOIN mesas m ON o.hash_mesa = m.hash_mesa
        WHERE d.area = 'bebidas' AND d.estado = 'preparando'
        ORDER BY d.fecha_actualizacion ASC
    ");
    $bebidas_preparando = $stmt->fetchAll();
    
    // Obtener estadísticas
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_preparando,
            AVG(TIMESTAMPDIFF(MINUTE, fecha_actualizacion, NOW())) as tiempo_promedio
        FROM detalle_orden 
        WHERE area = 'bebidas' AND estado = 'preparando'
    ");
    $estadisticas = $stmt->fetch();
    
} catch (PDOException $e) {
    $bebidas_preparando = [];
    $estadisticas = ['total_preparando' => 0, 'tiempo_promedio' => 0];
}
?>

<!-- Estadísticas de preparación -->
<div class="row g-4 mb-4">
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4 text-center">
                <div class="bg-info bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                    <i class="bi bi-cup-straw text-info fs-2"></i>
                </div>
                <h3 class="text-info mb-1"><?= count($bebidas_preparando) ?></h3>
                <p class="text-muted mb-0">Bebidas en Preparación</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4 text-center">
                <div class="bg-preparando bg-opacity-20 rounded-3 p-3 mb-3 d-inline-block">
                    <i class="bi bi-clock text-preparando fs-2"></i>
                </div>
                <h3 class="text-preparando mb-1"><?= round($estadisticas['tiempo_promedio'] ?? 0) ?> min</h3>
                <p class="text-muted mb-0">Tiempo Promedio</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4 text-center">
                <div class="bg-success bg-opacity-10 rounded-3 p-3 mb-3 d-inline-block">
                    <i class="bi bi-speedometer text-success fs-2"></i>
                </div>
                <h3 class="text-success mb-1">
                    <?php 
                    // Para bebidas, el tiempo ideal es menor (5 min vs 15 min para cocina)
                    $eficiencia = count($bebidas_preparando) > 0 ? min(100, max(0, 100 - ($estadisticas['tiempo_promedio'] ?? 0) * 5)) : 100;
                    echo round($eficiencia);
                    ?>%
                </h3>
                <p class="text-muted mb-0">Eficiencia</p>
            </div>
        </div>
    </div>
</div>

<!-- Navegación rápida -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-cup-straw me-2"></i>
                        Bebidas en Preparación
                    </h5>
                    <div class="d-flex gap-2">
                        <a href="/Restaurante/bebidas/ordenes.php" class="btn btn-outline-primary rounded-pill">
                            <i class="bi bi-list me-2"></i>
                            Ver Pendientes
                        </a>
                        <button class="btn btn-primary rounded-pill" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise me-2"></i>
                            Actualizar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Lista de bebidas en preparación -->
<div class="row g-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-0">
                <?php if (empty($bebidas_preparando)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-check-circle text-success fs-1 mb-3 d-block"></i>
                        <h5 class="text-muted">¡Excelente trabajo!</h5>
                        <p class="text-muted">No hay bebidas en preparación en este momento</p>
                        <a href="/Restaurante/bebidas/ordenes.php" class="btn btn-primary rounded-pill">
                            <i class="bi bi-list me-2"></i>
                            Ver Órdenes Pendientes
                        </a>
                    </div>
                <?php else: ?>
                    <div class="row g-0">
                        <?php foreach ($bebidas_preparando as $bebida): ?>
                            <div class="col-md-6 col-lg-4">
                                <div class="card border-0 border-end border-bottom h-100">
                                    <div class="card-body p-4">
                                        <!-- Header con mesa y tiempo -->
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <div>
                                                <h6 class="mb-1">
                                                    <i class="bi bi-table me-1"></i>
                                                    Mesa #<?= $bebida['numero_mesa'] ?>
                                                </h6>
                                                <small class="text-muted">
                                                    <i class="bi bi-person me-1"></i>
                                                    <?= htmlspecialchars($bebida['mesero_nombre']) ?>
                                                </small>
                                            </div>
                                            <div class="text-end">
                                                <?php
                                                $tiempo = $bebida['tiempo_preparacion'];
                                                // Para bebidas: >8 min = crítico, >5 min = advertencia
                                                $color_tiempo = $tiempo > 8 ? 'danger' : ($tiempo > 5 ? 'dark' : 'success');
                                                ?>
                                                <span class="badge bg-<?= $color_tiempo ?> rounded-pill">
                                                    <i class="bi bi-clock me-1"></i>
                                                    <?= $tiempo ?> min
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <!-- Producto -->
                                        <div class="mb-3">
                                            <h5 class="mb-2"><?= htmlspecialchars($bebida['producto_nombre']) ?></h5>
                                            <?php if (!empty($bebida['descripcion'])): ?>
                                                <p class="text-muted small mb-2"><?= htmlspecialchars($bebida['descripcion']) ?></p>
                                            <?php endif; ?>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-primary rounded-pill me-2">
                                                    Cantidad: <?= $bebida['cantidad'] ?>
                                                </span>
                                                <span class="badge bg-info rounded-pill">
                                                    <i class="bi bi-cup-straw me-1"></i>
                                                    Preparando
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <!-- Notas especiales -->
                                        <?php if (!empty($bebida['notas'])): ?>
                                            <div class="mb-3">
                                                <div class="alert alert-info py-2 px-3 mb-0">
                                                    <small>
                                                        <i class="bi bi-chat-left-text me-1"></i>
                                                        <strong>Notas:</strong> <?= htmlspecialchars($bebida['notas']) ?>
                                                    </small>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <!-- Información de tiempo -->
                                        <div class="mb-3">
                                            <small class="text-muted">
                                                <i class="bi bi-clock-history me-1"></i>
                                                Iniciado: <?= date('H:i', strtotime($bebida['fecha_actualizacion'])) ?>
                                            </small>
                                        </div>
                                        
                                        <!-- Botón de acción -->
                                        <div class="d-grid">
                                            <button class="btn btn-success rounded-pill marcar-listo-btn" 
                                                    data-detalle-id="<?= $bebida['id'] ?>"
                                                    data-producto="<?= htmlspecialchars($bebida['producto_nombre']) ?>"
                                                    data-mesa="<?= $bebida['numero_mesa'] ?>">
                                                <i class="bi bi-check-circle me-2"></i>
                                                Marcar como Lista
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// CSS adicional
$extra_css = '
<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.badge {
    font-size: 0.75rem;
}

.alert-info {
    background-color: #e7f3ff;
    border-color: #b8daff;
    color: #0c5460;
}

.marcar-listo-btn {
    transition: all 0.3s ease;
}

.marcar-listo-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(25, 135, 84, 0.3);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.tiempo-critico {
    animation: pulse 2s infinite;
}

/* Estilos específicos para bebidas */
.bg-info.bg-opacity-10 {
    background-color: rgba(13, 202, 240, 0.1) !important;
}

.text-info {
    color: #0dcaf0 !important;
}
</style>
';

// JavaScript adicional
$extra_js = '
<script>
document.addEventListener("DOMContentLoaded", function() {
    // Auto-refresh cada 20 segundos (más frecuente para bebidas)
    setInterval(function() {
        location.reload();
    }, 20000);
    
    // Marcar como listo
    document.querySelectorAll(".marcar-listo-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const detalleId = this.dataset.detalleId;
            const producto = this.dataset.producto;
            const mesa = this.dataset.mesa;
            
            if (confirm(`¿Marcar como lista: ${producto} para Mesa #${mesa}?`)) {
                cambiarEstadoDetalle(detalleId, "listo");
            }
        });
    });
    
    // Función para cambiar estado
    function cambiarEstadoDetalle(detalleId, nuevoEstado) {
        fetch("/Restaurante/api/cambiar_estado_detalle.php", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                detalle_id: detalleId,
                nuevo_estado: nuevoEstado
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Mostrar notificación de éxito
                showToast("Bebida marcada como lista", "success");
                
                // Recargar la página después de un breve delay
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showToast("Error: " + data.message, "error");
            }
        })
        .catch(error => {
            console.error("Error:", error);
            showToast("Error de conexión", "error");
        });
    }
    
    // Función para mostrar notificaciones
    function showToast(message, type = "info") {
        // Crear elemento toast
        const toast = document.createElement("div");
        toast.className = `alert alert-${type === "success" ? "success" : "danger"} position-fixed`;
        toast.style.cssText = "top: 20px; right: 20px; z-index: 9999; min-width: 300px;";
        toast.innerHTML = `
            <i class="bi bi-${type === "success" ? "check-circle" : "exclamation-triangle"} me-2"></i>
            ${message}
        `;
        
        document.body.appendChild(toast);
        
        // Remover después de 3 segundos
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
    
    // Agregar animación a bebidas con tiempo crítico
    document.querySelectorAll(".badge.bg-danger").forEach(badge => {
        badge.closest(".card").classList.add("tiempo-critico");
    });
});

// Hacer la función global para uso en otros scripts
window.cambiarEstadoDetalle = function(detalleId, nuevoEstado) {
    fetch("/Restaurante/api/cambiar_estado_detalle.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify({
            detalle_id: detalleId,
            nuevo_estado: nuevoEstado
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast("Estado actualizado correctamente", "success");
            setTimeout(() => location.reload(), 1500);
        } else {
            showToast("Error: " + data.message, "error");
        }
    })
    .catch(error => {
        console.error("Error:", error);
        showToast("Error de conexión", "error");
    });
};
</script>
';

// Incluir el layout
include '../includes/layout.php';
?>
