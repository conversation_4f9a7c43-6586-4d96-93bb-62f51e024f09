<?php
// Página de ajustes de cuenta (propina y descuentos)
session_start();
require_once '../config/db.php';

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    header('Location: /Restaurante/auth/login.php');
    exit();
}

// Verificar que sea mesero o admin
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol IN ('mesero', 'admin')");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    die('Acceso denegado. Solo meseros y administradores pueden acceder.');
}

// Obtener ID de cuenta
$cuenta_id = $_GET['cuenta_id'] ?? null;

if (!$cuenta_id) {
    die('ID de cuenta requerido');
}

// Obtener información de la cuenta
try {
    $stmt = $pdo->prepare("
        SELECT c.*, u.nombre as mesero_nombre, m.numero_mesa
        FROM cuentas c
        LEFT JOIN usuarios u ON c.mesero_id = u.id
        LEFT JOIN mesas m ON c.hash_mesa = m.hash_mesa
        WHERE c.id = ?
    ");
    $stmt->execute([$cuenta_id]);
    $cuenta = $stmt->fetch();
    
    if (!$cuenta) {
        die('Cuenta no encontrada');
    }
    
    // Calcular subtotal actual de las órdenes
    $stmt = $pdo->prepare("
        SELECT COALESCE(SUM(d.subtotal), 0) as subtotal_ordenes
        FROM detalle_orden d
        INNER JOIN ordenes o ON d.orden_id = o.id
        WHERE o.cuenta_id = ?
    ");
    $stmt->execute([$cuenta_id]);
    $subtotal_actual = $stmt->fetch()['subtotal_ordenes'];
    
    // Si el subtotal en la cuenta es 0, usar el calculado
    if ($cuenta['subtotal'] == 0) {
        $cuenta['subtotal'] = $subtotal_actual;
    }
    
} catch (PDOException $e) {
    die('Error al obtener información de la cuenta: ' . $e->getMessage());
}

$page_title = 'Propina de Cuenta';
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body style="font-family: 'Inter', sans-serif; background: #f8f9fa;">

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-xl-6">
            <!-- Header -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0">
                                <i class="bi bi-calculator me-2"></i>
                                Propina de Cuenta
                            </h5>
                            <small>Mesa #<?= $cuenta['numero_mesa'] ?> - <?= htmlspecialchars($cuenta['nombre_cliente']) ?></small>
                        </div>
                        <button class="btn btn-outline-light btn-sm" onclick="window.close()">
                            <i class="bi bi-x-lg"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="border-end">
                                <h6 class="text-muted mb-1">Subtotal</h6>
                                <h4 class="mb-0" id="subtotal-display">Q<?= number_format($cuenta['subtotal'], 2) ?></h4>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border-end">
                                <h6 class="text-muted mb-1">Propina</h6>
                                <h4 class="mb-0" id="ajustes-display">Q0.00</h4>
                            </div>
                        </div>
                        <div class="col-4">
                            <h6 class="text-muted mb-1">Total Final</h6>
                            <h4 class="mb-0 text-success" id="total-final-display">Q<?= number_format($cuenta['subtotal'], 2) ?></h4>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Propina -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="bi bi-heart me-2 text-success"></i>
                        Propina
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-2 mb-3">
                        <div class="col-3">
                            <button class="btn btn-outline-success w-100 propina-btn" data-porcentaje="0">
                                0%
                            </button>
                        </div>
                        <div class="col-3">
                            <button class="btn btn-outline-success w-100 propina-btn" data-porcentaje="5">
                                5%
                            </button>
                        </div>
                        <div class="col-3">
                            <button class="btn btn-outline-success w-100 propina-btn active" data-porcentaje="10">
                                10%
                            </button>
                        </div>
                        <div class="col-3">
                            <button class="btn btn-outline-success w-100 propina-btn" data-porcentaje="15">
                                15%
                            </button>
                        </div>
                    </div>
                    <div class="row g-2 mb-3">
                        <div class="col-3">
                            <button class="btn btn-outline-success w-100 propina-btn" data-porcentaje="20">
                                20%
                            </button>
                        </div>
                        <div class="col-9">
                            <div class="input-group">
                                <span class="input-group-text">Personalizada</span>
                                <input type="number" class="form-control" id="propina-personalizada" 
                                       placeholder="%" min="0" max="100" step="0.1">
                                <button class="btn btn-success" type="button" onclick="aplicarPropinaPersonalizada()">
                                    Aplicar
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Propina seleccionada:</span>
                        <span class="fw-bold text-success" id="propina-info">10% = Q<?= number_format($cuenta['subtotal'] * 0.10, 2) ?></span>
                    </div>
                </div>
            </div>

            <!-- Descuentos -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="bi bi-tag me-2 text-warning"></i>
                        Descuentos
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Tipo de descuento</label>
                            <select class="form-select" id="descuento-tipo">
                                <option value="ninguno">Sin descuento</option>
                                <option value="porcentaje">Porcentaje (%)</option>
                                <option value="monto_fijo">Monto fijo (Q)</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Valor</label>
                            <input type="number" class="form-control" id="descuento-valor" 
                                   placeholder="0" min="0" step="0.01" disabled>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Descuento</label>
                            <input type="text" class="form-control" id="descuento-calculado" 
                                   value="Q0.00" readonly>
                        </div>
                    </div>
                    <div class="mt-3">
                        <label class="form-label">Motivo del descuento (opcional)</label>
                        <input type="text" class="form-control" id="descuento-motivo" 
                               placeholder="Ej: Cliente frecuente, promoción especial...">
                    </div>
                </div>
            </div>

            <!-- Acciones -->
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button class="btn btn-outline-secondary" onclick="window.close()">
                            <i class="bi bi-x-circle me-2"></i>
                            Cancelar
                        </button>
                        <button class="btn btn-primary" onclick="guardarAjustes()">
                            <i class="bi bi-check-circle me-2"></i>
                            Aplicar Propina
                        </button>
                        <button class="btn btn-success" onclick="guardarYPagar()">
                            <i class="bi bi-credit-card me-2"></i>
                            Aplicar y Pagar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Variables globales
const cuentaId = <?= $cuenta_id ?>;
const subtotal = <?= $cuenta['subtotal'] ?>;
let propinaActual = 10; // Porcentaje por defecto
let descuentoActual = 0;
let descuentoTipo = 'ninguno';

// Inicializar
document.addEventListener('DOMContentLoaded', function() {
    calcularTotales();

    // Event listeners para propina
    document.querySelectorAll('.propina-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            // Remover active de todos
            document.querySelectorAll('.propina-btn').forEach(b => b.classList.remove('active'));
            // Agregar active al clickeado
            this.classList.add('active');

            propinaActual = parseFloat(this.dataset.porcentaje);
            document.getElementById('propina-personalizada').value = '';
            calcularTotales();
        });
    });

    // Event listeners para descuento
    document.getElementById('descuento-tipo').addEventListener('change', function() {
        const valor = this.value;
        const inputValor = document.getElementById('descuento-valor');

        if (valor === 'ninguno') {
            inputValor.disabled = true;
            inputValor.value = '';
            descuentoTipo = 'ninguno';
            descuentoActual = 0;
        } else {
            inputValor.disabled = false;
            descuentoTipo = valor;
        }
        calcularTotales();
    });

    document.getElementById('descuento-valor').addEventListener('input', function() {
        const valor = parseFloat(this.value) || 0;

        if (descuentoTipo === 'porcentaje') {
            descuentoActual = (subtotal * valor) / 100;
        } else if (descuentoTipo === 'monto_fijo') {
            descuentoActual = valor;
        }

        calcularTotales();
    });
});

function aplicarPropinaPersonalizada() {
    const valor = parseFloat(document.getElementById('propina-personalizada').value);

    if (isNaN(valor) || valor < 0 || valor > 100) {
        showToast('Por favor ingresa un porcentaje válido entre 0 y 100', 'warning');
        return;
    }

    // Remover active de todos los botones
    document.querySelectorAll('.propina-btn').forEach(b => b.classList.remove('active'));

    propinaActual = valor;
    calcularTotales();
}

function calcularTotales() {
    // Calcular propina
    const propinaMonto = (subtotal * propinaActual) / 100;

    // Validar que el descuento no sea mayor al subtotal
    if (descuentoActual > subtotal) {
        descuentoActual = subtotal;
        if (descuentoTipo === 'monto_fijo') {
            document.getElementById('descuento-valor').value = subtotal.toFixed(2);
        }
    }

    // Calcular total final
    const totalFinal = subtotal + propinaMonto - descuentoActual;

    // Actualizar displays
    document.getElementById('propina-info').textContent =
        `${propinaActual}% = Q${propinaMonto.toFixed(2)}`;

    document.getElementById('descuento-calculado').value =
        `Q${descuentoActual.toFixed(2)}`;

    const ajustesTotal = propinaMonto - descuentoActual;
    document.getElementById('ajustes-display').textContent =
        `Q${ajustesTotal >= 0 ? '+' : ''}${ajustesTotal.toFixed(2)}`;

    document.getElementById('total-final-display').textContent =
        `Q${totalFinal.toFixed(2)}`;
}

function guardarAjustes() {
    const datos = {
        cuenta_id: cuentaId,
        propina_porcentaje: propinaActual,
        propina_monto: (subtotal * propinaActual) / 100,
        descuento_tipo: descuentoTipo,
        descuento_valor: descuentoTipo === 'ninguno' ? 0 : parseFloat(document.getElementById('descuento-valor').value) || 0,
        descuento_monto: descuentoActual,
        descuento_motivo: document.getElementById('descuento-motivo').value.trim(),
        subtotal: subtotal,
        total_final: subtotal + ((subtotal * propinaActual) / 100) - descuentoActual
    };

    fetch('/Restaurante/api/aplicar_ajustes_cuenta.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(datos)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Ajustes aplicados correctamente', 'success');
            setTimeout(() => {
                if (window.opener) {
                    window.opener.location.reload();
                }
                window.close();
            }, 1500);
        } else {
            showToast('Error: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Error de conexión', 'error');
    });
}

function guardarYPagar() {
    // Primero guardar ajustes, luego proceder al pago
    const datos = {
        cuenta_id: cuentaId,
        propina_porcentaje: propinaActual,
        propina_monto: (subtotal * propinaActual) / 100,
        descuento_tipo: descuentoTipo,
        descuento_valor: descuentoTipo === 'ninguno' ? 0 : parseFloat(document.getElementById('descuento-valor').value) || 0,
        descuento_monto: descuentoActual,
        descuento_motivo: document.getElementById('descuento-motivo').value.trim(),
        subtotal: subtotal,
        total_final: subtotal + ((subtotal * propinaActual) / 100) - descuentoActual,
        procesar_pago: true
    };

    fetch('/Restaurante/api/aplicar_ajustes_cuenta.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(datos)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Generar ticket automáticamente después del pago
            generarTicketAutomatico(cuentaId);

            // Mostrar mensaje de éxito
            showToast(`Cuenta pagada exitosamente. Total: Q${data.total_final.toFixed(2)}`, "success");

            // Cerrar ventana después de un breve delay
            setTimeout(() => {
                if (window.opener) {
                    window.opener.postMessage({
                        type: 'payment_success',
                        message: `Cuenta pagada exitosamente. Total: Q${data.total_final.toFixed(2)}`
                    }, '*');
                    window.opener.location.reload();
                }
                window.close();
            }, 2000);
        } else {
            // Mostrar error en la misma ventana
            document.body.insertAdjacentHTML('beforeend', `
                <div class="alert alert-danger alert-dismissible fade show position-fixed"
                     style="top: 20px; right: 20px; z-index: 9999;">
                    <strong>Error:</strong> ${data.message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `);
        }
    })
    .catch(error => {
        document.body.insertAdjacentHTML('beforeend', `
            <div class="alert alert-danger alert-dismissible fade show position-fixed"
                 style="top: 20px; right: 20px; z-index: 9999;">
                <strong>Error de conexión</strong>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);
    });
}

// Función para mostrar notificaciones toast
function showToast(message, type = 'info') {
    // Crear contenedor de toasts si no existe
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            display: flex;
            flex-direction: column;
            gap: 10px;
        `;
        document.body.appendChild(toastContainer);
    }

    // Crear elemento toast
    const toast = document.createElement('div');
    const bgColor = type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : type === 'warning' ? '#fff3cd' : '#d1ecf1';
    const textColor = type === 'success' ? '#155724' : type === 'error' ? '#721c24' : type === 'warning' ? '#856404' : '#0c5460';
    const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';

    toast.style.cssText = `
        background: ${bgColor};
        color: ${textColor};
        padding: 12px 20px;
        border-radius: 8px;
        border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : type === 'warning' ? '#ffeaa7' : '#bee5eb'};
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        animation: slideInRight 0.3s ease-out;
        font-weight: 500;
    `;

    toast.innerHTML = `${icon} ${message}`;

    // Agregar animación CSS
    if (!document.getElementById('toast-animations')) {
        const style = document.createElement('style');
        style.id = 'toast-animations';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    toastContainer.appendChild(toast);

    // Remover después de 4 segundos
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 4000);
}

// Función para generar ticket automáticamente después del pago
function generarTicketAutomatico(cuentaId) {
    // Abrir ticket en nueva ventana
    const ticketUrl = "/Restaurante/imprimir_ticket.php?cuenta_id=" + cuentaId + "&auto_print=1";
    const ticketWindow = window.open(ticketUrl, "ticket", "width=400,height=600,scrollbars=yes,resizable=yes");

    if (!ticketWindow) {
        showToast("Por favor, permita las ventanas emergentes para generar el ticket", "warning");
    } else {
        showToast("Generando ticket para imprimir...", "info");
    }
}
</script>

</body>
</html>
