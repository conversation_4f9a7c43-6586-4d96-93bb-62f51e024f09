<?php
// Habilitar reporte de errores para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Verificar si los archivos existen antes de incluirlos
if (!file_exists('../config/db.php')) {
    echo json_encode(['success' => false, 'message' => 'Error: archivo db.php no encontrado']);
    exit();
}

if (!file_exists('../includes/inventario_helper.php')) {
    echo json_encode(['success' => false, 'message' => 'Error: archivo inventario_helper.php no encontrado']);
    exit();
}

require_once '../config/db.php';
require_once '../includes/inventario_helper.php';

header('Content-Type: application/json');

// Función para logging de errores
function logError($message, $data = null) {
    $log = date('Y-m-d H:i:s') . " - " . $message;
    if ($data) {
        $log .= " - Data: " . json_encode($data);
    }

    // Intentar crear el directorio si no existe
    $logDir = '../logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }

    // Escribir al log
    $logFile = $logDir . '/compras_error.log';
    file_put_contents($logFile, $log . "\n", FILE_APPEND | LOCK_EX);
}

// Verificar conexión a la base de datos
if (!isset($pdo)) {
    echo json_encode(['success' => false, 'message' => 'Error: No hay conexión a la base de datos']);
    exit();
}

// Verificar que sea administrador
if (!isset($_SESSION['usuario_id'])) {
    echo json_encode(['success' => false, 'message' => 'No autorizado - No hay sesión activa']);
    exit();
}

try {
    $stmt = $pdo->prepare("SELECT rol FROM usuarios WHERE id = ?");
    $stmt->execute([$_SESSION['usuario_id']]);
    $usuario = $stmt->fetch();
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
    exit();
}

if (!$usuario || $usuario['rol'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden registrar compras']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit();
}

try {
    // Log de datos recibidos
    logError("Datos recibidos", $_POST);

    // Validar datos requeridos
    $proveedor_id = $_POST['proveedor_id'] ?? '';
    $fecha_compra = $_POST['fecha_compra'] ?? '';
    $numero_factura = $_POST['numero_factura'] ?? '';
    $productos = $_POST['productos'] ?? [];

    logError("Datos procesados", [
        'proveedor_id' => $proveedor_id,
        'fecha_compra' => $fecha_compra,
        'numero_factura' => $numero_factura,
        'productos_count' => count($productos)
    ]);

    if (empty($proveedor_id) || empty($fecha_compra) || empty($productos)) {
        throw new Exception('Faltan datos requeridos');
    }

    // Validar que hay al menos un producto
    $productos_validos = array_filter($productos, function($producto) {
        return !empty($producto['producto_id']) && 
               !empty($producto['cantidad']) && 
               !empty($producto['costo_unitario']);
    });

    if (empty($productos_validos)) {
        throw new Exception('Debe agregar al menos un producto válido');
    }

    // Iniciar transacción
    $pdo->beginTransaction();

    // Calcular total de la compra
    $total_compra = 0;
    foreach ($productos_validos as $producto) {
        $total_compra += $producto['cantidad'] * $producto['costo_unitario'];
    }

    // Insertar compra principal
    $stmt = $pdo->prepare("
        INSERT INTO compras (proveedor_id, fecha_compra, numero_factura, total, usuario_id)
        VALUES (?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        $proveedor_id,
        $fecha_compra,
        $numero_factura ?: null,
        $total_compra,
        $_SESSION['usuario_id']
    ]);

    $compra_id = $pdo->lastInsertId();

    // Insertar detalles de la compra y actualizar inventario
    foreach ($productos_validos as $producto) {
        $producto_id = $producto['producto_id'];
        $cantidad = (int) $producto['cantidad'];
        $costo_unitario = (float) $producto['costo_unitario'];
        $subtotal = $cantidad * $costo_unitario;

        // Insertar detalle de compra
        $stmt = $pdo->prepare("
            INSERT INTO detalle_compras (compra_id, producto_id, cantidad, precio_unitario, subtotal)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$compra_id, $producto_id, $cantidad, $costo_unitario, $subtotal]);

        // Actualizar inventario usando la función helper
        $resultado = registrarMovimientoInventario(
            $pdo,
            $producto_id,
            'entrada',
            $cantidad,
            'compra',
            $compra_id,
            "Compra #$compra_id - Factura: " . ($numero_factura ?: 'Sin número'),
            $_SESSION['usuario_id'],
            $costo_unitario
        );

        if (!$resultado['success']) {
            throw new Exception("Error al actualizar inventario: " . $resultado['error']);
        }

        // Actualizar costo promedio en inventario
        $stmt = $pdo->prepare("
            UPDATE inventario 
            SET costo_promedio = (
                SELECT AVG(costo_unitario) 
                FROM movimientos_inventario 
                WHERE producto_id = ? AND tipo_movimiento = 'entrada'
            )
            WHERE producto_id = ?
        ");
        $stmt->execute([$producto_id, $producto_id]);
    }

    // Confirmar transacción
    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => 'Compra registrada exitosamente',
        'compra_id' => $compra_id,
        'total' => $total_compra
    ]);

} catch (Exception $e) {
    // Log del error
    logError("Error en procesar_compra", [
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);

    // Rollback en caso de error
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug' => [
            'file' => basename($e->getFile()),
            'line' => $e->getLine()
        ]
    ]);
}
?>
