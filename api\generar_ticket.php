<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}


// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();
require_once '../config/db.php';

// Verificar que el usuario esté autenticado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Usuario no autenticado']);
    exit();
}

// Obtener ID de la cuenta
$cuenta_id = $_GET['cuenta_id'] ?? '';

if (empty($cuenta_id) || !is_numeric($cuenta_id)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID de cuenta no válido']);
    exit();
}

try {
    // Primero verificar si existe la columna logo_restaurante
    $logo_column_exists = false;
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM configuracion LIKE 'logo_restaurante'");
        $logo_column_exists = $stmt->rowCount() > 0;
    } catch (Exception $e) {
        // Si hay error, asumir que no existe
        $logo_column_exists = false;
    }

    // Obtener información de la cuenta con datos del mesero y mesa
    $logo_field = $logo_column_exists ? ', conf.logo_restaurante' : '';
    $stmt = $pdo->prepare("
        SELECT c.*, u.nombre as mesero_nombre, m.numero_mesa,
               conf.nombre_restaurante, conf.direccion, conf.telefono, conf.moneda
               $logo_field
        FROM cuentas c
        INNER JOIN usuarios u ON c.mesero_id = u.id
        INNER JOIN mesas m ON c.hash_mesa = m.hash_mesa
        CROSS JOIN configuracion conf
        WHERE c.id = ? AND conf.id = 1
    ");
    $stmt->execute([$cuenta_id]);
    $cuenta = $stmt->fetch();
    
    if (!$cuenta) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Cuenta no encontrada']);
        exit();
    }
    
    // Obtener productos de la cuenta con detalles
    $stmt = $pdo->prepare("
        SELECT d.*, p.nombre as producto_nombre, p.descripcion as producto_descripcion,
               o.fecha_hora as orden_fecha
        FROM detalle_orden d
        INNER JOIN ordenes o ON d.orden_id = o.id
        INNER JOIN productos p ON d.producto_id = p.id
        WHERE o.cuenta_id = ?
        ORDER BY o.fecha_hora ASC, d.id ASC
    ");
    $stmt->execute([$cuenta_id]);
    $productos = $stmt->fetchAll();
    
    // Calcular totales
    $subtotal = 0;
    $productos_agrupados = [];

    foreach ($productos as $producto) {
        $key = $producto['producto_id'] . '_' . $producto['precio_unitario'];
        if (isset($productos_agrupados[$key])) {
            $productos_agrupados[$key]['cantidad'] += $producto['cantidad'];
            $productos_agrupados[$key]['subtotal'] += $producto['subtotal'];
        } else {
            $productos_agrupados[$key] = [
                'nombre' => $producto['producto_nombre'],
                'descripcion' => $producto['producto_descripcion'],
                'cantidad' => $producto['cantidad'],
                'precio_unitario' => $producto['precio_unitario'],
                'subtotal' => $producto['subtotal'],
                'notas' => $producto['notas']
            ];
        }
        $subtotal += $producto['subtotal'];
    }

    // Usar los ajustes guardados en la cuenta o calcular valores por defecto
    $subtotal_cuenta = ($cuenta['subtotal'] > 0) ? $cuenta['subtotal'] : $subtotal;
    $propina_porcentaje = $cuenta['propina_porcentaje'] ?? 10.00;

    // Si no hay propina personalizada guardada, calcular la propina sugerida
    if ($cuenta['propina_monto'] > 0) {
        $propina_monto = $cuenta['propina_monto'];
    } else {
        $propina_monto = $subtotal_cuenta * ($propina_porcentaje / 100);
    }

    $descuento_monto = $cuenta['descuento_monto'] ?? 0;
    $descuento_motivo = $cuenta['descuento_motivo'] ?? null;

    // Si no hay total final personalizado, calcularlo
    if ($cuenta['total_final'] > 0) {
        $total_final = $cuenta['total_final'];
    } else {
        $total_final = $subtotal_cuenta + $propina_monto - $descuento_monto;
    }
    
    // Obtener mensaje de cortesía aleatorio desde configuración
    $mensaje_cortesia = '';
    $stmt = $pdo->query("SELECT mensajes_cortesia FROM configuracion WHERE id = 1");
    $config_mensajes = $stmt->fetch();

    if ($config_mensajes && !empty($config_mensajes['mensajes_cortesia'])) {
        $mensajes_array = json_decode($config_mensajes['mensajes_cortesia'], true);
        if (is_array($mensajes_array) && !empty($mensajes_array)) {
            // Filtrar solo mensajes activos
            $mensajes_activos = array_filter($mensajes_array, function($mensaje) {
                return isset($mensaje['activo']) && $mensaje['activo'] === true;
            });

            if (!empty($mensajes_activos)) {
                // Seleccionar un mensaje aleatorio
                $mensaje_seleccionado = $mensajes_activos[array_rand($mensajes_activos)];
                $mensaje_cortesia = $mensaje_seleccionado['mensaje'];
            }
        }
    }

    // Mensaje por defecto si no hay mensajes configurados
    if (empty($mensaje_cortesia)) {
        $mensaje_cortesia = '¡Gracias por visitarnos! Esperamos que haya disfrutado de su experiencia gastronómica.';
    }
    
    // Preparar datos del ticket
    $ticket_data = [
        'success' => true,
        'ticket' => [
            'restaurante' => [
                'nombre' => $cuenta['nombre_restaurante'],
                'direccion' => $cuenta['direccion'],
                'telefono' => $cuenta['telefono'],
                'logo' => $logo_column_exists && isset($cuenta['logo_restaurante']) ? $cuenta['logo_restaurante'] : null
            ],
            'cuenta' => [
                'id' => $cuenta['id'],
                'numero_mesa' => $cuenta['numero_mesa'],
                'nombre_cliente' => trim($cuenta['nombre_cliente'] . ' ' . $cuenta['apellido_cliente']),
                'mesero_nombre' => $cuenta['mesero_nombre'],
                'fecha_creacion' => $cuenta['fecha_creacion'],
                'estado' => $cuenta['estado']
            ],
            'productos' => array_values($productos_agrupados),
            'totales' => [
                'subtotal' => $subtotal_cuenta,
                'propina_porcentaje' => $propina_porcentaje,
                'propina_monto' => $propina_monto,
                'descuento_monto' => $descuento_monto,
                'descuento_motivo' => $descuento_motivo,
                'total_final' => $total_final,
                'moneda' => $cuenta['moneda']
            ],
            'mensaje_cortesia' => $mensaje_cortesia,
            'fecha_generacion' => date('Y-m-d H:i:s')
        ]
    ];
    
    echo json_encode($ticket_data, JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno: ' . $e->getMessage()]);
}
?>
