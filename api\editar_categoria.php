<?php
header('Content-Type: application/json');

// Verificar que sea una petición POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit();
}

// Iniciar sesión y conectar a la base de datos

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();
require_once '../config/db.php';

// Verificar que el usuario esté logueado y sea administrador
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden editar categorías']);
    exit();
}

// Obtener datos del formulario
$categoria_id = $_POST['categoria_id'] ?? '';
$nombre = trim($_POST['nombre'] ?? '');
$descripcion = trim($_POST['descripcion'] ?? '');
$icono = $_POST['icono'] ?? 'bi-tag';
$color = $_POST['color'] ?? 'primary';

// Validaciones
if (empty($categoria_id) || !is_numeric($categoria_id)) {
    echo json_encode(['success' => false, 'message' => 'ID de categoría inválido']);
    exit();
}

if (empty($nombre)) {
    echo json_encode(['success' => false, 'message' => 'El nombre es requerido']);
    exit();
}

if (strlen($nombre) > 100) {
    echo json_encode(['success' => false, 'message' => 'El nombre no puede exceder 100 caracteres']);
    exit();
}

if (strlen($descripcion) > 500) {
    echo json_encode(['success' => false, 'message' => 'La descripción no puede exceder 500 caracteres']);
    exit();
}

// Validar que el icono y color sean válidos
$iconos_validos = [
    'bi-egg-fried', 'bi-cup-straw', 'bi-cake2', 'bi-bowl', 'bi-leaf', 
    'bi-pizza', 'bi-cup-hot', 'bi-droplet', 'bi-tag', 'bi-star', 'bi-heart', 'bi-fire'
];

$colores_validos = ['primary', 'success', 'danger', 'warning', 'info', 'secondary', 'dark'];

if (!in_array($icono, $iconos_validos)) {
    $icono = 'bi-tag';
}

if (!in_array($color, $colores_validos)) {
    $color = 'primary';
}

try {
    // Verificar que la categoría existe
    $stmt = $pdo->prepare("SELECT * FROM categorias WHERE id = ?");
    $stmt->execute([$categoria_id]);
    $categoria_actual = $stmt->fetch();
    
    if (!$categoria_actual) {
        echo json_encode(['success' => false, 'message' => 'Categoría no encontrada']);
        exit();
    }
    
    // Verificar que no exista otra categoría con el mismo nombre
    $stmt = $pdo->prepare("SELECT id FROM categorias WHERE nombre = ? AND id != ?");
    $stmt->execute([$nombre, $categoria_id]);
    
    if ($stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'Ya existe otra categoría con ese nombre']);
        exit();
    }
    
    // Actualizar categoría
    $stmt = $pdo->prepare("
        UPDATE categorias 
        SET nombre = ?, descripcion = ?, icono = ?, color = ?, fecha_actualizacion = NOW()
        WHERE id = ?
    ");
    
    $stmt->execute([$nombre, $descripcion, $icono, $color, $categoria_id]);
    
    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'message' => 'Categoría actualizada exitosamente'
    ]);
    
} catch (PDOException $e) {
    error_log("Error en editar_categoria.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error al actualizar la categoría: ' . $e->getMessage()
    ]);
}
?>
