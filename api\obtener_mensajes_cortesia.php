<?php
header('Content-Type: application/json');

// Verificar que sea una petición GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit();
}

require_once '../config/db.php';

try {
    // Obtener configuración con mensajes de cortesía
    $stmt = $pdo->query("SELECT mensajes_cortesia FROM configuracion WHERE id = 1");
    $config = $stmt->fetch();
    
    if (!$config) {
        echo json_encode([
            'success' => true,
            'mensajes' => [],
            'message' => 'No hay configuración disponible'
        ]);
        exit();
    }
    
    $mensajes = [];
    if (!empty($config['mensajes_cortesia'])) {
        $mensajes_json = json_decode($config['mensajes_cortesia'], true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($mensajes_json)) {
            $mensajes = $mensajes_json;
        }
    }
    
    // Filtrar parámetros opcionales
    $solo_activos = isset($_GET['activos']) && $_GET['activos'] === 'true';
    $limite = isset($_GET['limite']) ? intval($_GET['limite']) : null;
    $ordenar_por_prioridad = isset($_GET['ordenar']) && $_GET['ordenar'] === 'prioridad';
    
    // Aplicar filtros
    if ($solo_activos) {
        $mensajes = array_filter($mensajes, function($mensaje) {
            return isset($mensaje['activo']) && $mensaje['activo'] === true;
        });
    }
    
    // Ordenar por prioridad si se solicita
    if ($ordenar_por_prioridad) {
        usort($mensajes, function($a, $b) {
            $prioridadA = isset($a['prioridad']) ? $a['prioridad'] : 999;
            $prioridadB = isset($b['prioridad']) ? $b['prioridad'] : 999;
            return $prioridadA - $prioridadB;
        });
    }
    
    // Aplicar límite si se especifica
    if ($limite && $limite > 0) {
        $mensajes = array_slice($mensajes, 0, $limite);
    }
    
    // Reindexar array
    $mensajes = array_values($mensajes);
    
    echo json_encode([
        'success' => true,
        'mensajes' => $mensajes,
        'total' => count($mensajes),
        'filtros_aplicados' => [
            'solo_activos' => $solo_activos,
            'limite' => $limite,
            'ordenado_por_prioridad' => $ordenar_por_prioridad
        ]
    ]);
    
} catch (PDOException $e) {
    error_log("Error en obtener_mensajes_cortesia.php: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'Error de base de datos: ' . $e->getMessage()
    ]);
    
} catch (Exception $e) {
    error_log("Error en obtener_mensajes_cortesia.php: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => $e->getMessage()
    ]);
}
?>
