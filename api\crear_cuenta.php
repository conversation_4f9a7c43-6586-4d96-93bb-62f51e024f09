<?php
header('Content-Type: application/json');
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Verificar que sea un mesero o administrador
require_once '../config/db.php';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol IN ('mesero', 'admin')");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo meseros y administradores pueden crear cuentas']);
    exit();
}

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);
$hash_mesa = $input['hash_mesa'] ?? '';
$nombre_cliente = trim($input['nombre_cliente'] ?? 'Cliente');
$apellido_cliente = trim($input['apellido_cliente'] ?? '');

if (empty($hash_mesa)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Hash de mesa requerido']);
    exit();
}

if (empty($nombre_cliente)) {
    $nombre_cliente = 'Cliente';
}

try {
    // Verificar que la mesa existe y está ocupada
    $stmt = $pdo->prepare("SELECT * FROM mesas WHERE hash_mesa = ? AND estado = 'ocupada'");
    $stmt->execute([$hash_mesa]);
    $mesa = $stmt->fetch();
    
    if (!$mesa) {
        echo json_encode(['success' => false, 'message' => 'Mesa no encontrada o no está ocupada']);
        exit();
    }
    
    // Verificar que el mesero tenga acceso a esta mesa (si no es admin)
    if ($usuario['rol'] === 'mesero' && $mesa['mesero_id'] != $usuario['id']) {
        echo json_encode(['success' => false, 'message' => 'No tienes permisos para esta mesa']);
        exit();
    }
    
    // Crear nueva cuenta
    $stmt = $pdo->prepare("
        INSERT INTO cuentas (mesa, hash_mesa, mesero_id, nombre_cliente, apellido_cliente, estado) 
        VALUES (?, ?, ?, ?, ?, 'abierta')
    ");
    
    $stmt->execute([
        $mesa['numero_mesa'],
        $hash_mesa,
        $mesa['mesero_id'],
        $nombre_cliente,
        $apellido_cliente
    ]);
    
    $cuenta_id = $pdo->lastInsertId();
    
    // Obtener la cuenta creada con información completa
    $stmt = $pdo->prepare("
        SELECT c.*, u.nombre as mesero_nombre 
        FROM cuentas c 
        LEFT JOIN usuarios u ON c.mesero_id = u.id 
        WHERE c.id = ?
    ");
    $stmt->execute([$cuenta_id]);
    $cuenta = $stmt->fetch();
    
    echo json_encode([
        'success' => true,
        'message' => 'Cuenta creada exitosamente',
        'cuenta' => $cuenta
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
