<?php
// Configurar variables para el layout
$page_title = 'Gestión de Compras';
$breadcrumbs = [
    ['title' => 'Inicio', 'url' => url('')],
    ['title' => 'Administración', 'url' => url('admin/')],
    ['title' => 'Compras']
];

// Iniciar el buffer de contenido
ob_start();

// Conectar a la base de datos
require_once '../config/session_config.php';
session_start();
require_once '../config/db.php';

// Verificar que sea administrador
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    echo '<div class="alert alert-danger">Acceso denegado. Solo administradores pueden acceder.</div>';
    $content = ob_get_clean();
    include '../includes/layout.php';
    exit();
}

// Obtener estadísticas de compras
try {
    // Total de compras
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM compras");
    $total_compras = $stmt->fetch()['total'];
    
    // Compras del mes actual
    $stmt = $pdo->query("
        SELECT COUNT(*) as total 
        FROM compras 
        WHERE MONTH(fecha_compra) = MONTH(CURRENT_DATE()) 
        AND YEAR(fecha_compra) = YEAR(CURRENT_DATE())
    ");
    $compras_mes = $stmt->fetch()['total'];
    
    // Total gastado este mes
    $stmt = $pdo->query("
        SELECT SUM(total) as total_gastado 
        FROM compras 
        WHERE MONTH(fecha_compra) = MONTH(CURRENT_DATE()) 
        AND YEAR(fecha_compra) = YEAR(CURRENT_DATE())
    ");
    $total_gastado_mes = $stmt->fetch()['total_gastado'] ?? 0;
    
    // Compras pendientes
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM compras WHERE estado = 'pendiente'");
    $compras_pendientes = $stmt->fetch()['total'];
    
    // Obtener compras con información de proveedores y usuarios
    $stmt = $pdo->query("
        SELECT 
            c.*,
            p.nombre as proveedor_nombre,
            p.contacto as proveedor_contacto,
            u.nombre as usuario_nombre
        FROM compras c
        LEFT JOIN proveedores p ON c.proveedor_id = p.id
        LEFT JOIN usuarios u ON c.usuario_id = u.id
        ORDER BY c.fecha_compra DESC, c.id DESC
    ");
    $compras = $stmt->fetchAll();

} catch (PDOException $e) {
    $total_compras = 0;
    $compras_mes = 0;
    $total_gastado_mes = 0;
    $compras_pendientes = 0;
    $compras = [];
}
?>

<!-- Estadísticas de Compras -->
<div class="compras-estadisticas-container mb-4">
    <div class="compras-estadisticas-header">
        <div class="compras-estadisticas-title">
            <span class="compras-icon">🛒</span>
            <h4>Gestión de Compras</h4>
        </div>
        <div class="compras-acciones">
            <button class="btn-nueva-compra" data-bs-toggle="modal" data-bs-target="#nuevaCompraModal">
                ➕ Nueva Compra
            </button>
            <button class="btn-reporte-compras" onclick="generarReporteCompras()">
                📊 Generar Reporte
            </button>
        </div>
    </div>

    <div class="compras-estadisticas-grid">
        <div class="stat-card stat-total">
            <div class="stat-icon">🛒</div>
            <div class="stat-info">
                <span class="stat-numero"><?= $total_compras ?></span>
                <span class="stat-label">Total de Compras</span>
            </div>
        </div>

        <div class="stat-card stat-mes">
            <div class="stat-icon">📅</div>
            <div class="stat-info">
                <span class="stat-numero"><?= $compras_mes ?></span>
                <span class="stat-label">Compras este Mes</span>
            </div>
        </div>

        <div class="stat-card stat-pendientes">
            <div class="stat-icon">⏳</div>
            <div class="stat-info">
                <span class="stat-numero"><?= $compras_pendientes ?></span>
                <span class="stat-label">Pendientes</span>
            </div>
        </div>

        <div class="stat-card stat-gastado">
            <div class="stat-icon">💰</div>
            <div class="stat-info">
                <span class="stat-numero">Q<?= number_format($total_gastado_mes, 2) ?></span>
                <span class="stat-label">Gastado este Mes</span>
            </div>
        </div>
    </div>
</div>

<style>
/* Estilos para estadísticas de compras */
.compras-estadisticas-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.compras-estadisticas-header {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    padding: 20px;
    border-bottom: 2px solid #81c784;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.compras-estadisticas-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.compras-icon {
    font-size: 24px;
}

.compras-estadisticas-title h4 {
    margin: 0;
    color: #2e7d32;
    font-weight: 600;
}

.compras-acciones {
    display: flex;
    gap: 12px;
}

.btn-nueva-compra, .btn-reporte-compras {
    background: #4caf50;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    text-decoration: none;
    display: inline-block;
}

.btn-nueva-compra:hover, .btn-reporte-compras:hover {
    background: #388e3c;
    transform: translateY(-1px);
    color: white;
    text-decoration: none;
}

.btn-reporte-compras {
    background: #ff9800;
}

.btn-reporte-compras:hover {
    background: #f57c00;
}

.compras-estadisticas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0;
}

.stat-card {
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    border-right: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.stat-card:last-child {
    border-right: none;
}

.stat-card:hover {
    background: #f8f9fa;
}

.stat-icon {
    font-size: 32px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.stat-total .stat-icon {
    background: #e8f5e8;
}

.stat-mes .stat-icon {
    background: #e3f2fd;
}

.stat-pendientes .stat-icon {
    background: #fff3e0;
}

.stat-gastado .stat-icon {
    background: #f3e5f5;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-numero {
    font-size: 24px;
    font-weight: 700;
    color: #212529;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
    margin-top: 4px;
}

@media (max-width: 768px) {
    .compras-estadisticas-header {
        flex-direction: column;
        text-align: center;
    }
    
    .compras-acciones {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-nueva-compra, .btn-reporte-compras {
        width: 100%;
    }

    .compras-estadisticas-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .stat-card {
        border-right: none;
        border-bottom: 1px solid #e9ecef;
        justify-content: center;
        text-align: center;
    }

    .stat-card:nth-child(2n) {
        border-right: 1px solid #e9ecef;
    }
}

/* Estilos para tabla de compras */
.compras-tabla-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-top: 20px;
}

.compras-tabla-header {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    padding: 20px;
    border-bottom: 2px solid #ce93d8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.compras-tabla-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.tabla-icon {
    font-size: 20px;
}

.compras-tabla-title h5 {
    margin: 0;
    color: #7b1fa2;
    font-weight: 600;
}

.compras-filtros {
    display: flex;
    gap: 12px;
}

.filtro-estado, .filtro-mes {
    padding: 8px 16px;
    border: 2px solid #ce93d8;
    border-radius: 20px;
    background: white;
    color: #7b1fa2;
    font-weight: 500;
    cursor: pointer;
}

.compras-tabla-content {
    padding: 20px;
}

.compras-empty {
    text-align: center;
    padding: 40px 20px;
}

.empty-icon {
    font-size: 48px;
    display: block;
    margin-bottom: 16px;
}

.compras-empty h5 {
    color: #6c757d;
    margin: 0 0 8px 0;
}

.compras-empty p {
    color: #6c757d;
    margin-bottom: 20px;
}

.table th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.table td {
    vertical-align: middle;
    font-size: 14px;
}

@media (max-width: 768px) {
    .compras-tabla-header {
        flex-direction: column;
        text-align: center;
    }

    .compras-filtros {
        flex-direction: column;
        width: 100%;
    }

    .filtro-estado, .filtro-mes {
        width: 100%;
    }
}
</style>

<!-- Tabla de Compras -->
<div class="compras-tabla-container">
    <div class="compras-tabla-header">
        <div class="compras-tabla-title">
            <span class="tabla-icon">📋</span>
            <h5>Historial de Compras</h5>
        </div>
        <div class="compras-filtros">
            <select class="filtro-estado" id="filtroEstado">
                <option value="">Todos los estados</option>
                <option value="pendiente">⏳ Pendiente</option>
                <option value="recibida">✅ Recibida</option>
                <option value="cancelada">❌ Cancelada</option>
            </select>
            <input type="month" class="filtro-mes" id="filtroMes"
                   value="<?= date('Y-m') ?>" title="Filtrar por mes">
        </div>
    </div>

    <div class="compras-tabla-content">
        <?php if (empty($compras)): ?>
            <div class="compras-empty">
                <span class="empty-icon">🛒</span>
                <h5>No hay compras registradas</h5>
                <p>Registra tu primera compra para comenzar a gestionar el inventario</p>
                <a href="inventario.php" class="btn btn-primary">➕ Registrar Primera Compra</a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Fecha</th>
                            <th>Proveedor</th>
                            <th>Factura</th>
                            <th>Total</th>
                            <th>Estado</th>
                            <th>Usuario</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($compras as $compra): ?>
                            <?php
                            $clase_estado = '';
                            $icono_estado = '';

                            switch ($compra['estado']) {
                                case 'pendiente':
                                    $clase_estado = 'warning';
                                    $icono_estado = '⏳';
                                    break;
                                case 'recibida':
                                    $clase_estado = 'success';
                                    $icono_estado = '✅';
                                    break;
                                case 'cancelada':
                                    $clase_estado = 'danger';
                                    $icono_estado = '❌';
                                    break;
                            }
                            ?>
                            <tr data-estado="<?= $compra['estado'] ?>"
                                data-fecha="<?= date('Y-m', strtotime($compra['fecha_compra'])) ?>">
                                <td>
                                    <strong>#<?= $compra['id'] ?></strong>
                                </td>
                                <td>
                                    <?= date('d/m/Y', strtotime($compra['fecha_compra'])) ?>
                                    <br>
                                    <small class="text-muted">
                                        <?= date('H:i', strtotime($compra['fecha_creacion'])) ?>
                                    </small>
                                </td>
                                <td>
                                    <strong><?= htmlspecialchars($compra['proveedor_nombre']) ?></strong>
                                    <?php if ($compra['proveedor_contacto']): ?>
                                        <br>
                                        <small class="text-muted">
                                            👤 <?= htmlspecialchars($compra['proveedor_contacto']) ?>
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($compra['numero_factura']): ?>
                                        <span class="badge bg-info">
                                            📄 <?= htmlspecialchars($compra['numero_factura']) ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">Sin número</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong>Q<?= number_format($compra['total'], 2) ?></strong>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $clase_estado ?>">
                                        <?= $icono_estado ?> <?= ucfirst($compra['estado']) ?>
                                    </span>
                                </td>
                                <td>
                                    <small><?= htmlspecialchars($compra['usuario_nombre']) ?></small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary btn-sm ver-detalles-btn"
                                                data-id="<?= $compra['id'] ?>"
                                                data-proveedor="<?= htmlspecialchars($compra['proveedor_nombre']) ?>"
                                                data-fecha="<?= date('d/m/Y', strtotime($compra['fecha_compra'])) ?>"
                                                data-total="<?= number_format($compra['total'], 2) ?>"
                                                data-factura="<?= htmlspecialchars($compra['numero_factura'] ?: 'Sin número') ?>"
                                                title="Ver detalles">
                                            👁️
                                        </button>
                                        <?php if ($compra['estado'] === 'pendiente'): ?>
                                            <button class="btn btn-outline-success btn-sm marcar-recibida-btn"
                                                    data-id="<?= $compra['id'] ?>"
                                                    title="Marcar como recibida">
                                                ✅
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm cancelar-compra-btn"
                                                    data-id="<?= $compra['id'] ?>"
                                                    title="Cancelar compra">
                                                ❌
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal Nueva Compra -->
<div class="modal fade" id="nuevaCompraModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">➕ Nueva Compra de Inventario</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="formNuevaCompra">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="proveedor_id" class="form-label">🏢 Proveedor *</label>
                                <select class="form-select" id="proveedor_id" name="proveedor_id" required>
                                    <option value="">Seleccionar proveedor...</option>
                                    <?php
                                    try {
                                        $stmt = $pdo->query("SELECT id, nombre FROM proveedores ORDER BY nombre");
                                        while ($proveedor = $stmt->fetch()) {
                                            echo "<option value='{$proveedor['id']}'>{$proveedor['nombre']}</option>";
                                        }
                                    } catch (PDOException $e) {
                                        echo "<option value=''>Error al cargar proveedores</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="fecha_compra" class="form-label">📅 Fecha de Compra *</label>
                                <input type="date" class="form-control" id="fecha_compra" name="fecha_compra"
                                       value="<?= date('Y-m-d') ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="numero_factura" class="form-label">📄 Número de Factura</label>
                        <input type="text" class="form-control" id="numero_factura" name="numero_factura"
                               placeholder="Opcional">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">📦 Productos a Comprar</label>
                        <div id="productos-compra">
                            <div class="producto-compra-item border rounded p-3 mb-2">
                                <div class="row">
                                    <div class="col-md-4">
                                        <select class="form-select producto-select" name="productos[0][producto_id]" required>
                                            <option value="">Seleccionar producto...</option>
                                            <?php
                                            try {
                                                $stmt = $pdo->query("
                                                    SELECT p.id, p.nombre, i.unidad_medida
                                                    FROM productos p
                                                    LEFT JOIN inventario i ON p.id = i.producto_id
                                                    ORDER BY p.nombre
                                                ");
                                                while ($producto = $stmt->fetch()) {
                                                    echo "<option value='{$producto['id']}'>{$producto['nombre']} ({$producto['unidad_medida']})</option>";
                                                }
                                            } catch (PDOException $e) {
                                                echo "<option value=''>Error al cargar productos</option>";
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <input type="number" class="form-control cantidad-input"
                                               name="productos[0][cantidad]" placeholder="Cantidad" min="1" required>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" class="form-control costo-input"
                                               name="productos[0][costo_unitario]" placeholder="Costo unitario"
                                               step="0.01" min="0" required>
                                    </div>
                                    <div class="col-md-2">
                                        <input type="text" class="form-control total-input"
                                               placeholder="Total" readonly>
                                    </div>
                                    <div class="col-md-1">
                                        <button type="button" class="btn btn-danger btn-sm eliminar-producto">❌</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm" id="agregar-producto">
                            ➕ Agregar Producto
                        </button>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">💰 Total de la Compra</label>
                                <input type="text" class="form-control" id="total-compra" readonly>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">💾 Registrar Compra</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Ver Detalles de Compra -->
<div class="modal fade" id="detallesCompraModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">👁️ Detalles de Compra</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>🏢 Proveedor:</strong>
                        <span id="detalle-proveedor"></span>
                    </div>
                    <div class="col-md-6">
                        <strong>📅 Fecha:</strong>
                        <span id="detalle-fecha"></span>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>📄 Factura:</strong>
                        <span id="detalle-factura"></span>
                    </div>
                    <div class="col-md-6">
                        <strong>💰 Total:</strong>
                        <span id="detalle-total"></span>
                    </div>
                </div>

                <h6>📦 Productos Comprados</h6>
                <div id="detalle-productos">
                    <!-- Los productos se cargarán aquí via AJAX -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// JavaScript adicional
$extra_js = '
<script>
// Función para mostrar toasts (definida globalmente)
function mostrarToast(mensaje, tipo) {
    const toastContainer = document.querySelector(".toast-container") || createToastContainer();

    const toastId = "toast-" + Date.now();
    const bgClass = tipo === "success" ? "bg-success" : tipo === "error" ? "bg-danger" : "bg-info";

    const toastHTML = `
        <div id="${toastId}" class="toast ${bgClass} text-white" role="alert">
            <div class="toast-body">
                ${mensaje}
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML("beforeend", toastHTML);

    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
    toast.show();

    toastElement.addEventListener("hidden.bs.toast", function() {
        this.remove();
    });
}

function createToastContainer() {
    const container = document.createElement("div");
    container.className = "toast-container position-fixed top-0 end-0 p-3";
    container.style.zIndex = "9999";
    document.body.appendChild(container);
    return container;
}

// Función para probar rutas (definida globalmente)
function probarRuta() {
    mostrarToast("🔧 Probando conexiones del sistema...", "info");

    fetch("../api/test_ruta.php")
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Mostrar información detallada del sistema
                let mensaje = "✅ Sistema funcionando correctamente\\n\\n";
                mensaje += "📊 Estadísticas:\\n";
                mensaje += "• Productos activos: " + data.estadisticas.productos_activos + "\\n";
                mensaje += "• Total compras: " + data.estadisticas.total_compras + "\\n";
                mensaje += "• Movimientos inventario: " + data.estadisticas.total_movimientos + "\\n\\n";
                mensaje += "🔗 APIs disponibles:\\n";
                Object.keys(data.apis_disponibles).forEach(api => {
                    mensaje += "• " + api + ": " + data.apis_disponibles[api] + "\\n";
                });
                mensaje += "\\n⏰ Timestamp: " + data.timestamp;

                alert(mensaje);
                mostrarToast("✅ " + data.message, "success");
            } else {
                mostrarToast("❌ Error en ruta: " + data.message, "error");
                console.error("Error details:", data);
            }
        })
        .catch(error => {
            mostrarToast("❌ Error de conexión: " + error.message, "error");
            console.error("Error:", error);
        });
}

// Función para generar reportes (definida globalmente)
function generarReporteCompras() {
    mostrarToast("📊 Abriendo opciones de reporte...", "info");

    // Calcular fechas por defecto
    const fechaFin = new Date().toISOString().split("T")[0];
    const fechaInicio = new Date(Date.now() - 30*24*60*60*1000).toISOString().split("T")[0];

    // Crear modal dinámicamente
    const modalHtml = `
        <div class="modal fade" id="reporteComprasModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">📊 Generar Reporte de Compras</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="formReporteCompras">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Fecha Inicio</label>
                                    <input type="date" class="form-control" id="fecha_inicio_reporte" value="${fechaInicio}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Fecha Fin</label>
                                    <input type="date" class="form-control" id="fecha_fin_reporte" value="${fechaFin}" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Proveedor (Opcional)</label>
                                <select class="form-control" id="proveedor_reporte">
                                    <option value="">Todos los proveedores</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Estado</label>
                                <select class="form-control" id="estado_reporte">
                                    <option value="">Todos los estados</option>
                                    <option value="pendiente">Pendiente</option>
                                    <option value="recibida">Recibida</option>
                                    <option value="cancelada">Cancelada</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Tipo de Reporte</label>
                                <select class="form-control" id="tipo_reporte" required>
                                    <option value="resumen">Resumen Ejecutivo</option>
                                    <option value="detallado">Reporte Detallado</option>
                                    <option value="por_proveedor">Por Proveedor</option>
                                    <option value="por_producto">Por Producto</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn btn-primary" onclick="procesarReporteCompras()">📊 Generar Reporte</button>
                        <button type="button" class="btn btn-success" onclick="exportarReporteCompras()">📥 Exportar CSV</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remover modal existente si existe
    const existingModal = document.getElementById("reporteComprasModal");
    if (existingModal) {
        existingModal.remove();
    }

    // Agregar modal al DOM
    document.body.insertAdjacentHTML("beforeend", modalHtml);

    // Cargar proveedores
    cargarProveedoresReporte();

    // Mostrar modal
    const modal = new bootstrap.Modal(document.getElementById("reporteComprasModal"));
    modal.show();
}

// Función para cargar proveedores en el reporte
function cargarProveedoresReporte() {
    fetch("../api/obtener_proveedores.php")
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById("proveedor_reporte");
                if (select) {
                    data.proveedores.forEach(proveedor => {
                        const option = document.createElement("option");
                        option.value = proveedor.id;
                        option.textContent = proveedor.nombre;
                        select.appendChild(option);
                    });
                }
            }
        })
        .catch(error => {
            console.error("Error al cargar proveedores:", error);
        });
}

// Función para procesar el reporte de compras
function procesarReporteCompras() {
    const fechaInicio = document.getElementById("fecha_inicio_reporte").value;
    const fechaFin = document.getElementById("fecha_fin_reporte").value;
    const proveedorId = document.getElementById("proveedor_reporte").value;
    const estado = document.getElementById("estado_reporte").value;
    const tipoReporte = document.getElementById("tipo_reporte").value;

    if (!fechaInicio || !fechaFin || !tipoReporte) {
        mostrarToast("❌ Por favor complete todos los campos requeridos", "error");
        return;
    }

    // Construir parámetros
    let params = "fecha_inicio=" + fechaInicio + "&fecha_fin=" + fechaFin + "&tipo=" + tipoReporte;
    if (proveedorId) params += "&proveedor_id=" + proveedorId;
    if (estado) params += "&estado=" + estado;

    // Mostrar loading
    mostrarToast("📊 Generando reporte...", "info");

    // Abrir reporte directamente en nueva ventana
    mostrarToast("📊 Abriendo reporte...", "info");
    window.open("../api/reportes_compras.php?" + params + "&view=html", "_blank");
    bootstrap.Modal.getInstance(document.getElementById("reporteComprasModal")).hide();
}

// Función para exportar reporte a CSV
function exportarReporteCompras() {
    const fechaInicio = document.getElementById("fecha_inicio_reporte").value;
    const fechaFin = document.getElementById("fecha_fin_reporte").value;
    const proveedorId = document.getElementById("proveedor_reporte").value;
    const estado = document.getElementById("estado_reporte").value;

    if (!fechaInicio || !fechaFin) {
        mostrarToast("❌ Por favor seleccione las fechas", "error");
        return;
    }

    // Construir parámetros
    let params = "fecha_inicio=" + fechaInicio + "&fecha_fin=" + fechaFin + "&export=csv";
    if (proveedorId) params += "&proveedor_id=" + proveedorId;
    if (estado) params += "&estado=" + estado;

    // Abrir en nueva ventana para descargar
    window.open("../api/exportar_compras.php?" + params, "_blank");
    mostrarToast("📥 Descargando reporte CSV...", "success");
}

// Funciones de reporte simplificadas - se abrirán en nueva ventana

document.addEventListener("DOMContentLoaded", function() {
    // Filtros
    const filtroEstado = document.getElementById("filtroEstado");
    const filtroMes = document.getElementById("filtroMes");

    if (filtroEstado) {
        filtroEstado.addEventListener("change", aplicarFiltros);
    }
    if (filtroMes) {
        filtroMes.addEventListener("change", aplicarFiltros);
    }

    function aplicarFiltros() {
        const estadoSeleccionado = filtroEstado ? filtroEstado.value : "";
        const mesSeleccionado = filtroMes ? filtroMes.value : "";

        const filas = document.querySelectorAll("tbody tr");

        filas.forEach(fila => {
            let mostrar = true;

            // Filtro por estado
            if (estadoSeleccionado && fila.dataset.estado !== estadoSeleccionado) {
                mostrar = false;
            }

            // Filtro por mes
            if (mesSeleccionado && fila.dataset.fecha !== mesSeleccionado) {
                mostrar = false;
            }

            fila.style.display = mostrar ? "" : "none";
        });
    }

    // ===== MODAL NUEVA COMPRA =====
    let contadorProductos = 1;

    // Agregar producto a la compra
    document.getElementById("agregar-producto").addEventListener("click", function() {
        const container = document.getElementById("productos-compra");
        const nuevoItem = document.querySelector(".producto-compra-item").cloneNode(true);

        // Actualizar nombres de los inputs
        nuevoItem.querySelectorAll("select, input").forEach(input => {
            if (input.name) {
                input.name = input.name.replace("[0]", `[${contadorProductos}]`);
                input.value = "";
            }
        });

        container.appendChild(nuevoItem);
        contadorProductos++;

        // Agregar eventos al nuevo item
        configurarEventosProducto(nuevoItem);
    });

    // Configurar eventos para items de productos
    function configurarEventosProducto(item) {
        const cantidadInput = item.querySelector(".cantidad-input");
        const costoInput = item.querySelector(".costo-input");
        const totalInput = item.querySelector(".total-input");
        const eliminarBtn = item.querySelector(".eliminar-producto");

        // Calcular total cuando cambie cantidad o costo
        function calcularTotal() {
            const cantidad = parseFloat(cantidadInput.value) || 0;
            const costo = parseFloat(costoInput.value) || 0;
            const total = cantidad * costo;
            totalInput.value = total.toFixed(2);
            calcularTotalCompra();
        }

        cantidadInput.addEventListener("input", calcularTotal);
        costoInput.addEventListener("input", calcularTotal);

        // Eliminar producto
        eliminarBtn.addEventListener("click", function() {
            if (document.querySelectorAll(".producto-compra-item").length > 1) {
                item.remove();
                calcularTotalCompra();
            }
        });
    }

    // Calcular total de la compra
    function calcularTotalCompra() {
        let total = 0;
        document.querySelectorAll(".total-input").forEach(input => {
            total += parseFloat(input.value) || 0;
        });
        document.getElementById("total-compra").value = "Q" + total.toFixed(2);
    }

    // Configurar eventos iniciales
    configurarEventosProducto(document.querySelector(".producto-compra-item"));

    // Enviar formulario de nueva compra
    document.getElementById("formNuevaCompra").addEventListener("submit", function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        fetch("../api/procesar_compra.php", {
            method: "POST",
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                mostrarToast("✅ Compra registrada exitosamente", "success");
                bootstrap.Modal.getInstance(document.getElementById("nuevaCompraModal")).hide();
                location.reload();
            } else {
                mostrarToast("❌ Error: " + data.message, "error");
            }
        })
        .catch(error => {
            mostrarToast("❌ Error al procesar la compra", "error");
        });
    });

    // Ver detalles de compra
    document.querySelectorAll(".ver-detalles-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const id = this.dataset.id;
            const proveedor = this.dataset.proveedor;
            const fecha = this.dataset.fecha;
            const total = this.dataset.total;
            const factura = this.dataset.factura;

            // Llenar información básica
            document.getElementById("detalle-proveedor").textContent = proveedor;
            document.getElementById("detalle-fecha").textContent = fecha;
            document.getElementById("detalle-total").textContent = "Q" + total;
            document.getElementById("detalle-factura").textContent = factura;

            // Cargar productos via AJAX
            cargarDetallesProductos(id);

            // Mostrar modal
            const modal = new bootstrap.Modal(document.getElementById("detallesCompraModal"));
            modal.show();
        });
    });

    function cargarDetallesProductos(compraId) {
        const container = document.getElementById("detalle-productos");
        container.innerHTML = "<div class=\"text-center\"><div class=\"spinner-border\" role=\"status\"></div></div>";

        fetch(`../api/obtener_detalle_compra.php?id=${compraId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    mostrarDetallesProductos(data.productos);
                } else {
                    container.innerHTML = "<div class=\"alert alert-danger\">Error al cargar los detalles</div>";
                }
            })
            .catch(error => {
                container.innerHTML = "<div class=\"alert alert-danger\">Error de conexión</div>";
            });
    }

    function mostrarDetallesProductos(productos) {
        const container = document.getElementById("detalle-productos");

        if (productos.length === 0) {
            container.innerHTML = "<div class=\"alert alert-info\">No hay productos en esta compra</div>";
            return;
        }

        let html = "<div class=\"table-responsive\"><table class=\"table table-sm\">";
        html += "<thead><tr><th>Producto</th><th>Cantidad</th><th>Precio Unit.</th><th>Subtotal</th></tr></thead><tbody>";

        productos.forEach(producto => {
            html += `<tr>
                <td><strong>${producto.producto_nombre}</strong></td>
                <td>${producto.cantidad} ${producto.unidad || ""}</td>
                <td>Q${parseFloat(producto.precio_unitario).toFixed(2)}</td>
                <td>Q${parseFloat(producto.subtotal).toFixed(2)}</td>
            </tr>`;
        });

        html += "</tbody></table></div>";
        container.innerHTML = html;
    }

    // Marcar como recibida
    document.querySelectorAll(".marcar-recibida-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const id = this.dataset.id;

            // Marcar como recibida directamente
            cambiarEstadoCompra(id, "recibida");
        });
    });

    // Cancelar compra
    document.querySelectorAll(".cancelar-compra-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const id = this.dataset.id;

            // Cancelar compra directamente
            cambiarEstadoCompra(id, "cancelada");
        });
    });

    function cambiarEstadoCompra(id, nuevoEstado) {
        const formData = new FormData();
        formData.append("compra_id", id);
        formData.append("estado", nuevoEstado);

        fetch("../api/cambiar_estado_compra.php", {
            method: "POST",
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                mostrarToast("✅ Estado actualizado exitosamente", "success");
                location.reload();
            } else {
                mostrarToast("❌ Error: " + data.message, "error");
            }
        })
        .catch(error => {
            mostrarToast("❌ Error de conexión", "error");
        });
    }

});
</script>
';

// Incluir el layout
include '../includes/layout.php';
?>
