<?php
// Documentación pública - No requiere autenticación
// Permitir acceso sin login para facilitar la implementación del sistema

// Variables para el layout
$page_title = 'Documentación Técnica';
$breadcrumbs = [
    ['title' => 'Inicio', 'url' => '/Restaurante/'],
    ['title' => 'Documentación', 'url' => '/Restaurante/docs/'],
    ['title' => 'Documentación Técnica']
];

// Iniciar captura de contenido
ob_start();
?>

<!-- Header -->
<div class="dev-docs-header">
    <div class="dev-docs-title">
        <span class="dev-docs-icon">💻</span>
        <h2>Documentación Técnica</h2>
    </div>
    <p>Información técnica completa para desarrolladores</p>
</div>

<!-- Navegación Interna -->
<div class="dev-docs-nav">
    <h4>🔧 Contenido Técnico</h4>
    <div class="nav-links">
        <a href="#arquitectura" class="nav-link">🏗️ Arquitectura</a>
        <a href="#database" class="nav-link">🗄️ Base de Datos</a>
        <a href="#api" class="nav-link">🔌 APIs</a>
        <a href="#frontend" class="nav-link">🎨 Frontend</a>
        <a href="#seguridad" class="nav-link">🔒 Seguridad</a>
        <a href="#deployment" class="nav-link">🚀 Deployment</a>
    </div>
</div>

<!-- Arquitectura del Sistema -->
<section id="arquitectura" class="docs-section">
    <div class="section-header">
        <h3>🏗️ Arquitectura del Sistema</h3>
    </div>
    
    <div class="section-content">
        <div class="arch-overview">
            <h5>📋 Resumen de la Arquitectura</h5>
            <p>Sistema web basado en PHP con arquitectura MVC simplificada, usando MySQL como base de datos y AdminLTE para la interfaz de usuario.</p>
        </div>
        
        <div class="tech-stack">
            <h5>🛠️ Stack Tecnológico</h5>
            <div class="stack-grid">
                <div class="stack-item">
                    <h6>Backend</h6>
                    <ul>
                        <li>PHP 7.4+</li>
                        <li>MySQL 8.0+</li>
                        <li>PDO para base de datos</li>
                        <li>Sesiones PHP nativas</li>
                    </ul>
                </div>
                
                <div class="stack-item">
                    <h6>Frontend</h6>
                    <ul>
                        <li>HTML5 + CSS3</li>
                        <li>JavaScript ES6+</li>
                        <li>Bootstrap 5.3</li>
                        <li>AdminLTE 4.0</li>
                        <li>Bootstrap Icons</li>
                    </ul>
                </div>
                
                <div class="stack-item">
                    <h6>Herramientas</h6>
                    <ul>
                        <li>XAMPP/LAMP</li>
                        <li>Git para control de versiones</li>
                        <li>Composer (opcional)</li>
                        <li>VS Code recomendado</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="folder-structure">
            <h5>📁 Estructura de Carpetas</h5>
            <div class="code-block">
<pre>/Restaurante/
├── admin/              # Panel de administración
├── api/               # Endpoints de API
├── assets/            # Recursos estáticos
│   ├── css/          # Estilos personalizados
│   ├── js/           # JavaScript personalizado
│   ├── img/          # Imágenes del sistema
│   └── adminlte/     # Framework AdminLTE
├── auth/              # Autenticación
├── bebidas/           # Panel de bebidas
├── cocina/            # Panel de cocina
├── config/            # Configuración
├── docs/              # Documentación
├── includes/          # Archivos compartidos
├── meseros/           # Panel de meseros
└── database_setup.sql # Script de base de datos</pre>
            </div>
        </div>
        
        <div class="design-patterns">
            <h5>🎯 Patrones de Diseño</h5>
            <div class="pattern-grid">
                <div class="pattern-item">
                    <h6>MVC Simplificado</h6>
                    <p>Separación de lógica de negocio, presentación y datos sin framework complejo.</p>
                </div>
                
                <div class="pattern-item">
                    <h6>Role-Based Access</h6>
                    <p>Sistema de roles con permisos específicos por área de trabajo.</p>
                </div>
                
                <div class="pattern-item">
                    <h6>API REST</h6>
                    <p>Endpoints RESTful para operaciones CRUD y acciones específicas.</p>
                </div>
                
                <div class="pattern-item">
                    <h6>Component-Based UI</h6>
                    <p>Componentes reutilizables con estilos consistentes.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Base de Datos -->
<section id="database" class="docs-section">
    <div class="section-header">
        <h3>🗄️ Base de Datos</h3>
    </div>
    
    <div class="section-content">
        <div class="db-overview">
            <h5>📊 Esquema de Base de Datos</h5>
            <p>Base de datos MySQL optimizada para operaciones de restaurante con integridad referencial.</p>
        </div>
        
        <div class="tables-grid">
            <div class="table-card">
                <h6>👥 usuarios</h6>
                <div class="table-fields">
                    <code>id, nombre, email, password, rol, activo, fecha_creacion</code>
                </div>
                <p>Gestión de empleados con roles específicos (admin, mesero, cocina, bebidas).</p>
            </div>
            
            <div class="table-card">
                <h6>🪑 mesas</h6>
                <div class="table-fields">
                    <code>id, numero_mesa, estado, hash_mesa, mesero_id, fecha_apertura</code>
                </div>
                <p>Control de mesas con estados y asignación de meseros.</p>
            </div>
            
            <div class="table-card">
                <h6>💳 cuentas</h6>
                <div class="table-fields">
                    <code>id, hash_mesa, nombre_cliente, apellido_cliente, total, estado, mesero_id, fecha_creacion</code>
                </div>
                <p>Cuentas individuales por cliente en cada mesa.</p>
            </div>
            
            <div class="table-card">
                <h6>📋 ordenes</h6>
                <div class="table-fields">
                    <code>id, cuenta_id, total, estado, fecha_hora</code>
                </div>
                <p>Órdenes asociadas a cuentas específicas.</p>
            </div>
            
            <div class="table-card">
                <h6>🍽️ productos</h6>
                <div class="table-fields">
                    <code>id, nombre, descripcion, precio_costo, precio_venta, categoria_id, area, disponible, imagen</code>
                </div>
                <p>Catálogo de productos con precios y categorización.</p>
            </div>
            
            <div class="table-card">
                <h6>📦 detalle_orden</h6>
                <div class="table-fields">
                    <code>id, orden_id, producto_id, cantidad, precio_unitario, subtotal, notas, estado</code>
                </div>
                <p>Detalles de productos en cada orden con estados individuales.</p>
            </div>
            
            <div class="table-card">
                <h6>🏷️ categorias</h6>
                <div class="table-fields">
                    <code>id, nombre, descripcion, icono, color, activo</code>
                </div>
                <p>Categorías para organizar productos en el menú.</p>
            </div>
            
            <div class="table-card">
                <h6>⚙️ configuracion</h6>
                <div class="table-fields">
                    <code>id, nombre_restaurante, descripcion, direccion, telefono, total_mesas, mensajes_cortesia</code>
                </div>
                <p>Configuración general del restaurante.</p>
            </div>
        </div>
        
        <div class="relationships">
            <h5>🔗 Relaciones Principales</h5>
            <div class="rel-grid">
                <div class="rel-item">
                    <strong>mesas → cuentas:</strong> Una mesa puede tener múltiples cuentas activas
                </div>
                <div class="rel-item">
                    <strong>cuentas → ordenes:</strong> Una cuenta puede tener múltiples órdenes
                </div>
                <div class="rel-item">
                    <strong>ordenes → detalle_orden:</strong> Una orden contiene múltiples productos
                </div>
                <div class="rel-item">
                    <strong>productos → categorias:</strong> Productos organizados por categorías
                </div>
                <div class="rel-item">
                    <strong>usuarios → mesas:</strong> Meseros asignados a mesas específicas
                </div>
            </div>
        </div>
        
        <div class="db-setup">
            <h5>🛠️ Configuración de Base de Datos</h5>
            <div class="code-block">
<pre># Configuración en config/db.php
$host = 'localhost';
$db   = 'restaurante';
$user = 'root';
$pass = '';
$charset = 'utf8mb4';

# Ejecutar script de instalación
mysql -u root -p < database_setup.sql</pre>
            </div>
        </div>
    </div>
</section>

<!-- APIs y Endpoints -->
<section id="api" class="docs-section">
    <div class="section-header">
        <h3>🔌 APIs y Endpoints</h3>
    </div>

    <div class="section-content">
        <div class="api-overview">
            <h5>🌐 Estructura de APIs</h5>
            <p>Todas las APIs están en la carpeta <code>/api/</code> y devuelven respuestas JSON. Requieren autenticación por sesión.</p>
        </div>

        <div class="endpoints-grid">
            <div class="endpoint-category">
                <h6>🍽️ Gestión de Órdenes</h6>
                <div class="endpoint-list">
                    <div class="endpoint-item">
                        <span class="method post">POST</span>
                        <code>/api/crear_orden.php</code>
                        <p>Crear nueva orden con productos</p>
                    </div>
                    <div class="endpoint-item">
                        <span class="method post">POST</span>
                        <code>/api/crear_orden_cuenta.php</code>
                        <p>Crear orden asociada a cuenta específica</p>
                    </div>
                    <div class="endpoint-item">
                        <span class="method put">PUT</span>
                        <code>/api/cambiar_estado_detalle.php</code>
                        <p>Cambiar estado de producto individual</p>
                    </div>
                    <div class="endpoint-item">
                        <span class="method post">POST</span>
                        <code>/api/finalizar_orden.php</code>
                        <p>Finalizar orden completa</p>
                    </div>
                </div>
            </div>

            <div class="endpoint-category">
                <h6>🪑 Gestión de Mesas</h6>
                <div class="endpoint-list">
                    <div class="endpoint-item">
                        <span class="method post">POST</span>
                        <code>/api/abrir_mesa.php</code>
                        <p>Abrir mesa para servicio</p>
                    </div>
                    <div class="endpoint-item">
                        <span class="method post">POST</span>
                        <code>/api/abrir_mesa_con_cuenta.php</code>
                        <p>Abrir mesa y crear primera cuenta</p>
                    </div>
                    <div class="endpoint-item">
                        <span class="method post">POST</span>
                        <code>/api/cerrar_mesa.php</code>
                        <p>Cerrar mesa después del servicio</p>
                    </div>
                </div>
            </div>

            <div class="endpoint-category">
                <h6>💳 Gestión de Cuentas</h6>
                <div class="endpoint-list">
                    <div class="endpoint-item">
                        <span class="method post">POST</span>
                        <code>/api/crear_cuenta.php</code>
                        <p>Crear nueva cuenta en mesa</p>
                    </div>
                    <div class="endpoint-item">
                        <span class="method post">POST</span>
                        <code>/api/cerrar_cuenta.php</code>
                        <p>Procesar pago de cuenta</p>
                    </div>
                    <div class="endpoint-item">
                        <span class="method post">POST</span>
                        <code>/api/pagar_mesa_completa.php</code>
                        <p>Pagar todas las cuentas de una mesa</p>
                    </div>
                </div>
            </div>

            <div class="endpoint-category">
                <h6>🍽️ Gestión de Productos</h6>
                <div class="endpoint-list">
                    <div class="endpoint-item">
                        <span class="method post">POST</span>
                        <code>/api/crear_producto.php</code>
                        <p>Crear nuevo producto</p>
                    </div>
                    <div class="endpoint-item">
                        <span class="method put">PUT</span>
                        <code>/api/editar_producto.php</code>
                        <p>Editar producto existente</p>
                    </div>
                    <div class="endpoint-item">
                        <span class="method put">PUT</span>
                        <code>/api/toggle_producto.php</code>
                        <p>Activar/desactivar producto</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="api-example">
            <h5>📝 Ejemplo de Uso</h5>
            <div class="code-block">
<pre>// Crear nueva orden
fetch('/Restaurante/api/crear_orden_cuenta.php', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        cuenta_id: 123,
        productos: [
            {
                producto_id: 1,
                cantidad: 2,
                notas: "Sin cebolla"
            }
        ]
    })
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('Orden creada:', data.orden_id);
    }
});</pre>
            </div>
        </div>

        <div class="response-format">
            <h5>📋 Formato de Respuesta</h5>
            <div class="code-block">
<pre>{
    "success": true,
    "message": "Orden creada exitosamente",
    "data": {
        "orden_id": 123,
        "total": 45.50,
        "productos_count": 3
    }
}</pre>
            </div>
        </div>
    </div>
</section>

<!-- Frontend -->
<section id="frontend" class="docs-section">
    <div class="section-header">
        <h3>🎨 Frontend y Componentes</h3>
    </div>

    <div class="section-content">
        <div class="frontend-overview">
            <h5>🎯 Filosofía de Diseño</h5>
            <p>Interfaz moderna con componentes reutilizables, optimizada para dispositivos táctiles y uso en entornos de restaurante.</p>
        </div>

        <div class="ui-components">
            <h5>🧩 Componentes Principales</h5>
            <div class="components-grid">
                <div class="component-card">
                    <h6>📋 Cards de Orden</h6>
                    <p>Componentes para mostrar órdenes con estados visuales y acciones rápidas.</p>
                    <div class="component-features">
                        <span class="feature-tag">Estados con colores</span>
                        <span class="feature-tag">Iconos emoji</span>
                        <span class="feature-tag">Responsive</span>
                    </div>
                </div>

                <div class="component-card">
                    <h6>🍽️ Selector de Productos</h6>
                    <p>Interfaz para seleccionar productos con categorías y filtros.</p>
                    <div class="component-features">
                        <span class="feature-tag">Filtros dinámicos</span>
                        <span class="feature-tag">Imágenes</span>
                        <span class="feature-tag">Contador cantidad</span>
                    </div>
                </div>

                <div class="component-card">
                    <h6>🪑 Selector de Mesas</h6>
                    <p>Grid visual para seleccionar mesas con estados claros.</p>
                    <div class="component-features">
                        <span class="feature-tag">Estados visuales</span>
                        <span class="feature-tag">Información hover</span>
                        <span class="feature-tag">Grid adaptativo</span>
                    </div>
                </div>

                <div class="component-card">
                    <h6>🔔 Sistema de Notificaciones</h6>
                    <p>Toast notifications para feedback del usuario.</p>
                    <div class="component-features">
                        <span class="feature-tag">Auto-dismiss</span>
                        <span class="feature-tag">Tipos de mensaje</span>
                        <span class="feature-tag">Animaciones</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="css-architecture">
            <h5>🎨 Arquitectura CSS</h5>
            <div class="css-structure">
                <div class="css-item">
                    <strong>AdminLTE Base:</strong> Framework principal para layout y componentes básicos
                </div>
                <div class="css-item">
                    <strong>Custom CSS:</strong> Estilos personalizados en <code>/assets/css/custom.css</code>
                </div>
                <div class="css-item">
                    <strong>Component Styles:</strong> Estilos específicos por componente en cada archivo PHP
                </div>
                <div class="css-item">
                    <strong>Responsive Design:</strong> Mobile-first con breakpoints estándar
                </div>
            </div>
        </div>

        <div class="js-architecture">
            <h5>⚡ JavaScript</h5>
            <div class="js-structure">
                <div class="js-item">
                    <strong>Vanilla JS:</strong> Sin frameworks pesados, JavaScript nativo para mejor rendimiento
                </div>
                <div class="js-item">
                    <strong>Fetch API:</strong> Para comunicación con APIs del backend
                </div>
                <div class="js-item">
                    <strong>Event Delegation:</strong> Manejo eficiente de eventos en elementos dinámicos
                </div>
                <div class="js-item">
                    <strong>Modular Functions:</strong> Funciones reutilizables para operaciones comunes
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Seguridad -->
<section id="seguridad" class="docs-section">
    <div class="section-header">
        <h3>🔒 Seguridad</h3>
    </div>

    <div class="section-content">
        <div class="security-overview">
            <h5>🛡️ Medidas de Seguridad Implementadas</h5>
            <p>El sistema implementa múltiples capas de seguridad para proteger datos y operaciones.</p>
        </div>

        <div class="security-measures">
            <div class="security-grid">
                <div class="security-item">
                    <h6>🔐 Autenticación</h6>
                    <ul>
                        <li>Sesiones PHP nativas</li>
                        <li>Passwords hasheados con MD5</li>
                        <li>Verificación de rol en cada página</li>
                        <li>Timeout automático de sesión</li>
                    </ul>
                </div>

                <div class="security-item">
                    <h6>🚫 Autorización</h6>
                    <ul>
                        <li>Control de acceso basado en roles</li>
                        <li>Verificación de permisos por endpoint</li>
                        <li>Filtrado de datos según rol</li>
                        <li>Redirección automática si no autorizado</li>
                    </ul>
                </div>

                <div class="security-item">
                    <h6>💉 Prevención de Inyecciones</h6>
                    <ul>
                        <li>Prepared statements en todas las consultas</li>
                        <li>Validación de entrada de datos</li>
                        <li>Sanitización de HTML output</li>
                        <li>Escape de caracteres especiales</li>
                    </ul>
                </div>

                <div class="security-item">
                    <h6>🔒 Protección de Archivos</h6>
                    <ul>
                        <li>Archivos index.php en carpetas sensibles</li>
                        <li>Configuración fuera del webroot</li>
                        <li>Validación de tipos de archivo</li>
                        <li>Límites de tamaño de upload</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="security-best-practices">
            <h5>✅ Mejores Prácticas</h5>
            <div class="practices-list">
                <div class="practice-item">
                    <strong>Validación de Entrada:</strong> Siempre validar y sanitizar datos del usuario
                </div>
                <div class="practice-item">
                    <strong>Prepared Statements:</strong> Usar PDO con parámetros para todas las consultas
                </div>
                <div class="practice-item">
                    <strong>Escape de Output:</strong> Usar htmlspecialchars() para mostrar datos
                </div>
                <div class="practice-item">
                    <strong>Verificación de Sesión:</strong> Verificar autenticación en cada página
                </div>
                <div class="practice-item">
                    <strong>HTTPS:</strong> Usar SSL/TLS en producción
                </div>
            </div>
        </div>

        <div class="security-code-example">
            <h5>💻 Ejemplo de Código Seguro</h5>
            <div class="code-block">
<pre>// Verificación de autenticación
if (!isset($_SESSION['usuario_id'])) {
    header('Location: /Restaurante/auth/login.php');
    exit();
}

// Consulta segura con prepared statement
$stmt = $pdo->prepare("SELECT * FROM productos WHERE id = ? AND disponible = 1");
$stmt->execute([$producto_id]);
$producto = $stmt->fetch();

// Output seguro
echo htmlspecialchars($producto['nombre']);

// Validación de entrada
$cantidad = filter_var($_POST['cantidad'], FILTER_VALIDATE_INT);
if ($cantidad === false || $cantidad < 1) {
    throw new InvalidArgumentException('Cantidad inválida');
}</pre>
            </div>
        </div>
    </div>
</section>

<!-- Deployment -->
<section id="deployment" class="docs-section">
    <div class="section-header">
        <h3>🚀 Deployment y Configuración</h3>
    </div>

    <div class="section-content">
        <div class="deployment-overview">
            <h5>📦 Preparación para Producción</h5>
            <p>Guía completa para desplegar el sistema en un servidor de producción.</p>
        </div>

        <div class="deployment-steps">
            <h5>🔧 Pasos de Instalación</h5>
            <div class="steps-list">
                <div class="deploy-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h6>Requisitos del Servidor</h6>
                        <ul>
                            <li>PHP 7.4 o superior</li>
                            <li>MySQL 8.0 o superior</li>
                            <li>Apache/Nginx con mod_rewrite</li>
                            <li>Extensiones: PDO, PDO_MySQL, GD, mbstring</li>
                        </ul>
                    </div>
                </div>

                <div class="deploy-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h6>Configuración de Base de Datos</h6>
                        <div class="code-block">
<pre># Crear base de datos
CREATE DATABASE restaurante CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# Importar estructura
mysql -u root -p restaurante < database_setup.sql

# Configurar usuario
CREATE USER 'restaurante_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON restaurante.* TO 'restaurante_user'@'localhost';</pre>
                        </div>
                    </div>
                </div>

                <div class="deploy-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h6>Configuración del Sistema</h6>
                        <div class="code-block">
<pre># Editar config/db.php
$host = 'localhost';
$db   = 'restaurante';
$user = 'restaurante_user';
$pass = 'secure_password';

# Configurar permisos
chmod 755 /var/www/html/Restaurante
chmod 644 /var/www/html/Restaurante/config/db.php
chmod 777 /var/www/html/Restaurante/assets/img/productos/</pre>
                        </div>
                    </div>
                </div>

                <div class="deploy-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h6>Configuración del Servidor Web</h6>
                        <div class="code-block">
<pre># Apache .htaccess
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Nginx configuración
location / {
    try_files $uri $uri/ /index.php?$query_string;
}

location ~ \.php$ {
    fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
    fastcgi_index index.php;
    include fastcgi_params;
}</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="production-checklist">
            <h5>✅ Checklist de Producción</h5>
            <div class="checklist-grid">
                <div class="checklist-item">
                    <input type="checkbox" disabled> Configurar HTTPS/SSL
                </div>
                <div class="checklist-item">
                    <input type="checkbox" disabled> Cambiar contraseñas por defecto
                </div>
                <div class="checklist-item">
                    <input type="checkbox" disabled> Configurar backups automáticos
                </div>
                <div class="checklist-item">
                    <input type="checkbox" disabled> Optimizar configuración PHP
                </div>
                <div class="checklist-item">
                    <input type="checkbox" disabled> Configurar logs de error
                </div>
                <div class="checklist-item">
                    <input type="checkbox" disabled> Probar todas las funcionalidades
                </div>
                <div class="checklist-item">
                    <input type="checkbox" disabled> Configurar monitoreo
                </div>
                <div class="checklist-item">
                    <input type="checkbox" disabled> Documentar configuración
                </div>
            </div>
        </div>

        <div class="maintenance">
            <h5>🔧 Mantenimiento</h5>
            <div class="maintenance-grid">
                <div class="maintenance-item">
                    <h6>📊 Monitoreo</h6>
                    <ul>
                        <li>Logs de Apache/Nginx</li>
                        <li>Logs de PHP errors</li>
                        <li>Monitoreo de base de datos</li>
                        <li>Espacio en disco</li>
                    </ul>
                </div>

                <div class="maintenance-item">
                    <h6>💾 Backups</h6>
                    <ul>
                        <li>Backup diario de base de datos</li>
                        <li>Backup semanal de archivos</li>
                        <li>Pruebas de restauración</li>
                        <li>Almacenamiento offsite</li>
                    </ul>
                </div>

                <div class="maintenance-item">
                    <h6>🔄 Actualizaciones</h6>
                    <ul>
                        <li>Actualizaciones de seguridad PHP</li>
                        <li>Actualizaciones de MySQL</li>
                        <li>Actualizaciones del sistema</li>
                        <li>Pruebas en staging</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
/* Estilos para documentación de desarrollador */
.dev-docs-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    border-radius: 12px;
    text-align: center;
    margin-bottom: 24px;
}

.dev-docs-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    margin-bottom: 12px;
}

.dev-docs-icon {
    font-size: 48px;
}

.dev-docs-title h2 {
    margin: 0;
    font-weight: 700;
}

.dev-docs-nav {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dev-docs-nav h4 {
    margin-bottom: 16px;
    color: #2c3e50;
}

.nav-links {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.nav-link {
    background: #f8f9fa;
    color: #495057;
    padding: 8px 16px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background: #667eea;
    color: white;
    text-decoration: none;
}

.docs-section {
    background: white;
    border-radius: 12px;
    margin-bottom: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.section-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 24px;
}

.section-header h3 {
    margin: 0;
    font-weight: 600;
}

.section-content {
    padding: 24px;
}

.arch-overview, .db-overview {
    background: #e3f2fd;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #2196f3;
    margin-bottom: 24px;
}

.arch-overview h5, .db-overview h5 {
    color: #1565c0;
    margin-bottom: 12px;
    font-weight: 600;
}

.arch-overview p, .db-overview p {
    color: #495057;
    margin: 0;
}

.tech-stack, .design-patterns, .relationships, .db-setup {
    margin-bottom: 24px;
}

.tech-stack h5, .design-patterns h5, .relationships h5, .db-setup h5 {
    color: #2c3e50;
    margin-bottom: 16px;
    font-weight: 600;
}

.stack-grid, .pattern-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.stack-item, .pattern-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.stack-item h6, .pattern-item h6 {
    color: #2c3e50;
    margin-bottom: 12px;
    font-weight: 600;
}

.stack-item ul, .pattern-item p {
    color: #495057;
    margin: 0;
}

.stack-item li {
    margin-bottom: 4px;
}

.folder-structure {
    margin-bottom: 24px;
}

.folder-structure h5 {
    color: #2c3e50;
    margin-bottom: 16px;
    font-weight: 600;
}

.code-block {
    background: #2d3748;
    color: #e2e8f0;
    padding: 20px;
    border-radius: 8px;
    overflow-x: auto;
}

.code-block pre {
    margin: 0;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
}

.tables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.table-card {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #6f42c1;
}

.table-card h6 {
    color: #2c3e50;
    margin-bottom: 8px;
    font-weight: 600;
}

.table-fields {
    background: #e9ecef;
    padding: 8px 12px;
    border-radius: 4px;
    margin-bottom: 8px;
    font-family: monospace;
    font-size: 12px;
    word-break: break-all;
}

.table-card p {
    color: #495057;
    font-size: 14px;
    margin: 0;
}

.rel-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
}

.rel-item {
    background: #fff3cd;
    padding: 12px;
    border-radius: 6px;
    border-left: 4px solid #ffc107;
    font-size: 14px;
    color: #495057;
}

/* Estilos para APIs */
.api-overview {
    background: #e8f5e9;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #4caf50;
    margin-bottom: 24px;
}

.api-overview h5 {
    color: #2e7d32;
    margin-bottom: 12px;
    font-weight: 600;
}

.api-overview p {
    color: #495057;
    margin: 0;
}

.endpoints-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.endpoint-category {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #17a2b8;
}

.endpoint-category h6 {
    color: #2c3e50;
    margin-bottom: 16px;
    font-weight: 600;
}

.endpoint-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.endpoint-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    background: white;
    border-radius: 6px;
}

.method {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    min-width: 50px;
    text-align: center;
}

.method.post {
    background: #28a745;
    color: white;
}

.method.put {
    background: #ffc107;
    color: #212529;
}

.method.get {
    background: #007bff;
    color: white;
}

.method.delete {
    background: #dc3545;
    color: white;
}

.endpoint-item code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    flex: 1;
}

.endpoint-item p {
    color: #6c757d;
    font-size: 12px;
    margin: 0;
    flex: 1;
}

.api-example, .response-format {
    margin-bottom: 24px;
}

.api-example h5, .response-format h5 {
    color: #2c3e50;
    margin-bottom: 16px;
    font-weight: 600;
}

/* Estilos para Frontend */
.frontend-overview {
    background: #f3e5f5;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #9c27b0;
    margin-bottom: 24px;
}

.frontend-overview h5 {
    color: #7b1fa2;
    margin-bottom: 12px;
    font-weight: 600;
}

.frontend-overview p {
    color: #495057;
    margin: 0;
}

.ui-components {
    margin-bottom: 24px;
}

.ui-components h5 {
    color: #2c3e50;
    margin-bottom: 16px;
    font-weight: 600;
}

.components-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
}

.component-card {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #9c27b0;
}

.component-card h6 {
    color: #2c3e50;
    margin-bottom: 8px;
    font-weight: 600;
}

.component-card p {
    color: #495057;
    font-size: 14px;
    margin-bottom: 12px;
}

.component-features {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.feature-tag {
    background: #e1bee7;
    color: #7b1fa2;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.css-architecture, .js-architecture {
    margin-bottom: 24px;
}

.css-architecture h5, .js-architecture h5 {
    color: #2c3e50;
    margin-bottom: 16px;
    font-weight: 600;
}

.css-structure, .js-structure {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.css-item, .js-item {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border-left: 4px solid #ff9800;
    font-size: 14px;
    color: #495057;
}

/* Estilos para Seguridad */
.security-overview {
    background: #ffebee;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #f44336;
    margin-bottom: 24px;
}

.security-overview h5 {
    color: #c62828;
    margin-bottom: 12px;
    font-weight: 600;
}

.security-overview p {
    color: #495057;
    margin: 0;
}

.security-measures {
    margin-bottom: 24px;
}

.security-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.security-item {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #f44336;
}

.security-item h6 {
    color: #2c3e50;
    margin-bottom: 12px;
    font-weight: 600;
}

.security-item ul {
    color: #495057;
    margin: 0;
}

.security-item li {
    margin-bottom: 6px;
    font-size: 14px;
}

.security-best-practices {
    margin-bottom: 24px;
}

.security-best-practices h5 {
    color: #2c3e50;
    margin-bottom: 16px;
    font-weight: 600;
}

.practices-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.practice-item {
    background: #e8f5e9;
    padding: 12px;
    border-radius: 6px;
    border-left: 4px solid #4caf50;
    font-size: 14px;
    color: #495057;
}

.security-code-example {
    margin-bottom: 24px;
}

.security-code-example h5 {
    color: #2c3e50;
    margin-bottom: 16px;
    font-weight: 600;
}

/* Estilos para Deployment */
.deployment-overview {
    background: #e3f2fd;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #2196f3;
    margin-bottom: 24px;
}

.deployment-overview h5 {
    color: #1565c0;
    margin-bottom: 12px;
    font-weight: 600;
}

.deployment-overview p {
    color: #495057;
    margin: 0;
}

.deployment-steps {
    margin-bottom: 24px;
}

.deployment-steps h5 {
    color: #2c3e50;
    margin-bottom: 16px;
    font-weight: 600;
}

.steps-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.deploy-step {
    display: flex;
    gap: 16px;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #2196f3;
}

.deploy-step .step-number {
    background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 16px;
    flex-shrink: 0;
}

.deploy-step .step-content h6 {
    color: #2c3e50;
    margin-bottom: 12px;
    font-weight: 600;
}

.deploy-step .step-content ul {
    color: #495057;
    margin-bottom: 12px;
}

.deploy-step .step-content li {
    margin-bottom: 6px;
    font-size: 14px;
}

.production-checklist {
    margin-bottom: 24px;
}

.production-checklist h5 {
    color: #2c3e50;
    margin-bottom: 16px;
    font-weight: 600;
}

.checklist-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
}

.checklist-item {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border-left: 4px solid #28a745;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #495057;
}

.checklist-item input[type="checkbox"] {
    margin: 0;
}

.maintenance {
    margin-bottom: 24px;
}

.maintenance h5 {
    color: #2c3e50;
    margin-bottom: 16px;
    font-weight: 600;
}

.maintenance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.maintenance-item {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #ff9800;
}

.maintenance-item h6 {
    color: #2c3e50;
    margin-bottom: 12px;
    font-weight: 600;
}

.maintenance-item ul {
    color: #495057;
    margin: 0;
}

.maintenance-item li {
    margin-bottom: 6px;
    font-size: 14px;
}

@media (max-width: 768px) {
    .nav-links {
        flex-direction: column;
    }

    .stack-grid, .pattern-grid {
        grid-template-columns: 1fr;
    }

    .tables-grid {
        grid-template-columns: 1fr;
    }

    .rel-grid {
        grid-template-columns: 1fr;
    }

    .endpoints-grid {
        grid-template-columns: 1fr;
    }

    .components-grid {
        grid-template-columns: 1fr;
    }

    .security-grid {
        grid-template-columns: 1fr;
    }

    .checklist-grid {
        grid-template-columns: 1fr;
    }

    .maintenance-grid {
        grid-template-columns: 1fr;
    }

    .deploy-step {
        flex-direction: column;
        text-align: center;
    }

    .deploy-step .step-number {
        align-self: center;
    }
}
</style>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// Incluir el layout público (sin autenticación)
include 'layout_public.php';
?>
