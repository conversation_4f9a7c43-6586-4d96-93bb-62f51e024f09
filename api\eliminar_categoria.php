<?php
header('Content-Type: application/json');

// Verificar que sea una petición POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit();
}

// Iniciar sesión y conectar a la base de datos

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();
require_once '../config/db.php';

// Verificar que el usuario esté logueado y sea administrador
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden eliminar categorías']);
    exit();
}

// Obtener datos del JSON
$input = json_decode(file_get_contents('php://input'), true);
$categoria_id = $input['categoria_id'] ?? '';

// Validaciones
if (empty($categoria_id) || !is_numeric($categoria_id)) {
    echo json_encode(['success' => false, 'message' => 'ID de categoría inválido']);
    exit();
}

try {
    // Verificar que la categoría existe
    $stmt = $pdo->prepare("SELECT * FROM categorias WHERE id = ?");
    $stmt->execute([$categoria_id]);
    $categoria = $stmt->fetch();
    
    if (!$categoria) {
        echo json_encode(['success' => false, 'message' => 'Categoría no encontrada']);
        exit();
    }
    
    // Verificar que no tenga productos asociados
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM productos WHERE categoria_id = ?");
    $stmt->execute([$categoria_id]);
    $productos_asociados = $stmt->fetch()['total'];
    
    if ($productos_asociados > 0) {
        echo json_encode([
            'success' => false, 
            'message' => "No se puede eliminar la categoría porque tiene {$productos_asociados} producto(s) asociado(s). Primero cambia la categoría de esos productos."
        ]);
        exit();
    }
    
    // Eliminar categoría
    $stmt = $pdo->prepare("DELETE FROM categorias WHERE id = ?");
    $stmt->execute([$categoria_id]);
    
    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'message' => 'Categoría eliminada exitosamente'
    ]);
    
} catch (PDOException $e) {
    error_log("Error en eliminar_categoria.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error al eliminar la categoría: ' . $e->getMessage()
    ]);
}
?>
