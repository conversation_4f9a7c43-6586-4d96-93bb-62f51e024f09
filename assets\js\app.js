// JavaScript personalizado para el Sistema de Restaurante

// Configuración global
const RestauranteApp = {
    baseUrl: '/Restaurante',
    notificationInterval: 30000, // 30 segundos
    
    // Inicializar la aplicación
    init: function() {
        this.initNotifications();
        this.initTooltips();
        this.initConfirmDialogs();
        this.initAutoRefresh();
    },
    
    // Sistema de notificaciones
    initNotifications: function() {
        this.checkNotifications();
        setInterval(() => {
            this.checkNotifications();
        }, this.notificationInterval);
    },
    
    // Verificar notificaciones nuevas
    checkNotifications: function() {
        fetch(`${this.baseUrl}/api/notifications.php`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateNotificationBadge(data.notifications.length);
                    this.updateNotificationList(data.notifications);
                }
            })
            .catch(error => {
                console.error('Error al obtener notificaciones:', error);
            });
    },
    
    // Actualizar badge de notificaciones
    updateNotificationBadge: function(count) {
        const badge = document.getElementById('notification-count');
        if (badge) {
            badge.textContent = count;
            badge.style.display = count > 0 ? 'inline' : 'none';
        }
    },
    
    // Actualizar lista de notificaciones
    updateNotificationList: function(notifications) {
        const list = document.getElementById('notifications-list');
        if (!list) return;

        if (notifications.length === 0) {
            list.innerHTML = `
                <a href="#" class="dropdown-item">
                    <i class="bi bi-info-circle me-2"></i> No hay notificaciones nuevas
                </a>
            `;
            return;
        }

        let html = '';
        notifications.forEach(notification => {
            const iconClass = notification.icon || this.getNotificationIcon(notification.type);
            const time = this.formatTime(notification.timestamp || notification.fecha_hora || new Date());
            const typeClass = this.getNotificationTypeClass(notification.type);

            html += `
                <a href="#" class="dropdown-item notification-item ${notification.priority === 'high' ? 'border-start border-danger border-3' : ''}">
                    <div class="d-flex align-items-start">
                        <i class="bi ${iconClass} me-2 text-${typeClass}"></i>
                        <div class="flex-grow-1">
                            <strong class="d-block">${notification.title || notification.titulo || 'Sin título'}</strong>
                            <small class="text-muted d-block">${notification.message || notification.mensaje || 'Sin mensaje'}</small>
                            <small class="text-muted"><i class="bi bi-clock me-1"></i>${time}</small>
                        </div>
                    </div>
                </a>
            `;
        });

        // Agregar enlace para ver todas las notificaciones
        html += `
            <div class="dropdown-divider"></div>
            <a href="#" class="dropdown-item text-center text-muted">
                <small>Ver todas las notificaciones</small>
            </a>
        `;

        list.innerHTML = html;
    },
    
    // Obtener icono según tipo de notificación
    getNotificationIcon: function(type) {
        const icons = {
            'success': 'bi-check-circle',
            'warning': 'bi-exclamation-triangle',
            'danger': 'bi-exclamation-circle',
            'info': 'bi-info-circle',
            // Compatibilidad con tipos antiguos
            'nueva_orden': 'bi-plus-circle',
            'orden_lista': 'bi-check-circle',
            'orden_servida': 'bi-check2-all',
            'inventario_bajo': 'bi-exclamation-triangle',
            'sistema': 'bi-gear'
        };
        return icons[type] || 'bi-bell';
    },

    // Obtener clase de color según tipo de notificación
    getNotificationTypeClass: function(type) {
        const classes = {
            'success': 'success',
            'warning': 'dark',
            'danger': 'danger',
            'info': 'info',
            // Compatibilidad con tipos antiguos
            'nueva_orden': 'primary',
            'orden_lista': 'success',
            'orden_servida': 'success',
            'inventario_bajo': 'dark',
            'sistema': 'secondary'
        };
        return classes[type] || 'secondary';
    },
    
    // Formatear tiempo relativo
    formatTime: function(datetime) {
        const now = new Date();
        const time = new Date(datetime);
        const diff = Math.floor((now - time) / 1000);
        
        if (diff < 60) return 'Hace un momento';
        if (diff < 3600) return `Hace ${Math.floor(diff / 60)} min`;
        if (diff < 86400) return `Hace ${Math.floor(diff / 3600)} h`;
        return `Hace ${Math.floor(diff / 86400)} días`;
    },
    
    // Inicializar tooltips
    initTooltips: function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },
    
    // Inicializar diálogos de confirmación
    initConfirmDialogs: function() {
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('confirm-delete')) {
                e.preventDefault();
                const message = e.target.getAttribute('data-message') || '¿Estás seguro de que quieres eliminar este elemento?';

                // Crear modal de confirmación elegante
                const confirmModal = document.createElement('div');
                confirmModal.className = 'modal fade';
                confirmModal.innerHTML = `
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header border-0">
                                <h5 class="modal-title">
                                    <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                                    Confirmar Eliminación
                                </h5>
                            </div>
                            <div class="modal-body">
                                <p class="mb-0">${message}</p>
                            </div>
                            <div class="modal-footer border-0">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                                <button type="button" class="btn btn-danger" id="confirm-delete-btn">Eliminar</button>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(confirmModal);
                const modal = new bootstrap.Modal(confirmModal);
                modal.show();

                // Manejar confirmación
                document.getElementById('confirm-delete-btn').addEventListener('click', function() {
                    window.location.href = e.target.href;
                });

                // Limpiar modal al cerrarse
                confirmModal.addEventListener('hidden.bs.modal', function() {
                    confirmModal.remove();
                });
            }
        });
    },
    
    // Auto-refresh para páginas específicas
    initAutoRefresh: function() {
        const autoRefreshPages = ['ordenes.php', 'ver_ordenes.php', 'preparacion.php'];
        const currentPage = window.location.pathname.split('/').pop();

        if (autoRefreshPages.includes(currentPage)) {
            setInterval(() => {
                this.refreshOrdersTable();
            }, 60000); // Refresh cada minuto
        }
    },
    
    // Refrescar tabla de órdenes
    refreshOrdersTable: function() {
        const table = document.querySelector('.tabla-ordenes tbody');
        if (!table) return;
        
        fetch(window.location.href + '?ajax=1')
            .then(response => response.text())
            .then(html => {
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const newTable = doc.querySelector('.tabla-ordenes tbody');
                
                if (newTable) {
                    table.innerHTML = newTable.innerHTML;
                    this.showToast('Órdenes actualizadas', 'success');
                }
            })
            .catch(error => {
                console.error('Error al actualizar órdenes:', error);
            });
    },
    
    // Mostrar notificación moderna y bonita
    showToast: function(message, type = 'info', duration = 4000) {
        // Crear contenedor si no existe
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            toastContainer.style.maxWidth = '400px';
            document.body.appendChild(toastContainer);
        }

        // Mapear tipos a configuraciones
        const typeConfig = {
            'success': {
                bgClass: 'bg-success',
                icon: 'bi-check-circle-fill',
                title: 'Éxito'
            },
            'error': {
                bgClass: 'bg-danger',
                icon: 'bi-exclamation-triangle-fill',
                title: 'Error'
            },
            'warning': {
                bgClass: 'bg-dark',
                icon: 'bi-exclamation-triangle-fill',
                title: 'Advertencia'
            },
            'info': {
                bgClass: 'bg-info',
                icon: 'bi-info-circle-fill',
                title: 'Información'
            }
        };

        const config = typeConfig[type] || typeConfig['info'];
        const toastId = 'toast-' + Date.now() + Math.random().toString(36).substr(2, 9);

        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white ${config.bgClass} border-0 shadow-lg mb-2" role="alert" style="border-radius: 12px; backdrop-filter: blur(10px);">
                <div class="d-flex">
                    <div class="toast-body d-flex align-items-center">
                        <i class="bi ${config.icon} me-2 fs-5"></i>
                        <div>
                            <div class="fw-bold small">${config.title}</div>
                            <div>${message}</div>
                        </div>
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Cerrar"></button>
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        const toastElement = document.getElementById(toastId);

        // Animación de entrada
        toastElement.style.transform = 'translateX(100%)';
        toastElement.style.opacity = '0';

        // Usar setTimeout para permitir que el elemento se renderice
        setTimeout(() => {
            toastElement.style.transition = 'all 0.3s ease-out';
            toastElement.style.transform = 'translateX(0)';
            toastElement.style.opacity = '1';
        }, 10);

        // Auto-ocultar después del tiempo especificado
        setTimeout(() => {
            this.hideToast(toastElement);
        }, duration);

        // Permitir cerrar manualmente
        const closeBtn = toastElement.querySelector('.btn-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hideToast(toastElement);
            });
        }

        return toastElement;
    },

    // Ocultar notificación con animación
    hideToast: function(toastElement) {
        if (!toastElement || !toastElement.parentNode) return;

        toastElement.style.transition = 'all 0.3s ease-in';
        toastElement.style.transform = 'translateX(100%)';
        toastElement.style.opacity = '0';

        setTimeout(() => {
            if (toastElement.parentNode) {
                toastElement.remove();
            }
        }, 300);
    },
    
    // Cambiar estado de orden
    cambiarEstadoOrden: function(ordenId, nuevoEstado) {
        const formData = new FormData();
        formData.append('orden_id', ordenId);
        formData.append('nuevo_estado', nuevoEstado);
        
        fetch(`${this.baseUrl}/api/cambiar_estado_orden.php`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showToast('Estado actualizado correctamente', 'success');
                this.refreshOrdersTable();
            } else {
                this.showToast('Error al actualizar estado: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.showToast('Error de conexión', 'danger');
        });
    }
};

// Funciones globales de utilidad
function formatCurrency(amount) {
    return new Intl.NumberFormat('es-GT', {
        style: 'currency',
        currency: 'GTQ',
        minimumFractionDigits: 2
    }).format(amount);
}

function formatDateTime(datetime) {
    return new Intl.DateTimeFormat('es-GT', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    }).format(new Date(datetime));
}

// Función global para mostrar toasts
function showToast(message, type = 'info') {
    RestauranteApp.showToast(message, type);
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    RestauranteApp.init();
});
