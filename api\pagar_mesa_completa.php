<?php
header('Content-Type: application/json');
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Verificar que sea un mesero o administrador
require_once '../config/db.php';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol IN ('mesero', 'admin')");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo meseros y administradores pueden pagar mesas']);
    exit();
}

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);
$hash_mesa = $input['hash_mesa'] ?? null;

if (!$hash_mesa) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Hash de mesa inválido']);
    exit();
}

try {
    // Verificar que la mesa existe y está ocupada
    $stmt = $pdo->prepare("SELECT * FROM mesas WHERE hash_mesa = ? AND estado = 'ocupada'");
    $stmt->execute([$hash_mesa]);
    $mesa = $stmt->fetch();
    
    if (!$mesa) {
        echo json_encode(['success' => false, 'message' => 'Mesa no encontrada o no está ocupada']);
        exit();
    }
    
    // Verificar que el mesero tenga acceso a esta mesa (si no es admin)
    if ($usuario['rol'] === 'mesero' && $mesa['mesero_id'] != $usuario['id']) {
        echo json_encode(['success' => false, 'message' => 'No tienes permisos para esta mesa']);
        exit();
    }
    
    // Iniciar transacción
    $pdo->beginTransaction();
    
    // Obtener todas las cuentas abiertas de la mesa
    $stmt = $pdo->prepare("
        SELECT id, nombre_cliente, apellido_cliente, total, estado 
        FROM cuentas 
        WHERE hash_mesa = ? AND estado = 'abierta'
    ");
    $stmt->execute([$hash_mesa]);
    $cuentas_abiertas = $stmt->fetchAll();
    
    if (empty($cuentas_abiertas)) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'No hay cuentas abiertas para pagar en esta mesa']);
        exit();
    }
    
    // Calcular total de la mesa
    $total_mesa = 0;
    $cuentas_procesadas = 0;
    $detalles_cuentas = [];
    
    foreach ($cuentas_abiertas as $cuenta) {
        $total_mesa += $cuenta['total'];
        $cuentas_procesadas++;
        
        $detalles_cuentas[] = [
            'id' => $cuenta['id'],
            'cliente' => trim($cuenta['nombre_cliente'] . ' ' . $cuenta['apellido_cliente']),
            'total' => $cuenta['total']
        ];
        
        // Marcar cuenta como pagada
        $stmt = $pdo->prepare("UPDATE cuentas SET estado = 'pagada', fecha_actualizacion = NOW() WHERE id = ?");
        $stmt->execute([$cuenta['id']]);
        
        // Finalizar todas las órdenes de esta cuenta
        $stmt = $pdo->prepare("UPDATE ordenes SET estado = 'finalizada' WHERE cuenta_id = ?");
        $stmt->execute([$cuenta['id']]);
        
        // Marcar todos los productos como servidos
        $stmt = $pdo->prepare("
            UPDATE detalle_orden 
            SET estado = 'servido' 
            WHERE orden_id IN (SELECT id FROM ordenes WHERE cuenta_id = ?) 
            AND estado != 'servido'
        ");
        $stmt->execute([$cuenta['id']]);
    }
    
    // Verificar si todas las cuentas de la mesa están pagadas
    $stmt = $pdo->prepare("SELECT COUNT(*) as cuentas_pendientes FROM cuentas WHERE hash_mesa = ? AND estado != 'pagada'");
    $stmt->execute([$hash_mesa]);
    $pendientes = $stmt->fetch();
    
    $mesa_completamente_pagada = ($pendientes['cuentas_pendientes'] == 0);

    // Si todas las cuentas están pagadas, cerrar la mesa automáticamente
    if ($mesa_completamente_pagada) {
        $stmt = $pdo->prepare("UPDATE mesas SET estado = 'libre', mesero_id = NULL, fecha_apertura = NULL WHERE hash_mesa = ?");
        $stmt->execute([$hash_mesa]);
    }

    // Confirmar transacción
    $pdo->commit();
    
    // Preparar respuesta
    $mensaje = $mesa_completamente_pagada ? 'Mesa pagada y cerrada automáticamente' : 'Mesa pagada exitosamente';
    $response = [
        'success' => true,
        'message' => $mensaje,
        'mesa_numero' => $mesa['numero_mesa'],
        'hash_mesa' => $hash_mesa,
        'total_mesa' => $total_mesa,
        'total_formateado' => number_format($total_mesa, 0),
        'cuentas_procesadas' => $cuentas_procesadas,
        'detalles_cuentas' => $detalles_cuentas,
        'mesa_completamente_pagada' => $mesa_completamente_pagada,
        'procesado_por' => $usuario['nombre'],
        'fecha_pago' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($response);
    
} catch (PDOException $e) {
    // Revertir transacción en caso de error
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Error de base de datos: ' . $e->getMessage(),
        'error_code' => $e->getCode()
    ]);
}
?>
