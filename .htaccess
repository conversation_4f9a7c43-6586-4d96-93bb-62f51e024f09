# Configuración básica de seguridad para Sistema de Restaurante
# Compatible con XAMPP/Apache en Windows

# Habilitar el motor de reescritura
RewriteEngine On

# Denegar acceso directo a listados de directorios
Options -Indexes

# Proteger archivos sensibles básicos
<Files "*.sql">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files ".htaccess">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.config">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

# Redirecciones básicas para carpetas protegidas
RewriteRule ^api/?$ /Restaurante/index.php [R=302,L]
RewriteRule ^config/?$ /Restaurante/index.php [R=302,L]
RewriteRule ^includes/?$ /Restaurante/index.php [R=302,L]