<?php
// Documentación pública - No requiere autenticación
// Permitir acceso sin login para facilitar la implementación del sistema

// Variables para el layout
$page_title = 'Documentación del Sistema';
$breadcrumbs = [
    ['title' => 'Documentación']
];

// Iniciar captura de contenido
ob_start();
?>

<!-- Header de Documentación -->
<div class="docs-header-container mb-4">
    <div class="docs-header">
        <div class="docs-title">
            <span class="docs-icon">📚</span>
            <h2>Documentación del Sistema de Restaurante</h2>
        </div>
        <div class="docs-subtitle">
            <p>Guías completas para usuarios y desarrolladores</p>
        </div>
    </div>
</div>

<!-- Navegación de Documentación -->
<div class="docs-navigation-container mb-4">
    <div class="docs-nav-header">
        <h4>📖 Selecciona el tipo de documentación</h4>
    </div>
    
    <div class="docs-nav-grid">
        <!-- Documentación de Usuario -->
        <div class="docs-nav-card">
            <div class="docs-nav-icon">
                <span class="nav-emoji">👤</span>
            </div>
            <div class="docs-nav-content">
                <h5>Manual de Usuario</h5>
                <p>Guía completa para usar el sistema según tu rol</p>
                <ul class="docs-nav-features">
                    <li>🍽️ Guía para Meseros</li>
                    <li>👨‍🍳 Guía para Cocina</li>
                    <li>🥤 Guía para Bebidas</li>
                    <li>⚙️ Guía para Administradores</li>
                </ul>
                <a href="/Restaurante/docs/usuario.php" class="btn-docs-nav">
                    📖 Ver Manual de Usuario
                </a>
            </div>
        </div>
        
        <!-- Documentación de Desarrollador -->
        <div class="docs-nav-card">
            <div class="docs-nav-icon">
                <span class="nav-emoji">💻</span>
            </div>
            <div class="docs-nav-content">
                <h5>Documentación Técnica</h5>
                <p>Información técnica para desarrolladores</p>
                <ul class="docs-nav-features">
                    <li>🏗️ Arquitectura del Sistema</li>
                    <li>🗄️ Base de Datos</li>
                    <li>🔌 APIs y Endpoints</li>
                    <li>🎨 Componentes UI</li>
                </ul>
                <a href="/Restaurante/docs/desarrollador.php" class="btn-docs-nav">
                    🔧 Ver Documentación Técnica
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Información General del Sistema -->
<div class="docs-info-container">
    <div class="docs-info-header">
        <h4>ℹ️ Información General del Sistema</h4>
    </div>
    
    <div class="docs-info-grid">
        <div class="info-card">
            <div class="info-icon">🏪</div>
            <div class="info-content">
                <h6>Sistema de Restaurante</h6>
                <p>Plataforma completa para gestión de restaurantes con roles específicos y flujo de trabajo optimizado.</p>
            </div>
        </div>
        
        <div class="info-card">
            <div class="info-icon">👥</div>
            <div class="info-content">
                <h6>Roles del Sistema</h6>
                <p>Administrador, Mesero, Cocina y Bebidas con permisos y funcionalidades específicas.</p>
            </div>
        </div>
        
        <div class="info-card">
            <div class="info-icon">📱</div>
            <div class="info-content">
                <h6>Responsive Design</h6>
                <p>Optimizado para dispositivos móviles, tablets y computadoras de escritorio.</p>
            </div>
        </div>
        
        <div class="info-card">
            <div class="info-icon">🔄</div>
            <div class="info-content">
                <h6>Tiempo Real</h6>
                <p>Actualizaciones en tiempo real del estado de órdenes y productos.</p>
            </div>
        </div>
    </div>
</div>

<!-- Accesos Rápidos -->
<div class="docs-quick-access-container">
    <div class="docs-quick-header">
        <h4>🚀 Accesos Rápidos</h4>
    </div>
    
    <div class="quick-access-grid">
        <a href="/Restaurante/docs/usuario.php#meseros" class="quick-access-item">
            <span class="quick-icon">🍽️</span>
            <span class="quick-text">Guía de Meseros</span>
        </a>
        
        <a href="/Restaurante/docs/usuario.php#cocina" class="quick-access-item">
            <span class="quick-icon">👨‍🍳</span>
            <span class="quick-text">Guía de Cocina</span>
        </a>
        
        <a href="/Restaurante/docs/usuario.php#bebidas" class="quick-access-item">
            <span class="quick-icon">🥤</span>
            <span class="quick-text">Guía de Bebidas</span>
        </a>
        
        <a href="/Restaurante/docs/usuario.php#admin" class="quick-access-item">
            <span class="quick-icon">⚙️</span>
            <span class="quick-text">Panel Admin</span>
        </a>
        
        <a href="/Restaurante/docs/desarrollador.php#api" class="quick-access-item">
            <span class="quick-icon">🔌</span>
            <span class="quick-text">APIs</span>
        </a>
        
        <a href="/Restaurante/docs/desarrollador.php#database" class="quick-access-item">
            <span class="quick-icon">🗄️</span>
            <span class="quick-text">Base de Datos</span>
        </a>
    </div>
</div>

<style>
/* Estilos para la documentación */
.docs-header-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
    padding: 40px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.docs-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    margin-bottom: 16px;
}

.docs-icon {
    font-size: 48px;
}

.docs-title h2 {
    margin: 0;
    font-weight: 700;
}

.docs-subtitle p {
    margin: 0;
    font-size: 18px;
    opacity: 0.9;
}

.docs-navigation-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.docs-nav-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.docs-nav-header h4 {
    margin: 0;
    font-weight: 600;
}

.docs-nav-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 0;
}

.docs-nav-card {
    padding: 30px;
    border-right: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.docs-nav-card:last-child {
    border-right: none;
}

.docs-nav-card:hover {
    background: #f8f9fa;
}

.docs-nav-icon {
    text-align: center;
    margin-bottom: 20px;
}

.nav-emoji {
    font-size: 64px;
    display: block;
}

.docs-nav-content h5 {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 12px;
    text-align: center;
}

.docs-nav-content p {
    color: #6c757d;
    text-align: center;
    margin-bottom: 20px;
}

.docs-nav-features {
    list-style: none;
    padding: 0;
    margin-bottom: 24px;
}

.docs-nav-features li {
    padding: 8px 0;
    color: #495057;
    font-size: 14px;
}

.btn-docs-nav {
    display: block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: 25px;
    text-align: center;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-docs-nav:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    color: white;
    text-decoration: none;
}

.docs-info-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 24px;
}

.docs-info-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.docs-info-header h4 {
    margin: 0;
    font-weight: 600;
}

.docs-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0;
}

.info-card {
    padding: 24px;
    border-right: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
    text-align: center;
    transition: all 0.3s ease;
}

.info-card:hover {
    background: #f8f9fa;
}

.info-icon {
    font-size: 32px;
    margin-bottom: 12px;
}

.info-content h6 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 8px;
}

.info-content p {
    color: #6c757d;
    font-size: 14px;
    margin: 0;
}

.docs-quick-access-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.docs-quick-header {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.docs-quick-header h4 {
    margin: 0;
    font-weight: 600;
}

.quick-access-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0;
    padding: 20px;
}

.quick-access-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    text-decoration: none;
    color: #495057;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.quick-access-item:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    color: #495057;
    text-decoration: none;
}

.quick-icon {
    font-size: 32px;
    margin-bottom: 8px;
}

.quick-text {
    font-weight: 600;
    font-size: 14px;
    text-align: center;
}

@media (max-width: 768px) {
    .docs-nav-grid {
        grid-template-columns: 1fr;
    }
    
    .docs-nav-card {
        border-right: none;
        border-bottom: 1px solid #e9ecef;
    }
    
    .docs-nav-card:last-child {
        border-bottom: none;
    }
    
    .docs-info-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-access-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// Incluir el layout público (sin autenticación)
include 'layout_public.php';
?>
