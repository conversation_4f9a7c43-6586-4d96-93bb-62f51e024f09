<?php
// Configurar variables para el layout
$page_title = 'Órdenes de Bebidas';
$breadcrumbs = [
    ['title' => 'Inicio', 'url' => '/Restaurante/'],
    ['title' => 'Bebidas', 'url' => '/Restaurante/bebidas/'],
    ['title' => 'Órdenes']
];

// Iniciar el buffer de contenido
ob_start();

// Conectar a la base de datos
session_start();
require_once '../config/db.php';

// Verificar que sea personal de bebidas
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'bebidas'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    echo '<div class="alert alert-danger">Acceso denegado. Solo personal de bebidas puede acceder.</div>';
    $content = ob_get_clean();
    include '../includes/layout.php';
    exit();
}

// Obtener órdenes de bebidas
try {
    $stmt = $pdo->query("
        SELECT d.*, o.mesa, o.fecha_hora, o.hash_mesa, p.nombre as producto_nombre, 
               p.descripcion, u.nombre as mesero_nombre, m.numero_mesa
        FROM detalle_orden d
        INNER JOIN ordenes o ON d.orden_id = o.id
        INNER JOIN productos p ON d.producto_id = p.id
        INNER JOIN usuarios u ON o.mesero_id = u.id
        INNER JOIN mesas m ON o.hash_mesa = m.hash_mesa
        WHERE d.area = 'bebidas' AND d.estado IN ('pendiente', 'preparando')
        ORDER BY d.estado ASC, o.fecha_hora ASC
    ");
    $ordenes_bebidas = $stmt->fetchAll();
    
    // Agrupar por estado
    $pendientes = array_filter($ordenes_bebidas, function($o) { return $o['estado'] === 'pendiente'; });
    $preparando = array_filter($ordenes_bebidas, function($o) { return $o['estado'] === 'preparando'; });
    
} catch (PDOException $e) {
    $ordenes_bebidas = [];
    $pendientes = [];
    $preparando = [];
}
?>

<!-- Estadísticas de Bar -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="bg-secondary bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-clock-fill text-dark fs-4"></i>
                </div>
                <h3 class="text-dark mb-1"><?= count($pendientes) ?></h3>
                <p class="text-muted mb-0 small">Pendientes</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="bg-info bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-cup-straw text-preparando fs-4"></i>
                </div>
                <h3 class="text-preparando mb-1"><?= count($preparando) ?></h3>
                <p class="text-muted mb-0 small">Preparando</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-check-circle-fill text-success fs-4"></i>
                </div>
                <h3 class="text-success mb-1" id="listos-count">0</h3>
                <p class="text-muted mb-0 small">Listos</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <button class="btn btn-outline-primary rounded-pill w-100" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    Actualizar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bebidas Pendientes - Diseño Personalizado -->
<div class="bebidas-pendientes-container mb-4">
    <div class="bebidas-header">
        <div class="bebidas-title">
            <span class="bebidas-icon">🍹</span>
            <h4>Bebidas Pendientes (<?= count($pendientes) ?>)</h4>
        </div>
    </div>

    <div class="bebidas-content">
        <?php if (empty($pendientes)): ?>
            <div class="bebidas-empty">
                <span class="empty-icon">✅</span>
                <h5>¡Excelente! No hay bebidas pendientes</h5>
            </div>
        <?php else: ?>
            <div class="bebidas-grid">
                <?php foreach ($pendientes as $orden): ?>
                    <div class="bebida-card">
                        <div class="bebida-header">
                            <div class="mesa-info">
                                <span class="mesa-numero">Mesa #<?= $orden['numero_mesa'] ?></span>
                                <span class="mesero-nombre"><?= $orden['mesero_nombre'] ?></span>
                            </div>
                            <div class="bebida-tiempo">
                                <span class="tiempo-badge">
                                    🕐 <?= date('H:i', strtotime($orden['fecha_hora'])) ?>
                                </span>
                            </div>
                        </div>

                        <div class="bebida-producto">
                            <h5 class="producto-nombre"><?= htmlspecialchars($orden['producto_nombre']) ?></h5>
                            <p class="producto-descripcion"><?= htmlspecialchars($orden['descripcion']) ?></p>

                            <?php if (!empty($orden['notas'])): ?>
                                <div class="producto-notas">
                                    <span class="notas-badge">
                                        ⚠️ <?= htmlspecialchars($orden['notas']) ?>
                                    </span>
                                </div>
                            <?php endif; ?>

                            <div class="producto-cantidad">
                                <span class="cantidad-badge">🥤 Cantidad: <?= $orden['cantidad'] ?></span>
                            </div>
                        </div>

                        <div class="bebida-acciones">
                            <button class="btn-iniciar-bebida iniciar-preparacion-btn"
                                    data-detalle-id="<?= $orden['id'] ?>"
                                    data-producto="<?= htmlspecialchars($orden['producto_nombre']) ?>">
                                ▶️ Iniciar Preparación
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* Estilos personalizados para bebidas pendientes */
.bebidas-pendientes-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.bebidas-header {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    padding: 20px;
    border-bottom: 2px solid #90caf9;
}

.bebidas-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.bebidas-icon {
    font-size: 24px;
}

.bebidas-title h4 {
    margin: 0;
    color: #1565c0;
    font-weight: 600;
}

.bebidas-content {
    padding: 20px;
}

.bebidas-empty {
    text-align: center;
    padding: 40px 20px;
}

.empty-icon {
    font-size: 48px;
    display: block;
    margin-bottom: 16px;
}

.bebidas-empty h5 {
    color: #6c757d;
    margin: 0;
}

.bebidas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.bebida-card {
    background: #ffffff;
    border: 2px solid #e3f2fd;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
}

.bebida-card:hover {
    border-color: #2196f3;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
    transform: translateY(-2px);
}

.bebida-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.mesa-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.mesa-numero {
    font-weight: 700;
    color: #1565c0;
    font-size: 16px;
}

.mesero-nombre {
    color: #6c757d;
    font-size: 14px;
}

.tiempo-badge {
    background: #e3f2fd;
    color: #1565c0;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    border: 1px solid #90caf9;
}

.bebida-producto {
    margin-bottom: 20px;
}

.producto-nombre {
    color: #212529;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 18px;
}

.producto-descripcion {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 12px;
    line-height: 1.4;
}

.producto-notas {
    margin-bottom: 12px;
}

.notas-badge {
    background: #fff3cd;
    color: #856404;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 14px;
    display: inline-block;
    border: 1px solid #ffeaa7;
}

.producto-cantidad {
    margin-bottom: 12px;
}

.cantidad-badge {
    background: #e3f2fd;
    color: #1565c0;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.bebida-acciones {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.btn-iniciar-bebida {
    background: #2196f3;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-iniciar-bebida:hover {
    background: #1976d2;
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .bebidas-grid {
        grid-template-columns: 1fr;
    }

    .bebida-header {
        flex-direction: column;
        gap: 12px;
    }
}
</style>

<!-- Bebidas en Preparación - Diseño Personalizado -->
<div class="bebidas-preparacion-container">
    <div class="bebidas-preparacion-header">
        <div class="bebidas-preparacion-title">
            <span class="bebidas-preparacion-icon">🔥</span>
            <h4>Bebidas en Preparación (<?= count($preparando) ?>)</h4>
        </div>
    </div>

    <div class="bebidas-preparacion-content">
        <?php if (empty($preparando)): ?>
            <div class="bebidas-preparacion-empty">
                <span class="empty-icon">ℹ️</span>
                <h5>No hay bebidas en preparación</h5>
            </div>
        <?php else: ?>
            <div class="bebidas-preparacion-grid">
                <?php foreach ($preparando as $orden): ?>
                    <div class="bebida-preparacion-card">
                        <div class="bebida-preparacion-header">
                            <div class="mesa-info">
                                <span class="mesa-numero">Mesa #<?= $orden['numero_mesa'] ?></span>
                                <span class="mesero-nombre"><?= $orden['mesero_nombre'] ?></span>
                            </div>
                            <div class="bebida-preparacion-tiempo">
                                <span class="tiempo-badge">
                                    🕐 <?= date('H:i', strtotime($orden['fecha_hora'])) ?>
                                </span>
                            </div>
                        </div>

                        <div class="bebida-preparacion-producto">
                            <h5 class="producto-nombre"><?= htmlspecialchars($orden['producto_nombre']) ?></h5>
                            <p class="producto-descripcion"><?= htmlspecialchars($orden['descripcion']) ?></p>

                            <?php if (!empty($orden['notas'])): ?>
                                <div class="producto-notas">
                                    <span class="notas-badge">
                                        ⚠️ <?= htmlspecialchars($orden['notas']) ?>
                                    </span>
                                </div>
                            <?php endif; ?>

                            <div class="producto-cantidad">
                                <span class="cantidad-badge">🥤 Cantidad: <?= $orden['cantidad'] ?></span>
                            </div>
                        </div>

                        <div class="bebida-preparacion-acciones">
                            <button class="btn-marcar-listo-bebida marcar-listo-btn"
                                    data-detalle-id="<?= $orden['id'] ?>"
                                    data-producto="<?= htmlspecialchars($orden['producto_nombre']) ?>">
                                ✅ Marcar como Listo
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* Estilos personalizados para bebidas en preparación */
.bebidas-preparacion-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.bebidas-preparacion-header {
    background: linear-gradient(135deg, #e8f4fd 0%, #bbdefb 100%);
    padding: 20px;
    border-bottom: 2px solid #64b5f6;
}

.bebidas-preparacion-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.bebidas-preparacion-icon {
    font-size: 24px;
}

.bebidas-preparacion-title h4 {
    margin: 0;
    color: #1565c0;
    font-weight: 600;
}

.bebidas-preparacion-content {
    padding: 20px;
}

.bebidas-preparacion-empty {
    text-align: center;
    padding: 40px 20px;
}

.empty-icon {
    font-size: 48px;
    display: block;
    margin-bottom: 16px;
}

.bebidas-preparacion-empty h5 {
    color: #6c757d;
    margin: 0;
}

.bebidas-preparacion-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.bebida-preparacion-card {
    background: #ffffff;
    border: 2px solid #e8f4fd;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
}

.bebida-preparacion-card:hover {
    border-color: #2196f3;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
    transform: translateY(-2px);
}

.bebida-preparacion-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.mesa-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.mesa-numero {
    font-weight: 700;
    color: #1565c0;
    font-size: 16px;
}

.mesero-nombre {
    color: #6c757d;
    font-size: 14px;
}

.tiempo-badge {
    background: #e8f4fd;
    color: #1565c0;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    border: 1px solid #64b5f6;
}

.bebida-preparacion-producto {
    margin-bottom: 20px;
}

.producto-nombre {
    color: #212529;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 18px;
}

.producto-descripcion {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 12px;
    line-height: 1.4;
}

.producto-notas {
    margin-bottom: 12px;
}

.notas-badge {
    background: #fff3cd;
    color: #856404;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 14px;
    display: inline-block;
    border: 1px solid #ffeaa7;
}

.producto-cantidad {
    margin-bottom: 12px;
}

.cantidad-badge {
    background: #e8f4fd;
    color: #1565c0;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.bebida-preparacion-acciones {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.btn-marcar-listo-bebida {
    background: #4caf50;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-marcar-listo-bebida:hover {
    background: #45a049;
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .bebidas-preparacion-grid {
        grid-template-columns: 1fr;
    }

    .bebida-preparacion-header {
        flex-direction: column;
        gap: 12px;
    }
}
</style>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// JavaScript adicional
$extra_js = '
<script>
document.addEventListener("DOMContentLoaded", function() {
    // Auto-refresh cada 30 segundos
    setInterval(function() {
        location.reload();
    }, 30000);
    
    // Iniciar preparación
    document.querySelectorAll(".iniciar-preparacion-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const detalleId = this.dataset.detalleId;
            const producto = this.dataset.producto;

            cambiarEstadoDetalle(detalleId, "preparando");
        });
    });
    
    // Marcar como listo
    document.querySelectorAll(".marcar-listo-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const detalleId = this.dataset.detalleId;
            const producto = this.dataset.producto;

            cambiarEstadoDetalle(detalleId, "listo");
        });
    });
});

function cambiarEstadoDetalle(detalleId, nuevoEstado) {
    fetch("/Restaurante/api/cambiar_estado_detalle.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            detalle_id: detalleId,
            nuevo_estado: nuevoEstado
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast("Estado actualizado correctamente", "success");
            setTimeout(() => location.reload(), 1500);
        } else {
            showToast("Error: " + data.message, "error");
        }
    })
    .catch(error => {
        console.error("Error:", error);
        showToast("Error de conexión", "error");
    });
}

// Función para mostrar notificaciones toast
function showToast(message, type = "info") {
    // Crear contenedor de toasts si no existe
    let toastContainer = document.getElementById("toast-container");
    if (!toastContainer) {
        toastContainer = document.createElement("div");
        toastContainer.id = "toast-container";
        toastContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            display: flex;
            flex-direction: column;
            gap: 10px;
        `;
        document.body.appendChild(toastContainer);
    }

    // Crear elemento toast
    const toast = document.createElement("div");
    const bgColor = type === "success" ? "#d4edda" : type === "error" ? "#f8d7da" : "#d1ecf1";
    const textColor = type === "success" ? "#155724" : type === "error" ? "#721c24" : "#0c5460";
    const icon = type === "success" ? "✅" : type === "error" ? "❌" : "ℹ️";

    toast.style.cssText = `
        background: ${bgColor};
        color: ${textColor};
        padding: 12px 20px;
        border-radius: 8px;
        border: 1px solid ${type === "success" ? "#c3e6cb" : type === "error" ? "#f5c6cb" : "#bee5eb"};
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        animation: slideInRight 0.3s ease-out;
        font-weight: 500;
    `;

    toast.innerHTML = `${icon} ${message}`;

    // Agregar animación CSS
    if (!document.getElementById("toast-animations")) {
        const style = document.createElement("style");
        style.id = "toast-animations";
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    toastContainer.appendChild(toast);

    // Remover después de 4 segundos
    setTimeout(() => {
        toast.style.animation = "slideOutRight 0.3s ease-in";
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 4000);
}
</script>
';

// Incluir el layout
include '../includes/layout.php';
?>
