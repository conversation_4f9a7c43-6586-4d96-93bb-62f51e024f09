<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Reporte de Compras</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .report-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1200px;
            overflow: hidden;
        }
        .report-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .report-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 300;
        }
        .report-meta {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .table-container {
            padding: 30px;
        }
        .badge-custom {
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
        }
        .print-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        @media print {
            .print-btn { display: none; }
            body { background: white; }
            .report-container { box-shadow: none; margin: 0; }
        }
    </style>
</head>
<body>
    <button class="btn btn-primary print-btn" onclick="window.print()">
        <i class="fas fa-print"></i> Imprimir
    </button>

    <div class="report-container">
        <!-- Header -->
        <div class="report-header">
            <h1><i class="fas fa-chart-line"></i> Reporte de Compras</h1>
            <p class="mb-0">Análisis detallado del período seleccionado</p>
        </div>

        <!-- Meta información -->
        <div class="report-meta">
            <div class="row">
                <div class="col-md-4">
                    <strong><i class="fas fa-calendar"></i> Período:</strong><br>
                    <?= date('d/m/Y', strtotime($data['periodo']['fecha_inicio'])) ?> - 
                    <?= date('d/m/Y', strtotime($data['periodo']['fecha_fin'])) ?>
                </div>
                <div class="col-md-4">
                    <strong><i class="fas fa-filter"></i> Filtros:</strong><br>
                    <?php if (!empty($data['filtros']['proveedor_id'])): ?>
                        Proveedor específico
                    <?php else: ?>
                        Todos los proveedores
                    <?php endif; ?>
                    <?php if (!empty($data['filtros']['estado'])): ?>
                        | Estado: <?= ucfirst($data['filtros']['estado']) ?>
                    <?php endif; ?>
                </div>
                <div class="col-md-4">
                    <strong><i class="fas fa-file-alt"></i> Tipo:</strong><br>
                    <?= ucfirst(str_replace('_', ' ', $data['tipo_reporte'])) ?>
                </div>
            </div>
        </div>

        <!-- Estadísticas principales -->
        <div class="container-fluid py-4" style="background: #f8f9fa;">
            <div class="row g-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value text-primary">
                            <i class="fas fa-shopping-cart"></i>
                            <?= number_format($data['resumen']['total_compras']) ?>
                        </div>
                        <div class="stat-label">Total Compras</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value text-success">
                            <i class="fas fa-dollar-sign"></i>
                            Q<?= number_format($data['resumen']['monto_total'], 2) ?>
                        </div>
                        <div class="stat-label">Monto Total</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value text-info">
                            <i class="fas fa-calculator"></i>
                            Q<?= number_format($data['resumen']['promedio_compra'], 2) ?>
                        </div>
                        <div class="stat-label">Promedio por Compra</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value text-warning">
                            <i class="fas fa-users"></i>
                            <?= $data['resumen']['proveedores_activos'] ?>
                        </div>
                        <div class="stat-label">Proveedores Activos</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabla de compras -->
        <div class="table-container">
            <h3><i class="fas fa-list"></i> Detalle de Compras</h3>
            
            <?php if (!empty($data['compras'])): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th><i class="fas fa-calendar"></i> Fecha</th>
                                <th><i class="fas fa-truck"></i> Proveedor</th>
                                <th><i class="fas fa-file-invoice"></i> Factura</th>
                                <th><i class="fas fa-info-circle"></i> Estado</th>
                                <th><i class="fas fa-dollar-sign"></i> Total</th>
                                <th><i class="fas fa-user"></i> Usuario</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data['compras'] as $compra): ?>
                                <?php
                                $estadoClass = $compra['estado'] === 'recibida' ? 'success' : 
                                              ($compra['estado'] === 'pendiente' ? 'warning' : 'danger');
                                $estadoIcon = $compra['estado'] === 'recibida' ? 'check-circle' : 
                                             ($compra['estado'] === 'pendiente' ? 'clock' : 'times-circle');
                                ?>
                                <tr>
                                    <td><?= date('d/m/Y', strtotime($compra['fecha_compra'])) ?></td>
                                    <td>
                                        <strong><?= htmlspecialchars($compra['proveedor_nombre']) ?></strong>
                                        <?php if ($compra['proveedor_contacto']): ?>
                                            <br><small class="text-muted"><?= htmlspecialchars($compra['proveedor_contacto']) ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= $compra['numero_factura'] ?: '<em>Sin número</em>' ?></td>
                                    <td>
                                        <span class="badge bg-<?= $estadoClass ?> badge-custom">
                                            <i class="fas fa-<?= $estadoIcon ?>"></i>
                                            <?= ucfirst($compra['estado']) ?>
                                        </span>
                                    </td>
                                    <td><strong>Q<?= number_format($compra['total'], 2) ?></strong></td>
                                    <td><?= htmlspecialchars($compra['usuario_nombre']) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    No se encontraron compras en el período seleccionado.
                </div>
            <?php endif; ?>
        </div>

        <!-- Análisis adicional -->
        <?php if (!empty($data['datos_adicionales']['por_estado'])): ?>
            <div class="table-container border-top">
                <h3><i class="fas fa-chart-pie"></i> Análisis por Estado</h3>
                <div class="row">
                    <?php foreach ($data['datos_adicionales']['por_estado'] as $estado): ?>
                        <div class="col-md-4 mb-3">
                            <div class="stat-card">
                                <div class="stat-value text-secondary">
                                    <?= $estado['cantidad'] ?>
                                </div>
                                <div class="stat-label">
                                    <?= ucfirst($estado['estado']) ?><br>
                                    <small>Q<?= number_format($estado['monto_total'], 2) ?></small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Footer -->
        <div class="report-meta text-center">
            <small class="text-muted">
                <i class="fas fa-clock"></i>
                Reporte generado el <?= date('d/m/Y H:i:s') ?>
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
