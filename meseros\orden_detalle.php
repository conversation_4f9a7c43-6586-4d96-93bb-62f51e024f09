<?php
// Verificar autenticación y rol
require_once '../config/session_config.php';
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    header('Location: ' . url('auth/login.php'));
    exit();
}

// Obtener ID de la orden
$orden_id = $_GET['id'] ?? null;

if (!$orden_id) {
    header('Location: /Restaurante/meseros/ver_ordenes.php');
    exit();
}

require_once '../config/db.php';

// Verificar que sea mesero, admin, cocina o bebidas
$is_admin = isset($_GET['admin']) && $_GET['admin'] == '1';
$area_filtro = $_GET['area'] ?? null; // cocina o bebidas
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol IN ('mesero', 'admin', 'cocina', 'bebidas')");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    header('Location: ' . url('auth/login.php'));
    exit();
}

// Determinar permisos según el rol
$es_admin = ($usuario['rol'] === 'admin');
$es_mesero = ($usuario['rol'] === 'mesero');
$es_cocina = ($usuario['rol'] === 'cocina');
$es_bebidas = ($usuario['rol'] === 'bebidas');

// Validar que el área solicitada coincida con el rol (para cocina/bebidas)
if (($es_cocina && $area_filtro !== 'cocina') || ($es_bebidas && $area_filtro !== 'bebidas')) {
    header('Location: /Restaurante/index.php');
    exit();
}

// Obtener información de la orden
try {
    if ($es_admin) {
        // Admin puede ver cualquier orden
        $stmt = $pdo->prepare("
            SELECT o.*, m.numero_mesa, u.nombre as mesero_nombre,
                   c.nombre_cliente, c.apellido_cliente
            FROM ordenes o
            LEFT JOIN mesas m ON o.hash_mesa = m.hash_mesa
            LEFT JOIN usuarios u ON o.mesero_id = u.id
            LEFT JOIN cuentas c ON o.cuenta_id = c.id
            WHERE o.id = ?
        ");
        $stmt->execute([$orden_id]);
    } elseif ($es_mesero) {
        // Mesero solo puede ver sus órdenes
        $stmt = $pdo->prepare("
            SELECT o.*, m.numero_mesa, u.nombre as mesero_nombre,
                   c.nombre_cliente, c.apellido_cliente
            FROM ordenes o
            LEFT JOIN mesas m ON o.hash_mesa = m.hash_mesa
            LEFT JOIN usuarios u ON o.mesero_id = u.id
            LEFT JOIN cuentas c ON o.cuenta_id = c.id
            WHERE o.id = ? AND o.mesero_id = ?
        ");
        $stmt->execute([$orden_id, $_SESSION['usuario_id']]);
    } else {
        // Cocina/Bebidas pueden ver órdenes que tengan productos de su área
        $stmt = $pdo->prepare("
            SELECT DISTINCT o.*, m.numero_mesa, u.nombre as mesero_nombre,
                   c.nombre_cliente, c.apellido_cliente
            FROM ordenes o
            LEFT JOIN mesas m ON o.hash_mesa = m.hash_mesa
            LEFT JOIN usuarios u ON o.mesero_id = u.id
            LEFT JOIN cuentas c ON o.cuenta_id = c.id
            INNER JOIN detalle_orden d ON o.id = d.orden_id
            WHERE o.id = ? AND d.area = ?
        ");
        $stmt->execute([$orden_id, $area_filtro]);
    }

    $orden = $stmt->fetch();

    if (!$orden) {
        if ($es_admin) {
            header('Location: /Restaurante/index.php');
        } elseif ($es_mesero) {
            header('Location: /Restaurante/meseros/ver_ordenes.php');
        } else {
            header('Location: /Restaurante/index.php');
        }
        exit();
    }

    // Obtener detalles de productos de la orden
    $stmt = $pdo->prepare("
        SELECT d.*, p.nombre as producto_nombre, p.descripcion as producto_descripcion, p.area
        FROM detalle_orden d
        INNER JOIN productos p ON d.producto_id = p.id
        WHERE d.orden_id = ?
        ORDER BY d.id ASC
    ");
    $stmt->execute([$orden_id]);
    $productos = $stmt->fetchAll();

    // Separar productos por área para cocina/bebidas
    if ($es_cocina || $es_bebidas) {
        $productos_mi_area = array_filter($productos, function($p) use ($area_filtro) {
            return $p['area'] === $area_filtro;
        });
        $productos_otra_area = array_filter($productos, function($p) use ($area_filtro) {
            return $p['area'] !== $area_filtro;
        });
    } else {
        $productos_mi_area = $productos;
        $productos_otra_area = [];
    }

    // Calcular estadísticas (solo de productos de mi área para cocina/bebidas)
    $productos_para_stats = ($es_cocina || $es_bebidas) ? $productos_mi_area : $productos;
    $total_productos = count($productos_para_stats);
    $pendientes = array_filter($productos_para_stats, function($p) { return $p['estado'] === 'pendiente'; });
    $preparando = array_filter($productos_para_stats, function($p) { return $p['estado'] === 'preparando'; });
    $listos = array_filter($productos_para_stats, function($p) { return $p['estado'] === 'listo'; });
    $servidos = array_filter($productos_para_stats, function($p) { return $p['estado'] === 'servido'; });

} catch (PDOException $e) {
    header('Location: /Restaurante/meseros/ver_ordenes.php');
    exit();
}

// Configurar variables para el layout según el rol
if ($es_cocina) {
    $page_title = "Orden #$orden_id - Productos de Cocina";
    $breadcrumbs = [
        ['title' => 'Inicio', 'url' => url('')],
        ['title' => 'Cocina', 'url' => url('cocina/')],
        ['title' => "Orden #$orden_id"]
    ];
} elseif ($es_bebidas) {
    $page_title = "Orden #$orden_id - Productos de Bebidas";
    $breadcrumbs = [
        ['title' => 'Inicio', 'url' => url('')],
        ['title' => 'Bebidas', 'url' => url('bebidas/')],
        ['title' => "Orden #$orden_id"]
    ];
} elseif ($es_admin) {
    $page_title = "Detalle de Orden #$orden_id (Admin)";
    $breadcrumbs = [
        ['title' => 'Inicio', 'url' => url('')],
        ['title' => 'Administración', 'url' => url('admin/')],
        ['title' => "Orden #$orden_id"]
    ];
} else {
    $page_title = "Detalle de Orden #$orden_id";
    $breadcrumbs = [
        ['title' => 'Inicio', 'url' => url('')],
        ['title' => 'Meseros', 'url' => url('meseros/')],
        ['title' => 'Órdenes', 'url' => url('meseros/ver_ordenes.php')],
        ['title' => "Orden #$orden_id"]
    ];
}

// Iniciar el buffer de contenido
ob_start();
?>

<!-- Información de la Orden -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-receipt me-2"></i>
                        Orden #<?= $orden['id'] ?>
                    </h5>
                    <span class="badge bg-<?= $orden['estado'] === 'finalizada' ? 'success' : ($orden['estado'] === 'pendiente' ? 'dark' : 'info') ?> fs-6">
                        <?= ucfirst($orden['estado']) ?>
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <strong class="text-muted d-block">Mesa</strong>
                        <span class="fs-5">#<?= $orden['numero_mesa'] ?? 'N/A' ?></span>
                    </div>
                    <div class="col-md-3">
                        <strong class="text-muted d-block">Fecha y Hora</strong>
                        <span><?= date('d/m/Y H:i', strtotime($orden['fecha_hora'])) ?></span>
                    </div>
                    <div class="col-md-3">
                        <strong class="text-muted d-block">Total</strong>
                        <span class="fs-5 text-primary fw-bold">Q<?= number_format($orden['total'], 0) ?></span>
                    </div>
                    <div class="col-md-3">
                        <strong class="text-muted d-block">Mesero</strong>
                        <span><?= htmlspecialchars($orden['mesero_nombre']) ?></span>
                    </div>
                    <?php if ($orden['nombre_cliente']): ?>
                    <div class="col-md-6">
                        <strong class="text-muted d-block">Cliente</strong>
                        <span><?= htmlspecialchars($orden['nombre_cliente']) ?>
                        <?= $orden['apellido_cliente'] ? ' ' . htmlspecialchars($orden['apellido_cliente']) : '' ?></span>
                    </div>
                    <?php endif; ?>

                </div>
            </div>
        </div>
    </div>
</div>

<!-- Estadísticas de Productos -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <div class="text-muted mb-2">Total Productos</div>
                <div class="fs-3 fw-bold text-primary"><?= $total_productos ?></div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <div class="text-muted mb-2">Pendientes</div>
                <div class="fs-3 fw-bold text-dark"><?= count($pendientes) ?></div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <div class="text-muted mb-2">En Preparación</div>
                <div class="fs-3 fw-bold text-preparando"><?= count($preparando) ?></div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <div class="text-muted mb-2">Listos</div>
                <div class="fs-3 fw-bold text-listo"><?= count($listos) ?></div>
            </div>
        </div>
    </div>
</div>

<!-- Productos de Mi Área -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="bi <?= $es_cocina ? 'bi-egg-fried' : ($es_bebidas ? 'bi-cup-straw' : 'bi-list-ul') ?> me-2"></i>
                    <?php if ($es_cocina): ?>
                        Productos de Cocina
                    <?php elseif ($es_bebidas): ?>
                        Productos de Bebidas
                    <?php else: ?>
                        Productos de la Orden
                    <?php endif; ?>
                    <?php if ($es_cocina || $es_bebidas): ?>
                        <span class="badge bg-<?= $es_cocina ? 'danger' : 'info' ?> ms-2"><?= count($productos_mi_area) ?></span>
                    <?php endif; ?>
                </h5>
            </div>
            <div class="card-body p-0">
                <?php
                $productos_a_mostrar = ($es_cocina || $es_bebidas) ? $productos_mi_area : $productos;
                if (empty($productos_a_mostrar)): ?>
                    <div class="text-center py-5 text-muted">
                        <i class="bi bi-inbox fs-1 d-block mb-3"></i>
                        <p><?php if ($es_cocina || $es_bebidas): ?>
                            No hay productos de <?= $es_cocina ? 'cocina' : 'bebidas' ?> en esta orden
                        <?php else: ?>
                            No hay productos en esta orden
                        <?php endif; ?></p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Producto</th>
                                    <th>Área</th>
                                    <th>Cantidad</th>
                                    <th>Precio Unit.</th>
                                    <th>Subtotal</th>
                                    <th>Estado</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($productos_a_mostrar as $producto): ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><?= htmlspecialchars($producto['producto_nombre']) ?></strong>
                                                <?php if ($producto['producto_descripcion']): ?>
                                                    <br><small class="text-muted"><?= htmlspecialchars($producto['producto_descripcion']) ?></small>
                                                <?php endif; ?>
                                                <?php if (!empty($producto['notas'])): ?>
                                                    <br><div class="mt-1">
                                                        <span class="badge bg-info bg-opacity-10 text-info">
                                                            <i class="bi bi-chat-left-text me-1"></i>
                                                            <?= htmlspecialchars($producto['notas']) ?>
                                                        </span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $producto['area'] === 'cocina' ? 'danger' : 'info' ?> bg-opacity-10 text-<?= $producto['area'] === 'cocina' ? 'danger' : 'info' ?>">
                                                <i class="bi <?= $producto['area'] === 'cocina' ? 'bi-egg-fried' : 'bi-cup-straw' ?> me-1"></i>
                                                <?= ucfirst($producto['area']) ?>
                                            </span>
                                        </td>
                                        <td><span class="fw-semibold"><?= $producto['cantidad'] ?></span></td>
                                        <td>$<?= number_format($producto['precio_unitario'], 0) ?></td>
                                        <td class="fw-semibold">$<?= number_format($producto['subtotal'], 0) ?></td>
                                        <td>
                                            <?php
                                            $estado_colors = [
                                                'pendiente' => 'pendiente',
                                                'preparando' => 'preparando',
                                                'listo' => 'listo',
                                                'servido' => 'servido'
                                            ];
                                            $color = $estado_colors[$producto['estado']] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?= $color ?>"><?= ucfirst($producto['estado']) ?></span>
                                        </td>
                                        <td>
                                            <?php
                                            // Solo meseros y admins pueden marcar como servido
                                            // Cocina/Bebidas no pueden interactuar con productos
                                            if (($es_mesero || $es_admin) && $producto['estado'] === 'listo' && $orden['estado'] !== 'finalizada'): ?>
                                                <button class="btn btn-sm btn-outline-success rounded-pill marcar-servido-btn"
                                                        data-detalle="<?= $producto['id'] ?>">
                                                    <i class="bi bi-check me-1"></i>
                                                    Servido
                                                </button>
                                            <?php else: ?>
                                                <span class="text-muted small">-</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if (($es_cocina || $es_bebidas) && !empty($productos_otra_area)): ?>
<!-- Otros Productos (Solo Informativo) -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light border-bottom">
                <h5 class="mb-0 text-muted">
                    <i class="bi bi-info-circle me-2"></i>
                    Otros Productos (Solo Informativo)
                    <span class="badge bg-secondary ms-2"><?= count($productos_otra_area) ?></span>
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Producto</th>
                                <th>Área</th>
                                <th>Cantidad</th>
                                <th>Estado</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($productos_otra_area as $producto): ?>
                                <tr class="table-secondary bg-opacity-25">
                                    <td>
                                        <div>
                                            <strong class="text-muted"><?= htmlspecialchars($producto['producto_nombre']) ?></strong>
                                            <?php if ($producto['producto_descripcion']): ?>
                                                <br><small class="text-muted"><?= htmlspecialchars($producto['producto_descripcion']) ?></small>
                                            <?php endif; ?>
                                            <?php if (!empty($producto['notas'])): ?>
                                                <br><div class="mt-1">
                                                    <span class="badge bg-secondary bg-opacity-10 text-secondary">
                                                        <i class="bi bi-chat-left-text me-1"></i>
                                                        <?= htmlspecialchars($producto['notas']) ?>
                                                    </span>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $producto['area'] === 'cocina' ? 'danger' : 'info' ?> bg-opacity-25 text-<?= $producto['area'] === 'cocina' ? 'danger' : 'info' ?>">
                                            <i class="bi <?= $producto['area'] === 'cocina' ? 'bi-egg-fried' : 'bi-cup-straw' ?> me-1"></i>
                                            <?= ucfirst($producto['area']) ?>
                                        </span>
                                    </td>
                                    <td><span class="fw-semibold text-muted"><?= $producto['cantidad'] ?></span></td>
                                    <td>
                                        <?php
                                        $estado_colors = [
                                            'pendiente' => 'pendiente',
                                            'preparando' => 'preparando',
                                            'listo' => 'listo',
                                            'servido' => 'servido'
                                        ];
                                        $color = $estado_colors[$producto['estado']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?= $color ?> bg-opacity-50"><?= ucfirst($producto['estado']) ?></span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Botones de Acción -->
<div class="row">
    <div class="col-12">
        <div class="d-flex gap-2 flex-wrap">
            <?php if ($es_admin): ?>
                <a href="../index.php" class="btn btn-outline-secondary rounded-pill">
                    <i class="bi bi-arrow-left me-2"></i>
                    Volver al Dashboard
                </a>
            <?php elseif ($es_cocina): ?>
                <a href="../cocina/ordenes.php" class="btn btn-outline-secondary rounded-pill">
                    <i class="bi bi-arrow-left me-2"></i>
                    Volver a Cocina
                </a>
            <?php elseif ($es_bebidas): ?>
                <a href="../bebidas/ordenes.php" class="btn btn-outline-secondary rounded-pill">
                    <i class="bi bi-arrow-left me-2"></i>
                    Volver a Bebidas
                </a>
            <?php else: ?>
                <a href="ver_ordenes.php" class="btn btn-outline-secondary rounded-pill">
                    <i class="bi bi-arrow-left me-2"></i>
                    Volver a Órdenes
                </a>
            <?php endif; ?>

            <?php if (($es_mesero || $es_admin) && $orden['hash_mesa']): ?>
                <a href="mesa_cuentas.php?hash=<?= $orden['hash_mesa'] ?>" class="btn btn-outline-primary rounded-pill">
                    <i class="bi bi-table me-2"></i>
                    Ver Mesa #<?= $orden['numero_mesa'] ?>
                </a>
            <?php endif; ?>

            <?php if (($es_mesero || $es_admin) && $orden['cuenta_id']): ?>
                <a href="cuenta_detalle.php?cuenta_id=<?= $orden['cuenta_id'] ?>" class="btn btn-outline-info rounded-pill">
                    <i class="bi bi-person me-2"></i>
                    Ver Cuenta
                </a>
            <?php endif; ?>
            
            <?php if ($orden['estado'] !== 'finalizada' && count($listos) === $total_productos && $total_productos > 0): ?>
                <button class="btn btn-success rounded-pill finalizar-orden-btn" data-orden="<?= $orden['id'] ?>">
                    <i class="bi bi-check-circle me-2"></i>
                    Finalizar Orden
                </button>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// CSS adicional
$extra_css = '
<style>
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.badge {
    font-size: 0.75rem;
}

.card {
    transition: all 0.3s ease;
}

.btn-sm {
    font-size: 0.75rem;
}

.badge.bg-info.bg-opacity-10 {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
    border-radius: 0.375rem;
    font-weight: 500;
}

.badge.bg-info.bg-opacity-10 i {
    font-size: 0.75rem;
}
</style>
';

// JavaScript adicional
$extra_js = '
<script>
document.addEventListener("DOMContentLoaded", function() {
    // Marcar productos como servidos
    document.querySelectorAll(".marcar-servido-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const detalleId = this.dataset.detalle;
            marcarComoServido(detalleId);
        });
    });
    
    // Finalizar orden
    document.querySelectorAll(".finalizar-orden-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const ordenId = this.dataset.orden;
            if (confirm("¿Finalizar esta orden? Esta acción marcará la orden como completada.")) {
                finalizarOrden(ordenId);
            }
        });
    });
});

function marcarComoServido(detalleId) {
    fetch("../api/cambiar_estado_detalle.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            detalle_id: detalleId,
            nuevo_estado: "servido"
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast("Estado actualizado correctamente", "success");
            setTimeout(() => location.reload(), 1500);
        } else {
            showToast("Error: " + data.message, "error");
        }
    })
    .catch(error => {
        console.error("Error:", error);
        showToast("Error de conexión", "error");
    });
}

function finalizarOrden(ordenId) {
    fetch("../api/finalizar_orden.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            orden_id: ordenId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Mostrar toast de éxito
            showToast("Orden finalizada exitosamente", "success");
            setTimeout(() => location.reload(), 2000);
        } else {
            showToast("Error: " + data.message, "error");
        }
    })
    .catch(error => {
        showToast("Error de conexión", "error");
    });
}
</script>
';

// Incluir el layout
include '../includes/layout.php';
?>
