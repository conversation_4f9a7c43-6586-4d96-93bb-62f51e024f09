<?php
header('Content-Type: application/json');
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Verificar que sea administrador
require_once '../config/db.php';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden cambiar disponibilidad de productos']);
    exit();
}

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);
$producto_id = $input['producto_id'] ?? null;
$disponible = $input['disponible'] ?? null;

if (!$producto_id || !is_numeric($producto_id)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID de producto inválido']);
    exit();
}

if ($disponible === null) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Estado de disponibilidad requerido']);
    exit();
}

$disponible = $disponible ? 1 : 0;

try {
    // Verificar que el producto existe
    $stmt = $pdo->prepare("SELECT * FROM productos WHERE id = ?");
    $stmt->execute([$producto_id]);
    $producto = $stmt->fetch();
    
    if (!$producto) {
        echo json_encode(['success' => false, 'message' => 'Producto no encontrado']);
        exit();
    }
    
    // Actualizar disponibilidad
    $stmt = $pdo->prepare("UPDATE productos SET disponible = ?, fecha_actualizacion = NOW() WHERE id = ?");
    $stmt->execute([$disponible, $producto_id]);
    
    $estado_texto = $disponible ? 'disponible' : 'no disponible';
    
    echo json_encode([
        'success' => true,
        'message' => "Producto marcado como {$estado_texto}",
        'producto_id' => $producto_id,
        'nombre' => $producto['nombre'],
        'disponible' => $disponible
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
