<?php
header('Content-Type: application/json');

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Verificar que sea un mesero
require_once '../config/db.php';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'mesero'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo los meseros pueden crear órdenes']);
    exit();
}

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);
$hash_mesa = $input['hash_mesa'] ?? null;
$mesa_numero = $input['mesa_numero'] ?? null;
$nombre_cuenta = $input['nombre_cuenta'] ?? 'Cuenta sin nombre';
$productos = $input['productos'] ?? [];
$total = $input['total'] ?? 0;

// Validaciones
if (!$hash_mesa || !$mesa_numero || empty($productos)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Datos incompletos']);
    exit();
}

try {
    // Verificar que la mesa existe y pertenece al mesero
    $stmt = $pdo->prepare("SELECT * FROM mesas WHERE hash_mesa = ? AND numero_mesa = ? AND mesero_id = ? AND estado = 'ocupada'");
    $stmt->execute([$hash_mesa, $mesa_numero, $usuario['id']]);
    $mesa = $stmt->fetch();
    
    if (!$mesa) {
        echo json_encode(['success' => false, 'message' => 'Mesa no encontrada o no tienes permisos']);
        exit();
    }
    
    // Iniciar transacción
    $pdo->beginTransaction();
    
    try {
        // Buscar si existe una cuenta abierta para esta mesa
        $cuenta_id = null;
        $stmt = $pdo->prepare("SELECT id FROM cuentas WHERE hash_mesa = ? AND estado = 'abierta' LIMIT 1");
        $stmt->execute([$hash_mesa]);
        $cuenta_existente = $stmt->fetch();

        if ($cuenta_existente) {
            $cuenta_id = $cuenta_existente['id'];
        }

        // Crear la orden principal (con o sin cuenta_id)
        $stmt = $pdo->prepare("
            INSERT INTO ordenes (cuenta_id, mesa, mesero_id, hash_mesa, estado, total, fecha_hora)
            VALUES (?, ?, ?, ?, 'pendiente', ?, NOW())
        ");

        $stmt->execute([
            $cuenta_id,
            $mesa_numero,
            $usuario['id'],
            $hash_mesa,
            $total
        ]);

        $orden_id = $pdo->lastInsertId();
        
        // Insertar los detalles de la orden
        foreach ($productos as $producto) {
            // Verificar que el producto existe
            $stmt = $pdo->prepare("SELECT * FROM productos WHERE id = ?");
            $stmt->execute([$producto['id']]);
            $producto_db = $stmt->fetch();
            
            if (!$producto_db) {
                throw new Exception("Producto con ID {$producto['id']} no encontrado");
            }
            
            // Usar el área del producto directamente
            $area = $producto_db['area'];
            
            // Calcular subtotal
            $cantidad = $producto['cantidad'];
            $precio_unitario = $producto_db['precio_venta'] ?? $producto_db['precio'] ?? 0;
            $subtotal = $precio_unitario * $cantidad;

            // Obtener notas del producto (si las hay)
            $notas = isset($producto['notas']) ? trim($producto['notas']) : '';

            // Insertar detalle de orden
            $stmt = $pdo->prepare("
                INSERT INTO detalle_orden (orden_id, producto_id, cantidad, precio_unitario, subtotal, area, estado, notas)
                VALUES (?, ?, ?, ?, ?, ?, 'pendiente', ?)
            ");

            $stmt->execute([
                $orden_id,
                $producto['id'],
                $cantidad,
                $precio_unitario,
                $subtotal,
                $area,
                $notas
            ]);
        }

        // Si se asoció con una cuenta, actualizar su total
        if ($cuenta_id) {
            $stmt = $pdo->prepare("
                UPDATE cuentas SET total = (
                    SELECT COALESCE(SUM(d.subtotal), 0)
                    FROM ordenes o
                    LEFT JOIN detalle_orden d ON o.id = d.orden_id
                    WHERE o.cuenta_id = ?
                )
                WHERE id = ?
            ");
            $stmt->execute([$cuenta_id, $cuenta_id]);
        }

        // Confirmar transacción
        $pdo->commit();
        
        $mensaje = 'Orden creada exitosamente';
        if ($cuenta_id) {
            $mensaje .= ' y asociada con cuenta existente';
        }

        echo json_encode([
            'success' => true,
            'message' => $mensaje,
            'orden_id' => $orden_id,
            'cuenta_id' => $cuenta_id,
            'mesa' => $mesa_numero,
            'total' => $total,
            'productos_count' => count($productos)
        ]);
        
    } catch (Exception $e) {
        // Revertir transacción en caso de error
        $pdo->rollBack();
        throw $e;
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
