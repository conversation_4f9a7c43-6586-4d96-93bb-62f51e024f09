<?php
header('Content-Type: application/json');

// Iniciar sesión y conectar a la base de datos
session_start();
require_once '../config/db.php';

// Verificar que el usuario esté logueado y sea administrador
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden acceder a reportes']);
    exit();
}

// Obtener parámetros
$fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-m-d', strtotime('-7 days'));
$fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-d');

// Validar fechas
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_inicio) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_fin)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Formato de fecha inválido']);
    exit();
}

try {
    // Obtener rendimiento de meseros
    $sql = "
        SELECT u.id, u.nombre,
               COUNT(o.id) as total_ordenes,
               COALESCE(SUM(o.total), 0) as ventas_totales,
               COALESCE(AVG(o.total), 0) as promedio_orden,
               COUNT(DISTINCT o.hash_mesa) as mesas_atendidas,
               MIN(o.fecha_hora) as primera_orden,
               MAX(o.fecha_hora) as ultima_orden
        FROM usuarios u
        LEFT JOIN ordenes o ON u.id = o.mesero_id 
            AND o.fecha_hora >= ? AND o.fecha_hora <= ?
            AND o.estado = 'finalizada'
        WHERE u.rol = 'mesero' AND u.activo = 1
        GROUP BY u.id, u.nombre
        ORDER BY ventas_totales DESC, total_ordenes DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$fecha_inicio . ' 00:00:00', $fecha_fin . ' 23:59:59']);
    $meseros = $stmt->fetchAll();
    
    // Calcular métricas adicionales para cada mesero
    foreach ($meseros as &$mesero) {
        // Calcular eficiencia (órdenes por hora trabajada)
        if ($mesero['primera_orden'] && $mesero['ultima_orden']) {
            $inicio = new DateTime($mesero['primera_orden']);
            $fin = new DateTime($mesero['ultima_orden']);
            $horas_trabajadas = $fin->diff($inicio)->h + ($fin->diff($inicio)->days * 24);
            $mesero['horas_trabajadas'] = $horas_trabajadas;
            $mesero['ordenes_por_hora'] = $horas_trabajadas > 0 ? round($mesero['total_ordenes'] / $horas_trabajadas, 2) : 0;
        } else {
            $mesero['horas_trabajadas'] = 0;
            $mesero['ordenes_por_hora'] = 0;
        }
        
        // Obtener tiempo promedio de servicio
        $sql_tiempo = "
            SELECT AVG(TIMESTAMPDIFF(MINUTE, o.fecha_hora, 
                (SELECT MAX(d.fecha_actualizacion) 
                 FROM detalle_orden d 
                 WHERE d.orden_id = o.id AND d.estado = 'servido')
            )) as tiempo_promedio_servicio
            FROM ordenes o
            WHERE o.mesero_id = ? 
            AND o.fecha_hora >= ? AND o.fecha_hora <= ?
            AND o.estado = 'finalizada'
            AND EXISTS (
                SELECT 1 FROM detalle_orden d 
                WHERE d.orden_id = o.id AND d.estado = 'servido'
            )
        ";
        
        $stmt_tiempo = $pdo->prepare($sql_tiempo);
        $stmt_tiempo->execute([
            $mesero['id'], 
            $fecha_inicio . ' 00:00:00', 
            $fecha_fin . ' 23:59:59'
        ]);
        $tiempo_servicio = $stmt_tiempo->fetch();
        $mesero['tiempo_promedio_servicio'] = round($tiempo_servicio['tiempo_promedio_servicio'] ?? 0, 1);
        
        // Calcular rating de eficiencia (0-100)
        $rating_ventas = min(100, ($mesero['ventas_totales'] / 100000) * 100); // Base 100k
        $rating_ordenes = min(100, ($mesero['total_ordenes'] / 50) * 100); // Base 50 órdenes
        $rating_tiempo = $mesero['tiempo_promedio_servicio'] > 0 ? 
            max(0, 100 - (($mesero['tiempo_promedio_servicio'] - 15) * 2)) : 100; // Ideal 15 min
        
        $mesero['rating_eficiencia'] = round(($rating_ventas + $rating_ordenes + $rating_tiempo) / 3, 1);
    }
    
    // Obtener estadísticas generales
    $total_ventas = array_sum(array_column($meseros, 'ventas_totales'));
    $total_ordenes = array_sum(array_column($meseros, 'total_ordenes'));
    $meseros_activos = count(array_filter($meseros, function($m) { return $m['total_ordenes'] > 0; }));
    
    // Respuesta JSON
    echo json_encode([
        'success' => true,
        'meseros' => $meseros,
        'estadisticas' => [
            'total_ventas_periodo' => $total_ventas,
            'total_ordenes_periodo' => $total_ordenes,
            'meseros_activos' => $meseros_activos,
            'promedio_ventas_mesero' => $meseros_activos > 0 ? round($total_ventas / $meseros_activos, 2) : 0,
            'promedio_ordenes_mesero' => $meseros_activos > 0 ? round($total_ordenes / $meseros_activos, 2) : 0,
            'fecha_inicio' => $fecha_inicio,
            'fecha_fin' => $fecha_fin
        ]
    ]);
    
} catch (PDOException $e) {
    error_log("Error en reportes_meseros.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error al generar reporte de meseros',
        'meseros' => []
    ]);
}
?>
