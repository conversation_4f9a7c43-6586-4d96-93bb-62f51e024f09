-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Servidor: 127.0.0.1
-- Tiempo de generación: 10-06-2025 a las 06:06:49
-- Versión del servidor: 10.4.32-MariaDB
-- Versión de PHP: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de datos: `restaurante`
--
CREATE DATABASE IF NOT EXISTS `restaurante` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish_ci;
USE `restaurante`;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `categorias`
--

CREATE TABLE `categorias` (
  `id` int(11) NOT NULL,
  `nombre` varchar(100) NOT NULL,
  `descripcion` text DEFAULT NULL,
  `icono` varchar(50) DEFAULT 'bi-tag',
  `color` varchar(20) DEFAULT 'primary',
  `activo` tinyint(1) DEFAULT 1,
  `fecha_creacion` timestamp NOT NULL DEFAULT current_timestamp(),
  `fecha_actualizacion` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish_ci;

--
-- Volcado de datos para la tabla `categorias`
--

INSERT INTO `categorias` (`id`, `nombre`, `descripcion`, `icono`, `color`, `activo`, `fecha_creacion`, `fecha_actualizacion`) VALUES
(1, 'Platos Principales', 'Comidas principales del menú', 'bi-egg-fried', 'danger', 1, '2025-06-03 02:17:17', '2025-06-03 02:17:17'),
(2, 'Bebidas', 'Bebidas frías y calientes', 'bi-cup-straw', 'info', 1, '2025-06-03 02:17:17', '2025-06-03 02:17:17'),
(3, 'Postres', 'Postres y dulces', 'bi-cake2', 'warning', 1, '2025-06-03 02:17:17', '2025-06-03 02:17:17'),
(4, 'Entradas', 'Aperitivos y entradas', 'bi-bowl', 'success', 1, '2025-06-03 02:17:17', '2025-06-03 02:17:17'),
(5, 'Ensaladas', 'Ensaladas frescas', 'bi-leaf', 'success', 1, '2025-06-03 02:17:17', '2025-06-03 02:17:17'),
(6, 'Sopas', 'Sopas calientes', 'bi-droplet', 'primary', 1, '2025-06-03 02:17:17', '2025-06-03 02:17:17'),
(7, 'Licores', '', 'bi-cup-straw', 'primary', 1, '2025-06-03 02:18:34', '2025-06-03 02:18:34'),
(8, 'Especiales', 'Productos Especiales de la Casa', 'bi-star', 'primary', 1, '2025-06-09 17:20:11', '2025-06-09 17:20:11');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `compras`
--

CREATE TABLE `compras` (
  `id` int(11) NOT NULL,
  `proveedor_id` int(11) NOT NULL,
  `numero_factura` varchar(50) DEFAULT NULL,
  `fecha_compra` date NOT NULL,
  `fecha_entrega` date DEFAULT NULL,
  `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
  `impuestos` decimal(10,2) DEFAULT 0.00,
  `total` decimal(10,2) NOT NULL DEFAULT 0.00,
  `estado` enum('pendiente','recibida','cancelada') DEFAULT 'pendiente',
  `notas` text DEFAULT NULL,
  `usuario_id` int(11) NOT NULL,
  `fecha_creacion` timestamp NOT NULL DEFAULT current_timestamp(),
  `fecha_actualizacion` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish_ci;

--
-- Volcado de datos para la tabla `compras`
--

INSERT INTO `compras` (`id`, `proveedor_id`, `numero_factura`, `fecha_compra`, `fecha_entrega`, `subtotal`, `impuestos`, `total`, `estado`, `notas`, `usuario_id`, `fecha_creacion`, `fecha_actualizacion`) VALUES
(1, 1, '8167128', '2025-06-09', NULL, 0.00, 0.00, 18.00, 'recibida', NULL, 21, '2025-06-09 21:35:21', '2025-06-09 21:35:35');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `configuracion`
--

CREATE TABLE `configuracion` (
  `id` int(11) NOT NULL,
  `nombre_restaurante` varchar(255) DEFAULT 'Mi Restaurante',
  `total_mesas` int(11) DEFAULT 8,
  `direccion` text DEFAULT NULL,
  `telefono` varchar(20) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `descripcion_restaurante` text DEFAULT 'Descripci¾n de nuestro restaurante',
  `moneda` varchar(10) DEFAULT 'Q',
  `mensajes_cortesia` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`mensajes_cortesia`))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish_ci;

--
-- Volcado de datos para la tabla `configuracion`
--

INSERT INTO `configuracion` (`id`, `nombre_restaurante`, `total_mesas`, `direccion`, `telefono`, `created_at`, `updated_at`, `descripcion_restaurante`, `moneda`, `mensajes_cortesia`) VALUES
(1, 'Restaurante El Buen Sabor', 9, 'Calle Principal #123', '5555-0123', '2025-06-01 06:50:09', '2025-06-03 05:41:46', 'Descripcion de nuestro restaurante', 'Q', '[{\"titulo\":\"Gracias Por su Visita\",\"mensaje\":\"¡Gracias por visitarnos! Esperamos que haya disfrutado su experiencia.\",\"prioridad\":1,\"activo\":true},{\"titulo\":\"Fue un Placer\",\"mensaje\":\"Gracias por elegirnos. ¡Fue un placer atenderle!\",\"prioridad\":1,\"activo\":true},{\"titulo\":\"Vuelva Pronto\",\"mensaje\":\"¡Gracias por su visita! Lo esperamos pronto nuevamente.\",\"prioridad\":1,\"activo\":true},{\"titulo\":\"compartir comida\",\"mensaje\":\"Nos alegra haber compartido esta comida con usted. ¡Vuelva pronto!\",\"prioridad\":1,\"activo\":true},{\"titulo\":\"Vuelva Pronto\",\"mensaje\":\"Gracias por confiar en nosotros. ¡Buen provecho y hasta la próxima!\",\"prioridad\":1,\"activo\":true},{\"titulo\":\"Vuelva cuando quiera\",\"mensaje\":\"Su visita nos llena de alegría. ¡Gracias y vuelva cuando quiera!\",\"prioridad\":1,\"activo\":true},{\"titulo\":\"Gracias\",\"mensaje\":\"Nos encanta recibir clientes como usted. ¡Mil gracias!\",\"prioridad\":1,\"activo\":true}]');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `cuentas`
--

CREATE TABLE `cuentas` (
  `id` int(11) NOT NULL,
  `mesa` int(11) NOT NULL,
  `hash_mesa` varchar(255) NOT NULL,
  `mesero_id` int(11) NOT NULL,
  `nombre_cliente` varchar(100) NOT NULL DEFAULT 'Cliente',
  `apellido_cliente` varchar(100) DEFAULT '',
  `total` decimal(10,2) DEFAULT 0.00,
  `estado` enum('abierta','cerrada','pagada') DEFAULT 'abierta',
  `fecha_creacion` timestamp NOT NULL DEFAULT current_timestamp(),
  `fecha_actualizacion` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `propina_porcentaje` decimal(5,2) DEFAULT 10.00 COMMENT 'Porcentaje de propina aplicado',
  `propina_monto` decimal(10,2) DEFAULT 0.00 COMMENT 'Monto de propina en dinero',
  `descuento_tipo` enum('ninguno','porcentaje','monto_fijo') DEFAULT 'ninguno' COMMENT 'Tipo de descuento aplicado',
  `descuento_valor` decimal(10,2) DEFAULT 0.00 COMMENT 'Valor del descuento (% o monto)',
  `descuento_monto` decimal(10,2) DEFAULT 0.00 COMMENT 'Monto de descuento en dinero',
  `descuento_motivo` varchar(255) DEFAULT NULL COMMENT 'Motivo del descuento aplicado',
  `subtotal` decimal(10,2) DEFAULT 0.00 COMMENT 'Subtotal sin ajustes',
  `total_final` decimal(10,2) DEFAULT 0.00 COMMENT 'Total final con propina y descuentos'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish_ci;

--
-- Volcado de datos para la tabla `cuentas`
--

INSERT INTO `cuentas` (`id`, `mesa`, `hash_mesa`, `mesero_id`, `nombre_cliente`, `apellido_cliente`, `total`, `estado`, `fecha_creacion`, `fecha_actualizacion`, `propina_porcentaje`, `propina_monto`, `descuento_tipo`, `descuento_valor`, `descuento_monto`, `descuento_motivo`, `subtotal`, `total_final`) VALUES
(9, 6, '52468ca98c61977eff256d38a46e67e7d54eeefa', 22, 'Pepito', 'Perez', 380.00, 'pagada', '2025-06-09 19:09:07', '2025-06-09 19:30:15', 10.00, 0.00, 'ninguno', 0.00, 0.00, NULL, 0.00, 0.00),
(10, 9, '25d04dbf70fbdda07556e8d0fe1df0af47483b3a', 22, 'Javier', 'Aguirre', 280.00, 'pagada', '2025-06-09 19:32:01', '2025-06-09 19:38:54', 10.00, 0.00, 'ninguno', 0.00, 0.00, NULL, 0.00, 0.00),
(11, 2, 'test_hash_456', 22, 'Ana', 'GarcÝa', 280.00, 'pagada', '2025-06-09 19:37:22', '2025-06-09 19:38:10', 10.00, 0.00, 'ninguno', 0.00, 0.00, NULL, 0.00, 0.00),
(12, 3, '874d036ddc2e32f1c7caad2d5599ac50d608fbb6', 22, 'Adriana', 'Castillo', 130.00, 'pagada', '2025-06-09 19:42:45', '2025-06-09 19:47:29', 10.00, 0.00, 'ninguno', 0.00, 0.00, NULL, 0.00, 0.00),
(13, 3, 'test_hash_789', 22, 'Carlos', 'Mendez', 130.00, 'pagada', '2025-06-09 19:45:51', '2025-06-09 19:47:10', 10.00, 0.00, 'ninguno', 0.00, 0.00, NULL, 0.00, 0.00),
(14, 4, 'ca88ce631d07c850914b06acec26e71e22d18a2a', 22, 'Pedro', 'Alvarado', 333.00, 'pagada', '2025-06-09 19:48:45', '2025-06-09 19:55:00', 10.00, 0.00, 'ninguno', 0.00, 0.00, NULL, 0.00, 0.00),
(15, 2, 'd4c823226f5dd3436f48c8f1f58cde34b1963c8d', 22, 'Alfonso', 'Lopez', 125.00, 'pagada', '2025-06-10 00:30:21', '2025-06-10 00:53:23', 10.00, 0.00, 'ninguno', 0.00, 0.00, NULL, 0.00, 0.00);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `detalle_compras`
--

CREATE TABLE `detalle_compras` (
  `id` int(11) NOT NULL,
  `compra_id` int(11) NOT NULL,
  `producto_id` int(11) NOT NULL,
  `cantidad` int(11) NOT NULL,
  `precio_unitario` decimal(10,2) NOT NULL,
  `subtotal` decimal(10,2) NOT NULL,
  `fecha_vencimiento` date DEFAULT NULL,
  `lote` varchar(50) DEFAULT NULL,
  `recibido` tinyint(1) DEFAULT 0,
  `cantidad_recibida` int(11) DEFAULT 0,
  `fecha_creacion` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish_ci;

--
-- Volcado de datos para la tabla `detalle_compras`
--

INSERT INTO `detalle_compras` (`id`, `compra_id`, `producto_id`, `cantidad`, `precio_unitario`, `subtotal`, `fecha_vencimiento`, `lote`, `recibido`, `cantidad_recibida`, `fecha_creacion`) VALUES
(1, 1, 11, 3, 6.00, 18.00, NULL, NULL, 0, 0, '2025-06-09 21:35:21');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `detalle_orden`
--

CREATE TABLE `detalle_orden` (
  `id` int(11) NOT NULL,
  `orden_id` int(11) NOT NULL,
  `producto_id` int(11) NOT NULL,
  `cantidad` int(11) NOT NULL DEFAULT 1,
  `precio_unitario` decimal(10,2) NOT NULL,
  `subtotal` decimal(10,2) NOT NULL,
  `area` enum('cocina','bebidas') NOT NULL,
  `estado` enum('pendiente','preparando','listo','servido') DEFAULT 'pendiente',
  `notas` text DEFAULT NULL,
  `fecha_creacion` timestamp NOT NULL DEFAULT current_timestamp(),
  `fecha_actualizacion` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish_ci;

--
-- Volcado de datos para la tabla `detalle_orden`
--

INSERT INTO `detalle_orden` (`id`, `orden_id`, `producto_id`, `cantidad`, `precio_unitario`, `subtotal`, `area`, `estado`, `notas`, `fecha_creacion`, `fecha_actualizacion`) VALUES
(5, 18, 10, 1, 30.00, 30.00, 'bebidas', 'servido', '', '2025-06-09 19:32:17', '2025-06-09 19:33:07'),
(6, 18, 8, 1, 40.00, 40.00, 'bebidas', 'servido', '', '2025-06-09 19:32:17', '2025-06-09 19:33:06'),
(7, 18, 12, 1, 70.00, 70.00, 'cocina', 'servido', '', '2025-06-09 19:32:17', '2025-06-09 19:33:37'),
(8, 18, 11, 1, 70.00, 70.00, 'cocina', 'servido', '', '2025-06-09 19:32:17', '2025-06-09 19:33:27'),
(9, 18, 2, 1, 70.00, 70.00, 'bebidas', 'servido', '', '2025-06-09 19:32:17', '2025-06-09 19:33:04'),
(14, 20, 8, 1, 40.00, 40.00, 'bebidas', 'servido', '', '2025-06-09 19:42:50', '2025-06-09 19:43:20'),
(15, 20, 13, 1, 60.00, 60.00, 'cocina', 'servido', '', '2025-06-09 19:42:50', '2025-06-09 19:43:22'),
(16, 20, 9, 1, 30.00, 30.00, 'bebidas', 'servido', '', '2025-06-09 19:42:50', '2025-06-09 19:43:23'),
(20, 22, 9, 1, 30.00, 30.00, 'bebidas', 'servido', '', '2025-06-09 19:48:54', '2025-06-09 19:49:35'),
(21, 22, 10, 1, 30.00, 30.00, 'bebidas', 'servido', '', '2025-06-09 19:48:54', '2025-06-09 19:49:33'),
(22, 22, 13, 1, 60.00, 60.00, 'cocina', 'servido', '', '2025-06-09 19:48:54', '2025-06-09 19:49:32'),
(23, 22, 2, 1, 70.00, 70.00, 'bebidas', 'servido', '', '2025-06-09 19:48:54', '2025-06-09 19:49:31'),
(24, 22, 15, 1, 143.00, 143.00, 'cocina', 'servido', '', '2025-06-09 19:48:54', '2025-06-09 19:49:36'),
(25, 23, 12, 1, 70.00, 70.00, 'cocina', 'servido', '', '2025-06-10 00:30:30', '2025-06-10 00:34:16'),
(26, 23, 1, 1, 55.00, 55.00, 'bebidas', 'servido', '', '2025-06-10 00:30:30', '2025-06-10 00:53:03');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `inventario`
--

CREATE TABLE `inventario` (
  `id` int(11) NOT NULL,
  `producto_id` int(11) NOT NULL,
  `stock_actual` int(11) NOT NULL DEFAULT 0,
  `stock_minimo` int(11) NOT NULL DEFAULT 0,
  `stock_maximo` int(11) DEFAULT NULL,
  `unidad_medida` enum('unidad','kg','litro','gramo','ml','porcion') NOT NULL DEFAULT 'unidad',
  `costo_promedio` decimal(10,2) DEFAULT 0.00,
  `fecha_actualizacion` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish_ci;

--
-- Volcado de datos para la tabla `inventario`
--

INSERT INTO `inventario` (`id`, `producto_id`, `stock_actual`, `stock_minimo`, `stock_maximo`, `unidad_medida`, `costo_promedio`, `fecha_actualizacion`) VALUES
(1, 1, 49, 10, 100, 'unidad', 20.00, '2025-06-10 00:53:03'),
(2, 2, 48, 10, 100, 'unidad', 50.00, '2025-06-09 19:49:31'),
(3, 3, 50, 10, 100, 'unidad', 30.00, '2025-06-09 18:03:32'),
(4, 4, 50, 10, 100, 'unidad', 25.00, '2025-06-09 18:03:32'),
(5, 5, 50, 10, 100, 'unidad', 40.00, '2025-06-09 18:03:32'),
(6, 6, 50, 10, 100, 'unidad', 30.00, '2025-06-09 18:03:32'),
(7, 7, 50, 10, 100, 'unidad', 30.00, '2025-06-09 18:03:32'),
(8, 8, 48, 10, 100, 'unidad', 20.00, '2025-06-09 19:43:20'),
(9, 9, 47, 10, 100, 'unidad', 15.00, '2025-06-09 19:49:35'),
(10, 10, 48, 10, 100, 'unidad', 15.00, '2025-06-09 19:49:33'),
(11, 11, 52, 10, 100, 'unidad', 6.00, '2025-06-09 21:35:21'),
(12, 12, 48, 10, 100, 'unidad', 35.00, '2025-06-10 00:34:16'),
(13, 13, 48, 10, 100, 'unidad', 35.00, '2025-06-09 19:49:32'),
(14, 14, 50, 10, 100, 'unidad', 10.00, '2025-06-09 18:03:32'),
(15, 15, 49, 10, 100, 'unidad', 80.00, '2025-06-09 19:49:36'),
(16, 16, 50, 10, 100, 'unidad', 500.00, '2025-06-09 18:03:32'),
(17, 17, 50, 10, 100, 'unidad', 50.00, '2025-06-09 18:03:32');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `mesas`
--

CREATE TABLE `mesas` (
  `id` int(11) NOT NULL,
  `numero_mesa` int(11) NOT NULL,
  `mesero_id` int(11) DEFAULT NULL,
  `estado` enum('libre','ocupada','reservada') DEFAULT 'libre',
  `fecha_apertura` timestamp NULL DEFAULT NULL,
  `hash_mesa` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish_ci;

--
-- Volcado de datos para la tabla `mesas`
--

INSERT INTO `mesas` (`id`, `numero_mesa`, `mesero_id`, `estado`, `fecha_apertura`, `hash_mesa`, `created_at`, `updated_at`) VALUES
(4, 4, NULL, 'libre', NULL, NULL, '2025-06-01 06:50:09', '2025-06-01 06:50:09'),
(6, 6, NULL, 'libre', NULL, NULL, '2025-06-01 06:50:09', '2025-06-01 06:50:09'),
(8, 8, NULL, 'libre', NULL, NULL, '2025-06-01 06:50:09', '2025-06-01 06:50:09'),
(14, 3, NULL, 'libre', NULL, NULL, '2025-06-03 04:10:54', '2025-06-03 06:57:13'),
(15, 5, NULL, 'libre', NULL, NULL, '2025-06-03 04:10:56', '2025-06-03 06:57:13'),
(16, 7, NULL, 'libre', NULL, NULL, '2025-06-03 04:37:39', '2025-06-03 06:57:13'),
(18, 2, NULL, 'libre', NULL, NULL, '2025-06-03 05:59:54', '2025-06-03 06:57:13'),
(24, 1, NULL, 'libre', NULL, NULL, '2025-06-09 18:53:38', '2025-06-09 18:56:55'),
(26, 1, NULL, 'libre', NULL, NULL, '2025-06-09 19:00:13', '2025-06-09 19:03:23'),
(27, 1, NULL, 'libre', NULL, NULL, '2025-06-09 19:06:11', '2025-06-09 19:08:40'),
(28, 6, NULL, 'libre', NULL, NULL, '2025-06-09 19:09:07', '2025-06-09 19:30:15'),
(29, 9, NULL, 'libre', NULL, NULL, '2025-06-09 19:32:01', '2025-06-09 19:38:54'),
(30, 2, NULL, 'libre', NULL, NULL, '2025-06-09 19:37:22', '2025-06-09 19:38:10'),
(31, 3, NULL, 'libre', NULL, NULL, '2025-06-09 19:42:45', '2025-06-09 19:47:10'),
(32, 3, NULL, 'libre', NULL, NULL, '2025-06-09 19:45:51', '2025-06-09 19:47:10'),
(33, 4, NULL, 'libre', NULL, 'ca88ce631d07c850914b06acec26e71e22d18a2a', '2025-06-09 19:48:45', '2025-06-09 19:55:00'),
(34, 2, NULL, 'libre', NULL, 'd4c823226f5dd3436f48c8f1f58cde34b1963c8d', '2025-06-10 00:30:21', '2025-06-10 00:53:23');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `movimientos_inventario`
--

CREATE TABLE `movimientos_inventario` (
  `id` int(11) NOT NULL,
  `producto_id` int(11) NOT NULL,
  `tipo_movimiento` enum('entrada','salida','ajuste','merma') NOT NULL,
  `cantidad` int(11) NOT NULL,
  `stock_anterior` int(11) NOT NULL,
  `stock_nuevo` int(11) NOT NULL,
  `costo_unitario` decimal(10,2) DEFAULT 0.00,
  `referencia_tipo` enum('compra','venta','ajuste','merma') NOT NULL,
  `referencia_id` int(11) DEFAULT NULL,
  `motivo` varchar(255) DEFAULT NULL,
  `usuario_id` int(11) NOT NULL,
  `fecha_movimiento` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish_ci;

--
-- Volcado de datos para la tabla `movimientos_inventario`
--

INSERT INTO `movimientos_inventario` (`id`, `producto_id`, `tipo_movimiento`, `cantidad`, `stock_anterior`, `stock_nuevo`, `costo_unitario`, `referencia_tipo`, `referencia_id`, `motivo`, `usuario_id`, `fecha_movimiento`) VALUES
(1, 9, 'salida', 1, 50, 49, 0.00, 'venta', 17, 'Venta automática - Orden #17', 22, '2025-06-09 19:29:40'),
(2, 2, 'salida', 1, 50, 49, 0.00, 'venta', 18, 'Venta automática - Orden #18', 22, '2025-06-09 19:33:04'),
(3, 8, 'salida', 1, 50, 49, 0.00, 'venta', 18, 'Venta automática - Orden #18', 22, '2025-06-09 19:33:06'),
(4, 10, 'salida', 1, 50, 49, 0.00, 'venta', 18, 'Venta automática - Orden #18', 22, '2025-06-09 19:33:07'),
(5, 11, 'salida', 1, 50, 49, 0.00, 'venta', 18, 'Venta automática - Orden #18', 22, '2025-06-09 19:33:27'),
(6, 12, 'salida', 1, 50, 49, 0.00, 'venta', 18, 'Venta automática - Orden #18', 22, '2025-06-09 19:33:37'),
(7, 8, 'salida', 1, 49, 48, 0.00, 'venta', 20, 'Venta automática - Orden #20', 22, '2025-06-09 19:43:20'),
(8, 13, 'salida', 1, 50, 49, 0.00, 'venta', 20, 'Venta automática - Orden #20', 22, '2025-06-09 19:43:22'),
(9, 9, 'salida', 1, 49, 48, 0.00, 'venta', 20, 'Venta automática - Orden #20', 22, '2025-06-09 19:43:23'),
(10, 2, 'salida', 1, 49, 48, 0.00, 'venta', 22, 'Venta automática - Orden #22', 22, '2025-06-09 19:49:31'),
(11, 13, 'salida', 1, 49, 48, 0.00, 'venta', 22, 'Venta automática - Orden #22', 22, '2025-06-09 19:49:32'),
(12, 10, 'salida', 1, 49, 48, 0.00, 'venta', 22, 'Venta automática - Orden #22', 22, '2025-06-09 19:49:33'),
(13, 9, 'salida', 1, 48, 47, 0.00, 'venta', 22, 'Venta automática - Orden #22', 22, '2025-06-09 19:49:35'),
(14, 15, 'salida', 1, 50, 49, 0.00, 'venta', 22, 'Venta automática - Orden #22', 22, '2025-06-09 19:49:36'),
(15, 11, 'entrada', 3, 49, 52, 6.00, 'compra', 1, 'Compra #1 - Factura: 8167128', 21, '2025-06-09 21:35:21'),
(16, 12, 'salida', 1, 49, 48, 0.00, 'venta', 23, 'Venta automática - Orden #23', 22, '2025-06-10 00:34:16'),
(17, 1, 'salida', 1, 50, 49, 0.00, 'venta', 23, 'Venta automática - Orden #23', 22, '2025-06-10 00:53:03');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `ordenes`
--

CREATE TABLE `ordenes` (
  `id` int(11) NOT NULL,
  `cuenta_id` int(11) DEFAULT NULL,
  `mesa` int(11) NOT NULL,
  `hash_mesa` varchar(255) DEFAULT NULL,
  `mesero_id` int(11) DEFAULT NULL,
  `estado` enum('pendiente','en_proceso','lista_parcial','finalizada','cancelada') DEFAULT 'pendiente',
  `total` decimal(10,2) NOT NULL,
  `fecha_hora` timestamp NOT NULL DEFAULT current_timestamp(),
  `fecha_actualizacion` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish_ci;

--
-- Volcado de datos para la tabla `ordenes`
--

INSERT INTO `ordenes` (`id`, `cuenta_id`, `mesa`, `hash_mesa`, `mesero_id`, `estado`, `total`, `fecha_hora`, `fecha_actualizacion`) VALUES
(18, 10, 9, '25d04dbf70fbdda07556e8d0fe1df0af47483b3a', 22, 'finalizada', 280.00, '2025-06-09 19:32:17', '2025-06-09 19:33:37'),
(20, 12, 3, '874d036ddc2e32f1c7caad2d5599ac50d608fbb6', 22, 'finalizada', 130.00, '2025-06-09 19:42:50', '2025-06-09 19:43:23'),
(22, 14, 4, 'ca88ce631d07c850914b06acec26e71e22d18a2a', 22, 'finalizada', 333.00, '2025-06-09 19:48:54', '2025-06-09 19:49:36'),
(23, 15, 2, 'd4c823226f5dd3436f48c8f1f58cde34b1963c8d', 22, 'finalizada', 125.00, '2025-06-10 00:30:30', '2025-06-10 00:53:03');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `productos`
--

CREATE TABLE `productos` (
  `id` int(11) NOT NULL,
  `nombre` varchar(100) NOT NULL,
  `descripcion` text DEFAULT NULL,
  `imagen` varchar(255) DEFAULT NULL,
  `precio_venta` decimal(10,2) NOT NULL,
  `precio_costo` decimal(10,2) DEFAULT 0.00,
  `tipo` enum('comida','bebida') NOT NULL,
  `creado_en` timestamp NOT NULL DEFAULT current_timestamp(),
  `area` enum('cocina','bebidas') NOT NULL DEFAULT 'cocina',
  `disponible` tinyint(1) DEFAULT 1,
  `categoria_id` int(11) DEFAULT NULL,
  `fecha_creacion` timestamp NOT NULL DEFAULT current_timestamp(),
  `fecha_actualizacion` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish_ci;

--
-- Volcado de datos para la tabla `productos`
--

INSERT INTO `productos` (`id`, `nombre`, `descripcion`, `imagen`, `precio_venta`, `precio_costo`, `tipo`, `creado_en`, `area`, `disponible`, `categoria_id`, `fecha_creacion`, `fecha_actualizacion`) VALUES
(1, 'Mojito', '', 'producto_1748919953_683e669199f26.webp', 55.00, 20.00, 'comida', '2025-06-03 03:05:53', 'bebidas', 1, 7, '2025-06-03 03:05:53', '2025-06-03 03:05:53'),
(2, 'Negroni Especial', '', 'producto_1748920151_683e6757b7dd8.webp', 70.00, 50.00, 'comida', '2025-06-03 03:09:11', 'bebidas', 1, 7, '2025-06-03 03:09:11', '2025-06-03 03:09:11'),
(3, 'Piña Colada', '', 'producto_1748920189_683e677d0601f.jpg', 55.00, 30.00, 'comida', '2025-06-03 03:09:49', 'bebidas', 1, 7, '2025-06-03 03:09:49', '2025-06-03 03:09:49'),
(4, 'Daiquiri', '', 'producto_1748920484_683e68a462ac8.jpg', 55.00, 25.00, 'comida', '2025-06-03 03:14:44', 'bebidas', 1, 7, '2025-06-03 03:14:44', '2025-06-03 03:14:44'),
(5, 'Bocado de Cardenal', '', 'producto_1748920584_683e690875fd1.jpg', 53.00, 40.00, 'comida', '2025-06-03 03:16:24', 'cocina', 1, 3, '2025-06-03 03:16:24', '2025-06-03 03:16:24'),
(6, 'Cheesecake', '', 'producto_1748920653_683e694dc079a.webp', 50.00, 30.00, 'comida', '2025-06-03 03:17:33', 'cocina', 1, 3, '2025-06-03 03:17:33', '2025-06-03 03:17:33'),
(7, 'Blue Ribbon Brownie', '', 'producto_1748920704_683e69807830e.jpg', 60.00, 30.00, 'comida', '2025-06-03 03:18:24', 'cocina', 1, 3, '2025-06-03 03:18:24', '2025-06-03 03:18:24'),
(8, 'Malteadas', '', 'producto_1748920762_683e69babdd2e.jpg', 40.00, 20.00, 'comida', '2025-06-03 03:19:22', 'bebidas', 1, 2, '2025-06-03 03:19:22', '2025-06-03 03:19:22'),
(9, 'Limonada de Mango', '', 'producto_1748920815_683e69ef13e7e.jpg', 30.00, 15.00, 'comida', '2025-06-03 03:20:15', 'bebidas', 1, 2, '2025-06-03 03:20:15', '2025-06-03 03:20:15'),
(10, 'Limonada de coco', '', 'producto_1748920847_683e6a0feb9ce.webp', 30.00, 15.00, 'comida', '2025-06-03 03:20:47', 'bebidas', 1, 2, '2025-06-03 03:20:47', '2025-06-03 03:20:47'),
(11, 'Twinkies Texas', '', 'producto_1748920932_683e6a64cf171.webp', 70.00, 40.00, 'comida', '2025-06-03 03:22:12', 'cocina', 1, 4, '2025-06-03 03:22:12', '2025-06-03 03:22:12'),
(12, 'BBQ Chicken Wings', '', 'producto_1748920983_683e6a979adb6.jpg', 70.00, 35.00, 'comida', '2025-06-03 03:23:03', 'cocina', 1, 4, '2025-06-03 03:23:03', '2025-06-03 03:23:03'),
(13, 'Canape', '', 'producto_1748921036_683e6acc9eef4.jpg', 60.00, 35.00, 'comida', '2025-06-03 03:23:56', 'cocina', 1, 4, '2025-06-03 03:23:56', '2025-06-03 03:23:56'),
(14, 'Tomates Rellenos', '', 'producto_1748921061_683e6ae59ed4e.jpg', 40.00, 10.00, 'comida', '2025-06-03 03:24:21', 'cocina', 1, 4, '2025-06-03 03:24:21', '2025-06-03 03:24:21'),
(15, 'Spiced Rubbed Bourbon Ribs', '', 'producto_1748921763_683e6da34b08e.jpg', 143.00, 80.00, 'comida', '2025-06-03 03:36:03', 'cocina', 1, 1, '2025-06-03 03:36:03', '2025-06-03 03:36:03'),
(16, 'Tomahawk 32oz', '', 'producto_1748921879_683e6e17e7406.jpg', 799.95, 500.00, 'comida', '2025-06-03 03:37:59', 'cocina', 1, 1, '2025-06-03 03:37:59', '2025-06-03 03:37:59'),
(17, 'Paella negra con pulpitos o langostinos', '', 'producto_1749489825_684718a12c387.jpg', 250.00, 50.00, 'comida', '2025-06-09 17:23:45', 'cocina', 1, 8, '2025-06-09 17:23:45', '2025-06-09 17:23:45');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `proveedores`
--

CREATE TABLE `proveedores` (
  `id` int(11) NOT NULL,
  `nombre` varchar(100) NOT NULL,
  `contacto` varchar(100) DEFAULT NULL,
  `telefono` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `direccion` text DEFAULT NULL,
  `activo` tinyint(1) DEFAULT 1,
  `fecha_creacion` timestamp NOT NULL DEFAULT current_timestamp(),
  `fecha_actualizacion` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish_ci;

--
-- Volcado de datos para la tabla `proveedores`
--

INSERT INTO `proveedores` (`id`, `nombre`, `contacto`, `telefono`, `email`, `direccion`, `activo`, `fecha_creacion`, `fecha_actualizacion`) VALUES
(1, 'Distribuidora Central', 'Juan Pérez', '2234-5678', '<EMAIL>', 'Zona 10, Ciudad de Guatemala', 1, '2025-06-09 18:03:32', '2025-06-09 18:03:32');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `usuarios`
--

CREATE TABLE `usuarios` (
  `id` int(11) NOT NULL,
  `nombre` varchar(100) NOT NULL,
  `correo` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `rol` enum('mesero','cocina','bebidas','admin') NOT NULL,
  `activo` tinyint(1) DEFAULT 1,
  `creado_en` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish_ci;

--
-- Volcado de datos para la tabla `usuarios`
--

INSERT INTO `usuarios` (`id`, `nombre`, `correo`, `password`, `rol`, `activo`, `creado_en`) VALUES
(21, 'Admin Principal', '<EMAIL>', '81dc9bdb52d04dc20036dbd8313ed055', 'admin', 1, '2025-06-01 06:13:18'),
(22, 'Juan Mesero', '<EMAIL>', '81dc9bdb52d04dc20036dbd8313ed055', 'mesero', 1, '2025-06-01 06:13:18'),
(23, 'Chef Principal', '<EMAIL>', '81dc9bdb52d04dc20036dbd8313ed055', 'cocina', 1, '2025-06-01 06:13:18'),
(24, 'Bartender', '<EMAIL>', '81dc9bdb52d04dc20036dbd8313ed055', 'bebidas', 1, '2025-06-01 06:13:18'),
(25, 'Javier Aguirre', '<EMAIL>', '81dc9bdb52d04dc20036dbd8313ed055', 'mesero', 1, '2025-06-09 17:46:23'),
(26, 'Alfonso Lopez', '<EMAIL>', '81dc9bdb52d04dc20036dbd8313ed055', 'cocina', 1, '2025-06-10 03:29:15');

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `categorias`
--
ALTER TABLE `categorias`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `compras`
--
ALTER TABLE `compras`
  ADD PRIMARY KEY (`id`),
  ADD KEY `usuario_id` (`usuario_id`),
  ADD KEY `idx_fecha_compra` (`fecha_compra`),
  ADD KEY `idx_estado` (`estado`),
  ADD KEY `idx_proveedor` (`proveedor_id`);

--
-- Indices de la tabla `configuracion`
--
ALTER TABLE `configuracion`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `cuentas`
--
ALTER TABLE `cuentas`
  ADD PRIMARY KEY (`id`),
  ADD KEY `mesero_id` (`mesero_id`),
  ADD KEY `idx_mesa_hash` (`mesa`,`hash_mesa`),
  ADD KEY `idx_estado` (`estado`),
  ADD KEY `idx_fecha` (`fecha_creacion`);

--
-- Indices de la tabla `detalle_compras`
--
ALTER TABLE `detalle_compras`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_compra` (`compra_id`),
  ADD KEY `idx_producto` (`producto_id`),
  ADD KEY `idx_vencimiento` (`fecha_vencimiento`);

--
-- Indices de la tabla `detalle_orden`
--
ALTER TABLE `detalle_orden`
  ADD PRIMARY KEY (`id`),
  ADD KEY `orden_id` (`orden_id`),
  ADD KEY `producto_id` (`producto_id`);

--
-- Indices de la tabla `inventario`
--
ALTER TABLE `inventario`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_producto` (`producto_id`),
  ADD KEY `idx_stock_bajo` (`stock_actual`,`stock_minimo`);

--
-- Indices de la tabla `mesas`
--
ALTER TABLE `mesas`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_mesas_estado` (`estado`),
  ADD KEY `idx_mesas_mesero` (`mesero_id`);

--
-- Indices de la tabla `movimientos_inventario`
--
ALTER TABLE `movimientos_inventario`
  ADD PRIMARY KEY (`id`),
  ADD KEY `usuario_id` (`usuario_id`),
  ADD KEY `idx_producto_fecha` (`producto_id`,`fecha_movimiento`),
  ADD KEY `idx_tipo_movimiento` (`tipo_movimiento`),
  ADD KEY `idx_referencia` (`referencia_tipo`,`referencia_id`);

--
-- Indices de la tabla `ordenes`
--
ALTER TABLE `ordenes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `mesero_id` (`mesero_id`),
  ADD KEY `idx_ordenes_mesa` (`mesa`),
  ADD KEY `idx_ordenes_hash_mesa` (`hash_mesa`),
  ADD KEY `cuenta_id` (`cuenta_id`);

--
-- Indices de la tabla `productos`
--
ALTER TABLE `productos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `categoria_id` (`categoria_id`);

--
-- Indices de la tabla `proveedores`
--
ALTER TABLE `proveedores`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `usuarios`
--
ALTER TABLE `usuarios`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `correo` (`correo`);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `categorias`
--
ALTER TABLE `categorias`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT de la tabla `compras`
--
ALTER TABLE `compras`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT de la tabla `configuracion`
--
ALTER TABLE `configuracion`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT de la tabla `cuentas`
--
ALTER TABLE `cuentas`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT de la tabla `detalle_compras`
--
ALTER TABLE `detalle_compras`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT de la tabla `detalle_orden`
--
ALTER TABLE `detalle_orden`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT de la tabla `inventario`
--
ALTER TABLE `inventario`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT de la tabla `mesas`
--
ALTER TABLE `mesas`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=35;

--
-- AUTO_INCREMENT de la tabla `movimientos_inventario`
--
ALTER TABLE `movimientos_inventario`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT de la tabla `ordenes`
--
ALTER TABLE `ordenes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT de la tabla `productos`
--
ALTER TABLE `productos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT de la tabla `proveedores`
--
ALTER TABLE `proveedores`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT de la tabla `usuarios`
--
ALTER TABLE `usuarios`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `compras`
--
ALTER TABLE `compras`
  ADD CONSTRAINT `compras_ibfk_1` FOREIGN KEY (`proveedor_id`) REFERENCES `proveedores` (`id`),
  ADD CONSTRAINT `compras_ibfk_2` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`);

--
-- Filtros para la tabla `cuentas`
--
ALTER TABLE `cuentas`
  ADD CONSTRAINT `cuentas_ibfk_1` FOREIGN KEY (`mesero_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `detalle_compras`
--
ALTER TABLE `detalle_compras`
  ADD CONSTRAINT `detalle_compras_ibfk_1` FOREIGN KEY (`compra_id`) REFERENCES `compras` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `detalle_compras_ibfk_2` FOREIGN KEY (`producto_id`) REFERENCES `productos` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `detalle_orden`
--
ALTER TABLE `detalle_orden`
  ADD CONSTRAINT `detalle_orden_ibfk_1` FOREIGN KEY (`orden_id`) REFERENCES `ordenes` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `detalle_orden_ibfk_2` FOREIGN KEY (`producto_id`) REFERENCES `productos` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `inventario`
--
ALTER TABLE `inventario`
  ADD CONSTRAINT `inventario_ibfk_1` FOREIGN KEY (`producto_id`) REFERENCES `productos` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `mesas`
--
ALTER TABLE `mesas`
  ADD CONSTRAINT `mesas_ibfk_1` FOREIGN KEY (`mesero_id`) REFERENCES `usuarios` (`id`) ON DELETE SET NULL;

--
-- Filtros para la tabla `movimientos_inventario`
--
ALTER TABLE `movimientos_inventario`
  ADD CONSTRAINT `movimientos_inventario_ibfk_1` FOREIGN KEY (`producto_id`) REFERENCES `productos` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `movimientos_inventario_ibfk_2` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`);

--
-- Filtros para la tabla `ordenes`
--
ALTER TABLE `ordenes`
  ADD CONSTRAINT `ordenes_ibfk_1` FOREIGN KEY (`mesero_id`) REFERENCES `usuarios` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `ordenes_ibfk_2` FOREIGN KEY (`cuenta_id`) REFERENCES `cuentas` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `productos`
--
ALTER TABLE `productos`
  ADD CONSTRAINT `productos_ibfk_1` FOREIGN KEY (`categoria_id`) REFERENCES `categorias` (`id`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
