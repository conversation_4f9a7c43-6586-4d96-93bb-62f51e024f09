<?php
// Verificar que se proporcione el hash de mesa
$hash_mesa = $_GET['hash_mesa'] ?? '';
$auto_print = $_GET['auto_print'] ?? '0';

if (empty($hash_mesa)) {
    die('Hash de mesa requerido');
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Mesa Completa</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: #fff;
            padding: 10px;
        }

        .ticket {
            max-width: 350px;
            margin: 0 auto;
            background: white;
            border: 1px solid #ddd;
            padding: 15px;
        }

        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .logo {
            max-width: 80px;
            height: auto;
            margin-bottom: 5px;
        }

        .restaurant-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
            text-transform: uppercase;
        }

        .restaurant-info {
            font-size: 10px;
            color: #666;
            margin-bottom: 5px;
        }

        .ticket-info {
            margin-bottom: 15px;
            font-size: 11px;
        }

        .ticket-info div {
            margin-bottom: 3px;
        }

        .section-title {
            font-weight: bold;
            font-size: 13px;
            margin: 15px 0 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #000;
            text-transform: uppercase;
        }

        .cuenta-section {
            margin-bottom: 20px;
            border: 1px dashed #666;
            padding: 10px;
        }

        .cuenta-header {
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 8px;
            background: #f0f0f0;
            padding: 5px;
            text-align: center;
        }

        .product {
            margin-bottom: 8px;
            padding-bottom: 5px;
            border-bottom: 1px dotted #ccc;
        }

        .product-line {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .product-name {
            flex: 1;
            font-weight: bold;
            margin-right: 10px;
        }

        .product-price {
            font-weight: bold;
            white-space: nowrap;
        }

        .product-details {
            font-size: 10px;
            color: #666;
            margin-top: 2px;
        }

        .product-notes {
            font-size: 10px;
            color: #333;
            font-style: italic;
            margin-top: 2px;
            padding-left: 10px;
        }

        .totals {
            margin-top: 15px;
            padding-top: 10px;
            border-top: 2px solid #000;
        }

        .total-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 11px;
        }

        .total-line.final {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #000;
            padding-top: 5px;
            margin-top: 8px;
        }

        .footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px dashed #000;
            font-size: 10px;
            color: #666;
        }

        .courtesy-message {
            margin: 5px 0;
            font-style: italic;
        }

        .signature-area {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #000;
        }

        .signature-line {
            border-bottom: 1px solid #000;
            height: 30px;
            margin: 10px 0;
            position: relative;
        }

        .signature-label {
            font-size: 10px;
            color: #666;
            margin-bottom: 5px;
        }

        .print-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
        }

        .print-button:hover {
            background: #0056b3;
        }

        .no-print {
            text-align: center;
            margin: 20px 0;
        }

        @media print {
            body {
                padding: 0;
                font-size: 11px;
            }
            
            .ticket {
                border: none;
                max-width: none;
                padding: 10px;
            }
            
            .no-print {
                display: none;
            }
        }

        .error {
            text-align: center;
            color: #dc3545;
            padding: 20px;
            font-size: 14px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="no-print">
        <button class="print-button" onclick="window.print()">🖨️ Imprimir Ticket</button>
        <button class="print-button" onclick="window.close()" style="background: #6c757d;">❌ Cerrar</button>
    </div>

    <div id="ticketContent">
        <div class="loading">
            <div>⏳ Generando ticket de mesa...</div>
        </div>
    </div>

    <script>
        // Obtener hash de mesa de la URL
        const urlParams = new URLSearchParams(window.location.search);
        const hashMesa = urlParams.get('hash_mesa');
        const autoPrint = urlParams.get('auto_print') === '1';

        if (hashMesa) {
            cargarTicketMesa(hashMesa);
        } else {
            document.getElementById('ticketContent').innerHTML = `
                <div class="error">
                    <div>❌ Hash de mesa requerido</div>
                </div>
            `;
        }

        function cargarTicketMesa(hashMesa) {
            fetch(`/Restaurante/api/generar_ticket_mesa.php?hash_mesa=${hashMesa}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        mostrarTicketMesa(data.ticket);
                        
                        // Auto-imprimir si se especifica
                        if (autoPrint) {
                            setTimeout(() => {
                                window.print();
                            }, 1000);
                        }
                    } else {
                        document.getElementById('ticketContent').innerHTML = `
                            <div class="error">
                                <div>❌ Error: ${data.message}</div>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('ticketContent').innerHTML = `
                        <div class="error">
                            <div>❌ Error de conexión</div>
                        </div>
                    `;
                });
        }

        function mostrarTicketMesa(ticket) {
            const fecha = new Date();
            const fechaFormateada = fecha.toLocaleDateString('es-GT') + ' ' + fecha.toLocaleTimeString('es-GT');
            
            let cuentasHtml = '';
            let totalGeneral = 0;
            
            ticket.cuentas.forEach(cuenta => {
                let productosHtml = '';
                cuenta.productos.forEach(producto => {
                    productosHtml += `
                        <div class="product">
                            <div class="product-line">
                                <div class="product-name">${producto.nombre}</div>
                                <div class="product-price">${ticket.totales.moneda} ${parseFloat(producto.subtotal).toFixed(2)}</div>
                            </div>
                            <div class="product-details">
                                ${producto.cantidad} x ${ticket.totales.moneda} ${parseFloat(producto.precio_unitario).toFixed(2)}
                            </div>
                            ${producto.notas ? `<div class="product-notes">Nota: ${producto.notas}</div>` : ''}
                        </div>
                    `;
                });

                cuentasHtml += `
                    <div class="cuenta-section">
                        <div class="cuenta-header">
                            CUENTA: ${cuenta.nombre_cliente} ${cuenta.apellido_cliente}
                        </div>
                        ${productosHtml}
                        <div class="total-line">
                            <span>Subtotal Cuenta:</span>
                            <span>${ticket.totales.moneda} ${parseFloat(cuenta.total).toFixed(2)}</span>
                        </div>
                    </div>
                `;
                
                totalGeneral += parseFloat(cuenta.total);
            });

            document.getElementById('ticketContent').innerHTML = `
                <div class="ticket">
                    <div class="header">
                        ${ticket.restaurante.logo ? `<img src="${ticket.restaurante.logo}" alt="Logo" class="logo">` : ''}
                        <div class="restaurant-name">${ticket.restaurante.nombre}</div>
                        <div class="restaurant-info">
                            ${ticket.restaurante.direccion}<br>
                            Tel: ${ticket.restaurante.telefono}
                        </div>
                    </div>
                    
                    <div class="ticket-info">
                        <div><strong>Fecha:</strong> ${fechaFormateada}</div>
                        <div><strong>Mesa:</strong> ${ticket.mesa.numero}</div>
                        <div><strong>Total Cuentas:</strong> ${ticket.cuentas.length}</div>
                        <div><strong>Mesero:</strong> ${ticket.mesa.mesero_nombre || 'N/A'}</div>
                    </div>
                    
                    <div class="section-title">Detalle por Cuenta</div>
                    ${cuentasHtml}
                    
                    <div class="totals">
                        <div class="total-line final">
                            <span>TOTAL MESA:</span>
                            <span>${ticket.totales.moneda} ${totalGeneral.toFixed(2)}</span>
                        </div>
                    </div>
                    
                    <div class="footer">
                        <div class="courtesy-message">¡Gracias por su visita!</div>
                        <div class="courtesy-message">Esperamos verle pronto</div>
                        <div style="margin-top: 10px; font-size: 9px;">
                            Ticket generado: ${fechaFormateada}
                        </div>
                    </div>
                    
                    <div class="signature-area">
                        <div class="signature-label">Firma del Cliente (Tarjeta):</div>
                        <div class="signature-line"></div>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
