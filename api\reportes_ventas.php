<?php
header('Content-Type: application/json');

// Iniciar sesión y conectar a la base de datos

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();
require_once '../config/db.php';

// Verificar que el usuario esté logueado y sea administrador
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden acceder a reportes']);
    exit();
}

// Obtener parámetros
$fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-m-d', strtotime('-7 days'));
$fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-d');
$mesero_id = $_GET['mesero_id'] ?? '';
$export = $_GET['export'] ?? '';

// Validar fechas
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_inicio) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_fin)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Formato de fecha inválido']);
    exit();
}

try {
    // Construir consulta base
    $sql = "
        SELECT DATE(o.fecha_hora) as fecha, 
               COALESCE(SUM(o.total), 0) as total,
               COUNT(o.id) as total_ordenes,
               COALESCE(AVG(o.total), 0) as promedio_orden
        FROM ordenes o
        WHERE o.fecha_hora >= ? AND o.fecha_hora <= ? 
        AND o.estado = 'finalizada'
    ";
    
    $params = [$fecha_inicio . ' 00:00:00', $fecha_fin . ' 23:59:59'];
    
    // Filtrar por mesero si se especifica
    if (!empty($mesero_id) && is_numeric($mesero_id)) {
        $sql .= " AND o.mesero_id = ?";
        $params[] = $mesero_id;
    }
    
    $sql .= " GROUP BY DATE(o.fecha_hora) ORDER BY fecha ASC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $ventas = $stmt->fetchAll();
    
    // Si es exportación CSV
    if ($export === 'csv') {
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="ventas_' . $fecha_inicio . '_' . $fecha_fin . '.csv"');
        
        echo "Fecha,Total Ventas,Total Ordenes,Promedio por Orden\n";
        foreach ($ventas as $venta) {
            echo $venta['fecha'] . ',' . 
                 $venta['total'] . ',' . 
                 $venta['total_ordenes'] . ',' . 
                 round($venta['promedio_orden'], 2) . "\n";
        }
        exit();
    }
    
    // Calcular estadísticas adicionales
    $total_periodo = array_sum(array_column($ventas, 'total'));
    $total_ordenes_periodo = array_sum(array_column($ventas, 'total_ordenes'));
    $promedio_diario = count($ventas) > 0 ? $total_periodo / count($ventas) : 0;
    
    // Respuesta JSON
    echo json_encode([
        'success' => true,
        'ventas' => $ventas,
        'estadisticas' => [
            'total_periodo' => $total_periodo,
            'total_ordenes_periodo' => $total_ordenes_periodo,
            'promedio_diario' => $promedio_diario,
            'dias_con_ventas' => count($ventas),
            'fecha_inicio' => $fecha_inicio,
            'fecha_fin' => $fecha_fin
        ]
    ]);
    
} catch (PDOException $e) {
    error_log("Error en reportes_ventas.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error al generar reporte de ventas',
        'ventas' => []
    ]);
}
?>
