<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📈 Reporte de Inventario</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .report-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1400px;
            overflow: hidden;
        }
        .report-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .report-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 300;
        }
        .report-meta {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            height: 100%;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .section-title {
            background: #f8f9fa;
            padding: 15px 30px;
            margin: 0;
            border-bottom: 1px solid #dee2e6;
            font-size: 1.2rem;
            font-weight: 600;
        }
        .print-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        @media print {
            .print-btn { display: none; }
            body { background: white; }
            .report-container { box-shadow: none; margin: 0; }
        }
    </style>
</head>
<body>
    <button class="btn btn-primary print-btn" onclick="window.print()">
        <i class="fas fa-print"></i> Imprimir
    </button>

    <div class="report-container">
        <!-- Header -->
        <div class="report-header">
            <h1><i class="fas fa-warehouse"></i> Reporte de Inventario</h1>
            <p class="mb-0">Análisis completo del inventario y movimientos</p>
        </div>

        <!-- Meta información -->
        <div class="report-meta">
            <div class="row">
                <div class="col-md-6">
                    <strong><i class="fas fa-calendar"></i> Período de Análisis:</strong><br>
                    <?= date('d/m/Y', strtotime($data['periodo']['fecha_inicio'])) ?> - 
                    <?= date('d/m/Y', strtotime($data['periodo']['fecha_fin'])) ?>
                </div>
                <div class="col-md-6">
                    <strong><i class="fas fa-filter"></i> Filtros Aplicados:</strong><br>
                    <?php if (!empty($data['filtros']['categoria'])): ?>
                        Categoría: <?= ucfirst($data['filtros']['categoria']) ?>
                    <?php else: ?>
                        Todas las categorías
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Resumen General -->
        <div class="container-fluid py-4" style="background: #f8f9fa;">
            <h3 class="text-center mb-4"><i class="fas fa-chart-bar"></i> Resumen General del Inventario</h3>
            <div class="row g-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value text-primary">
                            <i class="fas fa-boxes"></i>
                            <?= number_format($data['resumen_general']['total_productos']) ?>
                        </div>
                        <div class="stat-label">Total Productos</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value text-success">
                            <i class="fas fa-cubes"></i>
                            <?= number_format($data['resumen_general']['stock_total']) ?>
                        </div>
                        <div class="stat-label">Stock Total</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value text-info">
                            <i class="fas fa-dollar-sign"></i>
                            Q<?= number_format($data['resumen_general']['valor_total_inventario'], 2) ?>
                        </div>
                        <div class="stat-label">Valor Total Inventario</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value text-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <?= $data['resumen_general']['productos_stock_bajo'] ?>
                        </div>
                        <div class="stat-label">Productos Stock Bajo</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Productos con Stock Bajo -->
        <?php if (!empty($data['productos_stock_bajo'])): ?>
            <div class="section-title">
                <i class="fas fa-exclamation-triangle text-warning"></i> Productos con Stock Bajo
            </div>
            <div class="container-fluid py-3">
                <div class="table-responsive">
                    <table class="table table-hover table-sm">
                        <thead class="table-warning">
                            <tr>
                                <th>Producto</th>
                                <th>Categoría</th>
                                <th>Stock Actual</th>
                                <th>Stock Mínimo</th>
                                <th>Diferencia</th>
                                <th>Valor Stock</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data['productos_stock_bajo'] as $producto): ?>
                                <tr>
                                    <td><strong><?= htmlspecialchars($producto['nombre']) ?></strong></td>
                                    <td><?= htmlspecialchars($producto['categoria']) ?></td>
                                    <td><span class="badge bg-danger"><?= $producto['stock_actual'] ?></span></td>
                                    <td><?= $producto['stock_minimo'] ?></td>
                                    <td class="text-danger">
                                        <?= $producto['stock_actual'] - $producto['stock_minimo'] ?>
                                    </td>
                                    <td>Q<?= number_format($producto['valor_stock'], 2) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>

        <!-- Análisis por Categoría -->
        <?php if (!empty($data['analisis_por_categoria'])): ?>
            <div class="section-title">
                <i class="fas fa-layer-group text-info"></i> Análisis por Categoría
            </div>
            <div class="container-fluid py-3">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-info">
                            <tr>
                                <th>Categoría</th>
                                <th>Productos</th>
                                <th>Stock Total</th>
                                <th>Valor Categoría</th>
                                <th>Costo Promedio</th>
                                <th>Stock Bajo</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data['analisis_por_categoria'] as $categoria): ?>
                                <tr>
                                    <td><strong><?= htmlspecialchars($categoria['categoria']) ?></strong></td>
                                    <td><?= $categoria['productos_categoria'] ?></td>
                                    <td><?= number_format($categoria['stock_total_categoria']) ?></td>
                                    <td><strong>Q<?= number_format($categoria['valor_categoria'], 2) ?></strong></td>
                                    <td>Q<?= number_format($categoria['costo_promedio_categoria'], 2) ?></td>
                                    <td>
                                        <?php if ($categoria['productos_stock_bajo_categoria'] > 0): ?>
                                            <span class="badge bg-warning"><?= $categoria['productos_stock_bajo_categoria'] ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-success">0</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>

        <!-- Productos Más Movidos -->
        <?php if (!empty($data['productos_mas_movidos'])): ?>
            <div class="section-title">
                <i class="fas fa-exchange-alt text-primary"></i> Productos Más Movidos
            </div>
            <div class="container-fluid py-3">
                <div class="table-responsive">
                    <table class="table table-hover table-sm">
                        <thead class="table-primary">
                            <tr>
                                <th>Producto</th>
                                <th>Categoría</th>
                                <th>Total Movimientos</th>
                                <th>Entradas</th>
                                <th>Salidas</th>
                                <th>Stock Actual</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($data['productos_mas_movidos'], 0, 15) as $producto): ?>
                                <tr>
                                    <td><strong><?= htmlspecialchars($producto['nombre']) ?></strong></td>
                                    <td><?= htmlspecialchars($producto['categoria']) ?></td>
                                    <td><span class="badge bg-primary"><?= $producto['total_movimientos'] ?></span></td>
                                    <td><span class="badge bg-success"><?= $producto['total_entradas'] ?></span></td>
                                    <td><span class="badge bg-danger"><?= $producto['total_salidas'] ?></span></td>
                                    <td><?= number_format($producto['stock_actual']) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>

        <!-- Rotación de Inventario -->
        <?php if (!empty($data['rotacion_inventario'])): ?>
            <div class="section-title">
                <i class="fas fa-sync-alt text-success"></i> Rotación de Inventario
            </div>
            <div class="container-fluid py-3">
                <div class="table-responsive">
                    <table class="table table-hover table-sm">
                        <thead class="table-success">
                            <tr>
                                <th>Producto</th>
                                <th>Categoría</th>
                                <th>Stock Actual</th>
                                <th>Total Salidas</th>
                                <th>Índice Rotación</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data['rotacion_inventario'] as $producto): ?>
                                <tr>
                                    <td><strong><?= htmlspecialchars($producto['nombre']) ?></strong></td>
                                    <td><?= htmlspecialchars($producto['categoria']) ?></td>
                                    <td><?= number_format($producto['stock_actual']) ?></td>
                                    <td><?= number_format($producto['total_salidas']) ?></td>
                                    <td>
                                        <span class="badge bg-<?= $producto['rotacion'] > 2 ? 'success' : ($producto['rotacion'] > 1 ? 'warning' : 'danger') ?>">
                                            <?= number_format($producto['rotacion'], 2) ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>

        <!-- Footer -->
        <div class="report-meta text-center">
            <small class="text-muted">
                <i class="fas fa-clock"></i>
                Reporte generado el <?= date('d/m/Y H:i:s') ?>
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
