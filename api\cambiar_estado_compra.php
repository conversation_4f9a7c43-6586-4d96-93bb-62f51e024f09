<?php

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();
require_once '../config/db.php';

header('Content-Type: application/json');

// Verificar que sea administrador
if (!isset($_SESSION['usuario_id'])) {
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

$stmt = $pdo->prepare("SELECT rol FROM usuarios WHERE id = ?");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario || $usuario['rol'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden cambiar estados de compras']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit();
}

try {
    $compra_id = $_POST['compra_id'] ?? '';
    $nuevo_estado = $_POST['estado'] ?? '';
    
    if (empty($compra_id) || empty($nuevo_estado)) {
        throw new Exception('ID de compra y estado requeridos');
    }

    // Validar que el estado sea válido
    $estados_validos = ['pendiente', 'recibida', 'cancelada'];
    if (!in_array($nuevo_estado, $estados_validos)) {
        throw new Exception('Estado no válido');
    }

    // Verificar que la compra existe
    $stmt = $pdo->prepare("SELECT estado FROM compras WHERE id = ?");
    $stmt->execute([$compra_id]);
    $compra = $stmt->fetch();

    if (!$compra) {
        throw new Exception('Compra no encontrada');
    }

    // Actualizar el estado
    $stmt = $pdo->prepare("
        UPDATE compras 
        SET estado = ?, fecha_actualizacion = NOW() 
        WHERE id = ?
    ");
    $stmt->execute([$nuevo_estado, $compra_id]);

    echo json_encode([
        'success' => true,
        'message' => 'Estado actualizado exitosamente',
        'compra_id' => $compra_id,
        'nuevo_estado' => $nuevo_estado
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
