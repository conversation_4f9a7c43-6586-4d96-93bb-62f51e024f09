# 📚 Documentación del Sistema de Restaurante

Bienvenido a la documentación completa del Sistema de Restaurante. Esta documentación está diseñada para ayudar tanto a usuarios como a desarrolladores a entender y utilizar el sistema de manera efectiva.

## 🎯 Acceso a la Documentación

### 📖 Para Usuarios
- **[Manual de Usuario](/Restaurante/docs/usuario.php)** - Guía completa para usar el sistema según tu rol
- **[Página Principal de Documentación](/Restaurante/docs/)** - Portal de acceso a toda la documentación

### 💻 Para Desarrolladores
- **[Documentación Técnica](/Restaurante/docs/desarrollador.php)** - Información técnica completa para desarrolladores

## 🏪 Sobre el Sistema

El Sistema de Restaurante es una plataforma web completa diseñada para optimizar las operaciones de restaurantes mediante:

- **Gestión de Órdenes**: Desde la toma hasta la entrega
- **Control de Mesas**: Apertura, seguimiento y cierre automático
- **Roles Específicos**: Administrador, Mesero, Cocina y Bebidas
- **Tiempo Real**: Actualizaciones instantáneas del estado de órdenes
- **Responsive Design**: Optimizado para móviles, tablets y escritorio

## 👥 Roles del Sistema

### 🍽️ Meseros
- Tomar órdenes de clientes
- Gestionar cuentas por mesa
- Seguimiento de estado de productos
- Procesar pagos y generar tickets

### 👨‍🍳 Cocina
- Ver órdenes de productos de cocina
- Actualizar estados de preparación
- Marcar productos como listos
- Gestionar cola de preparación

### 🥤 Bebidas
- Gestionar órdenes de bebidas
- Control de preparación de cocteles
- Coordinación con cocina para timing
- Actualización de estados

### ⚙️ Administradores
- Gestión completa del sistema
- Configuración de productos y precios
- Reportes y análisis de ventas
- Administración de empleados
- Configuración del restaurante

## 🛠️ Stack Tecnológico

### Backend
- **PHP 7.4+** - Lenguaje principal
- **MySQL 8.0+** - Base de datos
- **PDO** - Acceso a base de datos
- **Sesiones PHP** - Autenticación

### Frontend
- **HTML5 + CSS3** - Estructura y estilos
- **JavaScript ES6+** - Interactividad
- **Bootstrap 5.3** - Framework CSS
- **AdminLTE 4.0** - Tema administrativo
- **Bootstrap Icons** - Iconografía

## 📁 Estructura del Proyecto

```
/Restaurante/
├── admin/              # Panel de administración
├── api/               # Endpoints de API REST
├── assets/            # Recursos estáticos
├── auth/              # Sistema de autenticación
├── bebidas/           # Panel para área de bebidas
├── cocina/            # Panel para área de cocina
├── config/            # Configuración del sistema
├── docs/              # Documentación (esta carpeta)
├── includes/          # Archivos compartidos
├── meseros/           # Panel para meseros
└── database_setup.sql # Script de base de datos
```

## 🚀 Instalación Rápida

### Requisitos
- XAMPP/LAMP/WAMP
- PHP 7.4+
- MySQL 8.0+
- Navegador web moderno

### Pasos
1. **Clonar/Descargar** el proyecto en tu servidor web
2. **Importar** `database_setup.sql` en MySQL
3. **Configurar** `config/db.php` con tus credenciales
4. **Acceder** a `/Restaurante/` en tu navegador
5. **Login** con usuario admin por defecto

### Usuario por Defecto
- **Usuario**: admin
- **Contraseña**: 1234
- **Rol**: Administrador

## 📖 Guías Rápidas

### Para Meseros
1. Accede con tu usuario
2. Selecciona "Tomar Orden"
3. Elige mesa libre o con cuentas existentes
4. Crea cuentas para clientes
5. Agrega productos con notas especiales
6. Monitorea estados en tiempo real
7. Procesa pagos cuando esté listo

### Para Cocina/Bebidas
1. Accede con tu usuario de área
2. Ve órdenes pendientes en tu panel
3. Cambia estado a "Preparando" al iniciar
4. Marca como "Listo" al terminar
5. El mesero marcará como "Servido"

### Para Administradores
1. Accede al panel de administración
2. Configura productos y categorías
3. Gestiona empleados y permisos
4. Revisa reportes y estadísticas
5. Configura parámetros del restaurante

## 🔧 Características Principales

### ✨ Funcionalidades Destacadas
- **Cuentas Separadas**: Múltiples cuentas por mesa para facilitar pagos
- **Estados Independientes**: Cocina y bebidas manejan sus productos por separado
- **Notas Especiales**: Instrucciones detalladas para cada producto
- **Tickets Personalizables**: Con información del restaurante y cortesías
- **Reportes Detallados**: Análisis de ventas, productos y eficiencia
- **Responsive Design**: Funciona perfectamente en dispositivos móviles

### 🎨 Interfaz de Usuario
- **Diseño Moderno**: Interfaz limpia y profesional
- **Iconos Emoji**: Fácil identificación visual
- **Colores Consistentes**: Estados claramente diferenciados
- **Touch-Friendly**: Optimizado para pantallas táctiles
- **Notificaciones**: Feedback inmediato de acciones

## 🔒 Seguridad

- **Autenticación por Sesión**: Control de acceso seguro
- **Roles y Permisos**: Acceso limitado según función
- **Prepared Statements**: Protección contra inyección SQL
- **Validación de Entrada**: Sanitización de datos del usuario
- **Escape de Output**: Prevención de XSS

## 📊 Base de Datos

### Tablas Principales
- **usuarios**: Empleados y sus roles
- **mesas**: Control de mesas del restaurante
- **cuentas**: Cuentas individuales por cliente
- **ordenes**: Órdenes asociadas a cuentas
- **productos**: Catálogo del menú
- **detalle_orden**: Productos específicos en cada orden
- **categorias**: Organización del menú
- **configuracion**: Parámetros del restaurante

## 🆘 Soporte

### Problemas Comunes
1. **Error de conexión**: Verificar config/db.php
2. **Página en blanco**: Revisar logs de PHP
3. **Permisos**: Verificar permisos de carpetas
4. **Sesión expirada**: Volver a iniciar sesión

### Contacto
Para soporte técnico o consultas sobre el sistema, consulta la documentación técnica o contacta al administrador del sistema.

## 📝 Notas de Versión

### Características Implementadas
- ✅ Sistema completo de roles
- ✅ Gestión de mesas y cuentas
- ✅ Toma de órdenes con notas
- ✅ Estados independientes por área
- ✅ Panel administrativo completo
- ✅ Reportes y estadísticas
- ✅ Sistema de tickets
- ✅ Responsive design
- ✅ Documentación completa

### Próximas Mejoras
- 🔄 Notificaciones push en tiempo real
- 📱 App móvil nativa
- 🔌 Integración con sistemas de pago
- 📊 Dashboard avanzado con gráficos
- 🖨️ Integración con impresoras térmicas

---

**Sistema de Restaurante** - Desarrollado para optimizar operaciones de restaurantes con tecnología moderna y diseño intuitivo.
