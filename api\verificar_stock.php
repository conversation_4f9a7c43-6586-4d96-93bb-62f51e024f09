<?php
header('Content-Type: application/json');

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

require_once '../config/db.php';
require_once '../includes/inventario_helper.php';

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);
$productos = $input['productos'] ?? [];

// Validaciones
if (empty($productos) || !is_array($productos)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Lista de productos requerida']);
    exit();
}

try {
    $productos_verificados = [];
    $productos_sin_stock = [];
    $stock_insuficiente = [];
    
    foreach ($productos as $producto) {
        $producto_id = $producto['id'] ?? null;
        $cantidad = $producto['cantidad'] ?? 0;
        
        if (!$producto_id || $cantidad <= 0) {
            continue;
        }
        
        // Verificar stock disponible
        $verificacion = verificarStockDisponible($pdo, $producto_id, $cantidad);
        
        if (!$verificacion['disponible']) {
            if (strpos($verificacion['error'], 'no encontrado') !== false) {
                $productos_sin_stock[] = [
                    'producto_id' => $producto_id,
                    'cantidad_solicitada' => $cantidad,
                    'error' => $verificacion['error']
                ];
            } else {
                $stock_insuficiente[] = [
                    'producto_id' => $producto_id,
                    'producto_nombre' => $verificacion['producto_nombre'] ?? 'Producto desconocido',
                    'cantidad_solicitada' => $cantidad,
                    'stock_disponible' => $verificacion['stock_actual'] ?? 0,
                    'error' => $verificacion['error']
                ];
            }
        } else {
            $productos_verificados[] = [
                'producto_id' => $producto_id,
                'producto_nombre' => $verificacion['producto_nombre'],
                'cantidad_solicitada' => $cantidad,
                'stock_disponible' => $verificacion['stock_actual'],
                'stock_restante' => $verificacion['stock_actual'] - $cantidad
            ];
        }
    }
    
    // Determinar si la orden puede procesarse
    $puede_procesar = empty($productos_sin_stock) && empty($stock_insuficiente);
    
    $response = [
        'success' => true,
        'puede_procesar' => $puede_procesar,
        'productos_verificados' => $productos_verificados,
        'total_productos' => count($productos),
        'productos_disponibles' => count($productos_verificados)
    ];
    
    // Agregar errores si los hay
    if (!empty($productos_sin_stock)) {
        $response['productos_sin_inventario'] = $productos_sin_stock;
    }
    
    if (!empty($stock_insuficiente)) {
        $response['stock_insuficiente'] = $stock_insuficiente;
    }
    
    // Mensaje descriptivo
    if ($puede_procesar) {
        $response['message'] = 'Todos los productos tienen stock suficiente';
    } else {
        $errores = [];
        if (!empty($productos_sin_stock)) {
            $errores[] = count($productos_sin_stock) . ' producto(s) sin inventario';
        }
        if (!empty($stock_insuficiente)) {
            $errores[] = count($stock_insuficiente) . ' producto(s) con stock insuficiente';
        }
        $response['message'] = 'No se puede procesar la orden: ' . implode(', ', $errores);
    }
    
    echo json_encode($response);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
