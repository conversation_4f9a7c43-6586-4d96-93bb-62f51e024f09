<?php

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();
require_once '../config/db.php';

// Verificar autenticación y permisos de admin
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Verificar que sea admin
$stmt = $pdo->prepare("SELECT rol FROM usuarios WHERE id = ?");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario || $usuario['rol'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden ejecutar esta función']);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);
$accion = $input['accion'] ?? null;

try {
    $pdo->beginTransaction();
    $correcciones = [];
    
    switch ($accion) {
        case 'corregir_totales':
            // Corregir totales de cuentas
            $stmt = $pdo->query("
                SELECT c.id, c.nombre_cliente, c.total as total_bd,
                       COALESCE(SUM(d.subtotal), 0) as total_real
                FROM cuentas c
                LEFT JOIN ordenes o ON c.id = o.cuenta_id
                LEFT JOIN detalle_orden d ON o.id = d.orden_id
                WHERE c.estado = 'abierta'
                GROUP BY c.id
                HAVING ABS(c.total - COALESCE(SUM(d.subtotal), 0)) > 0.01
            ");
            $cuentas_incorrectas = $stmt->fetchAll();
            
            foreach ($cuentas_incorrectas as $cuenta) {
                $stmt = $pdo->prepare("UPDATE cuentas SET total = ? WHERE id = ?");
                $stmt->execute([$cuenta['total_real'], $cuenta['id']]);
                
                $correcciones[] = "Total de cuenta '{$cuenta['nombre_cliente']}' corregido: {$cuenta['total_bd']} → {$cuenta['total_real']}";
            }
            break;
            
        case 'limpiar_mesas_vacias':
            // Liberar mesas ocupadas sin cuentas abiertas
            $stmt = $pdo->query("
                SELECT m.numero_mesa, m.hash_mesa
                FROM mesas m
                LEFT JOIN cuentas c ON m.hash_mesa = c.hash_mesa AND c.estado = 'abierta'
                WHERE m.estado = 'ocupada' AND c.id IS NULL
            ");
            $mesas_sin_cuentas = $stmt->fetchAll();
            
            foreach ($mesas_sin_cuentas as $mesa) {
                $stmt = $pdo->prepare("UPDATE mesas SET estado = 'libre', mesero_id = NULL, fecha_apertura = NULL WHERE hash_mesa = ?");
                $stmt->execute([$mesa['hash_mesa']]);
                
                $correcciones[] = "Mesa {$mesa['numero_mesa']} liberada (no tenía cuentas abiertas)";
            }
            break;
            
        case 'todo':
            // Ejecutar todas las correcciones
            
            // 1. Corregir totales
            $stmt = $pdo->query("
                SELECT c.id, c.nombre_cliente, c.total as total_bd,
                       COALESCE(SUM(d.subtotal), 0) as total_real
                FROM cuentas c
                LEFT JOIN ordenes o ON c.id = o.cuenta_id
                LEFT JOIN detalle_orden d ON o.id = d.orden_id
                WHERE c.estado = 'abierta'
                GROUP BY c.id
                HAVING ABS(c.total - COALESCE(SUM(d.subtotal), 0)) > 0.01
            ");
            $cuentas_incorrectas = $stmt->fetchAll();
            
            foreach ($cuentas_incorrectas as $cuenta) {
                $stmt = $pdo->prepare("UPDATE cuentas SET total = ? WHERE id = ?");
                $stmt->execute([$cuenta['total_real'], $cuenta['id']]);
                
                $correcciones[] = "Total de cuenta '{$cuenta['nombre_cliente']}' corregido: {$cuenta['total_bd']} → {$cuenta['total_real']}";
            }
            
            // 2. Limpiar mesas vacías
            $stmt = $pdo->query("
                SELECT m.numero_mesa, m.hash_mesa
                FROM mesas m
                LEFT JOIN cuentas c ON m.hash_mesa = c.hash_mesa AND c.estado = 'abierta'
                WHERE m.estado = 'ocupada' AND c.id IS NULL
            ");
            $mesas_sin_cuentas = $stmt->fetchAll();
            
            foreach ($mesas_sin_cuentas as $mesa) {
                $stmt = $pdo->prepare("UPDATE mesas SET estado = 'libre', mesero_id = NULL, fecha_apertura = NULL WHERE hash_mesa = ?");
                $stmt->execute([$mesa['hash_mesa']]);
                
                $correcciones[] = "Mesa {$mesa['numero_mesa']} liberada (no tenía cuentas abiertas)";
            }
            break;
            
        default:
            throw new Exception('Acción no válida');
    }
    
    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'Correcciones aplicadas exitosamente',
        'correcciones' => $correcciones,
        'total_correcciones' => count($correcciones)
    ]);
    
} catch (Exception $e) {
    $pdo->rollBack();
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
