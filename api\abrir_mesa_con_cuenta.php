<?php
header('Content-Type: application/json');

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Verificar que sea un mesero o administrador
require_once '../config/db.php';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol IN ('mesero', 'admin')");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo meseros y administradores pueden abrir mesas']);
    exit();
}

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);
$numero_mesa = $input['mesa'] ?? null;
$nombre_cliente = trim($input['nombre_cliente'] ?? 'Cliente');
$apellido_cliente = trim($input['apellido_cliente'] ?? '');

if (!$numero_mesa || !is_numeric($numero_mesa)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Número de mesa inválido']);
    exit();
}

if (empty($nombre_cliente)) {
    $nombre_cliente = 'Cliente';
}

try {
    // Verificar que la mesa no esté ya ocupada
    $stmt = $pdo->prepare("SELECT * FROM mesas WHERE numero_mesa = ? AND estado = 'ocupada'");
    $stmt->execute([$numero_mesa]);
    $mesa_existente = $stmt->fetch();

    if ($mesa_existente) {
        echo json_encode(['success' => false, 'message' => 'La mesa ya está ocupada']);
        exit();
    }

    // Iniciar transacción
    $pdo->beginTransaction();

    // Generar hash único para la mesa
    $random_string = bin2hex(random_bytes(5)); // 10 caracteres
    $hash = sha1($random_string . date('d-m-Y') . $usuario['id']);

    // Abrir la mesa
    $stmt = $pdo->prepare("
        INSERT INTO mesas (numero_mesa, mesero_id, estado, fecha_apertura, hash_mesa)
        VALUES (?, ?, 'ocupada', NOW(), ?)
        ON DUPLICATE KEY UPDATE
        mesero_id = VALUES(mesero_id),
        estado = VALUES(estado),
        fecha_apertura = VALUES(fecha_apertura),
        hash_mesa = VALUES(hash_mesa)
    ");

    $stmt->execute([
        $numero_mesa,
        $usuario['id'],
        $hash
    ]);

    // Crear la primera cuenta automáticamente
    $stmt = $pdo->prepare("
        INSERT INTO cuentas (mesa, hash_mesa, mesero_id, nombre_cliente, apellido_cliente, estado) 
        VALUES (?, ?, ?, ?, ?, 'abierta')
    ");
    
    $stmt->execute([
        $numero_mesa,
        $hash,
        $usuario['id'],
        $nombre_cliente,
        $apellido_cliente
    ]);
    
    $cuenta_id = $pdo->lastInsertId();

    // Confirmar transacción
    $pdo->commit();

    // Obtener la cuenta creada con información completa
    $stmt = $pdo->prepare("
        SELECT c.*, u.nombre as mesero_nombre 
        FROM cuentas c 
        LEFT JOIN usuarios u ON c.mesero_id = u.id 
        WHERE c.id = ?
    ");
    $stmt->execute([$cuenta_id]);
    $cuenta = $stmt->fetch();

    echo json_encode([
        'success' => true,
        'message' => 'Mesa abierta y cuenta creada exitosamente',
        'hash' => $hash,
        'mesa' => $numero_mesa,
        'cuenta' => $cuenta
    ]);

} catch (PDOException $e) {
    // Rollback en caso de error
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
