<?php
header('Content-Type: application/json');

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Verificar permisos del usuario
require_once '../config/db.php';
require_once '../includes/inventario_helper.php';

$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ?");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Usuario no encontrado']);
    exit();
}

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);
$detalle_id = $input['detalle_id'] ?? null;
$nuevo_estado = $input['nuevo_estado'] ?? null;

// Validaciones
if (!$detalle_id || !$nuevo_estado) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Datos incompletos']);
    exit();
}

$estados_validos = ['pendiente', 'preparando', 'listo', 'servido'];
if (!in_array($nuevo_estado, $estados_validos)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Estado inválido']);
    exit();
}

try {
    // Verificar que el detalle existe y corresponde al área del usuario
    $stmt = $pdo->prepare("
        SELECT d.*, p.nombre as producto_nombre, p.tipo 
        FROM detalle_orden d 
        INNER JOIN productos p ON d.producto_id = p.id 
        WHERE d.id = ?
    ");
    $stmt->execute([$detalle_id]);
    $detalle = $stmt->fetch();
    
    if (!$detalle) {
        echo json_encode(['success' => false, 'message' => 'Detalle de orden no encontrado']);
        exit();
    }
    
    // Verificar permisos según el rol del usuario
    $puede_cambiar = false;

    switch ($usuario['rol']) {
        case 'administrador':
            $puede_cambiar = true;
            break;
        case 'cocinero':
        case 'cocina':
            $puede_cambiar = ($detalle['area'] === 'cocina');
            break;
        case 'bartender':
        case 'bebidas':
            $puede_cambiar = ($detalle['area'] === 'bebidas');
            break;
        case 'mesero':
            // Los meseros solo pueden marcar como servido
            $puede_cambiar = ($nuevo_estado === 'servido' && $detalle['estado'] === 'listo');
            break;
    }

    if (!$puede_cambiar) {
        echo json_encode(['success' => false, 'message' => 'No tienes permisos para cambiar este estado']);
        exit();
    }
    
    // Iniciar transacción para operaciones atómicas
    $pdo->beginTransaction();

    try {
        // Actualizar el estado del detalle
        $stmt = $pdo->prepare("UPDATE detalle_orden SET estado = ?, fecha_actualizacion = CURRENT_TIMESTAMP WHERE id = ?");
        $stmt->execute([$nuevo_estado, $detalle_id]);

        // Si se marca como servido, descontar del inventario automáticamente
        $inventario_resultado = null;
        if ($nuevo_estado === 'servido' && $detalle['estado'] !== 'servido') {
            try {
                $inventario_resultado = descontarInventarioVenta(
                    $pdo,
                    $detalle['producto_id'],
                    $detalle['cantidad'],
                    $detalle['orden_id'],
                    $usuario['id']
                );

                // Si hay error en el inventario, continuar pero registrar el error
                if (!$inventario_resultado['success']) {
                    error_log("Error al descontar inventario: " . $inventario_resultado['error']);
                    // No fallar la operación por problemas de inventario
                    $inventario_resultado['warning'] = true;
                }
            } catch (Exception $e) {
                error_log("Excepción al descontar inventario: " . $e->getMessage());
                $inventario_resultado = ['success' => false, 'error' => $e->getMessage(), 'warning' => true];
            }
        }

    // Actualizar el estado de la orden basado en los estados de todos los detalles
    $stmt = $pdo->prepare("
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN estado = 'pendiente' THEN 1 ELSE 0 END) as pendientes,
            SUM(CASE WHEN estado = 'preparando' THEN 1 ELSE 0 END) as preparando,
            SUM(CASE WHEN estado = 'listo' THEN 1 ELSE 0 END) as listos,
            SUM(CASE WHEN estado = 'servido' THEN 1 ELSE 0 END) as servidos
        FROM detalle_orden
        WHERE orden_id = ?
    ");
    $stmt->execute([$detalle['orden_id']]);
    $estadisticas = $stmt->fetch();

    // Determinar el nuevo estado de la orden
    $nuevo_estado_orden = 'pendiente';

    if ($estadisticas['servidos'] == $estadisticas['total']) {
        // Todos los detalles están servidos
        $nuevo_estado_orden = 'finalizada';
    } elseif ($estadisticas['listos'] > 0 || $estadisticas['servidos'] > 0) {
        // Algunos detalles están listos o servidos
        $nuevo_estado_orden = 'lista_parcial';
    } elseif ($estadisticas['preparando'] > 0) {
        // Algunos detalles están en preparación
        $nuevo_estado_orden = 'en_proceso';
    }

        // Actualizar el estado de la orden
        $stmt = $pdo->prepare("UPDATE ordenes SET estado = ?, fecha_actualizacion = CURRENT_TIMESTAMP WHERE id = ?");
        $stmt->execute([$nuevo_estado_orden, $detalle['orden_id']]);

        // Confirmar transacción
        $pdo->commit();

        // Preparar respuesta
        $response = [
            'success' => true,
            'message' => 'Estado actualizado correctamente',
            'data' => [
                'detalle_id' => $detalle_id,
                'estado_anterior' => $detalle['estado'],
                'estado_nuevo' => $nuevo_estado,
                'producto' => $detalle['producto_nombre'],
                'area' => $detalle['area'],
                'orden_id' => $detalle['orden_id'],
                'nuevo_estado_orden' => $nuevo_estado_orden,
                'estadisticas' => $estadisticas
            ]
        ];

        // Agregar información de inventario si se procesó
        if ($inventario_resultado) {
            $response['inventario'] = $inventario_resultado;
            if (isset($inventario_resultado['warning'])) {
                $response['message'] .= ' (Advertencia: problema con inventario)';
            } else if ($inventario_resultado['success']) {
                $response['message'] .= ' e inventario actualizado';
            }
        }

        echo json_encode($response);

    } catch (Exception $e) {
        // Rollback en caso de error
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        throw $e;
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
