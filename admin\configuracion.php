<?php
// Configurar variables para el layout
$page_title = 'Configuración del Restaurante';
$breadcrumbs = [
    ['title' => 'Inicio', 'url' => url('')],
    ['title' => 'Administración', 'url' => url('admin/')],
    ['title' => 'Configuración']
];

// Iniciar el buffer de contenido
ob_start();

// Conectar a la base de datos
require_once '../config/session_config.php';\nsession_start();
require_once '../config/db.php';

// Verificar que sea administrador
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    echo '<div class="alert alert-danger">Acceso denegado. Solo administradores pueden acceder.</div>';
    $content = ob_get_clean();
    include '../includes/layout.php';
    exit();
}

// Obtener configuración actual
try {
    $stmt = $pdo->query("SELECT * FROM configuracion WHERE id = 1");
    $config = $stmt->fetch();
    
    if (!$config) {
        // Si no existe configuración, crear una por defecto
        $stmt = $pdo->prepare("INSERT INTO configuracion (id, total_mesas, nombre_restaurante, descripcion_restaurante, direccion, telefono, moneda) VALUES (1, 8, 'Mi Restaurante', 'Bienvenidos a nuestro restaurante', 'Dirección del restaurante', '0000-0000', 'Q')");
        $stmt->execute();

        // Obtener la configuración recién creada
        $stmt = $pdo->query("SELECT * FROM configuracion WHERE id = 1");
        $config = $stmt->fetch();
    }
} catch (PDOException $e) {
    echo '<div class="alert alert-danger">Error al obtener la configuración: ' . $e->getMessage() . '</div>';
    $content = ob_get_clean();
    include '../includes/layout.php';
    exit();
}
?>

<!-- Encabezado de la página -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">
                    <i class="bi bi-gear-fill me-2 text-primary"></i>
                    Configuración del Restaurante
                </h1>
                <p class="text-muted mb-0">Gestiona la información básica de tu restaurante</p>
            </div>
            <div>
                <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    Actualizar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Formulario de configuración -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="bi bi-building me-2"></i>
                    Información del Restaurante
                </h5>
            </div>
            <div class="card-body p-4">
                <form id="formConfiguracion" method="POST">
                    <div class="row g-4">
                        <!-- Nombre del Restaurante -->
                        <div class="col-md-6">
                            <label for="nombre_restaurante" class="form-label fw-semibold">
                                <i class="bi bi-shop me-2"></i>
                                Nombre del Restaurante
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="nombre_restaurante" 
                                   name="nombre_restaurante" 
                                   value="<?= htmlspecialchars($config['nombre_restaurante']) ?>" 
                                   required
                                   maxlength="100">
                            <div class="form-text">Nombre que aparecerá en tickets y reportes</div>
                        </div>

                        <!-- Teléfono -->
                        <div class="col-md-6">
                            <label for="telefono" class="form-label fw-semibold">
                                <i class="bi bi-telephone me-2"></i>
                                Teléfono de Contacto
                            </label>
                            <input type="text"
                                   class="form-control"
                                   id="telefono"
                                   name="telefono"
                                   value="<?= htmlspecialchars($config['telefono'] ?? '') ?>"
                                   required
                                   maxlength="20"
                                   placeholder="2345-6789">
                            <div class="form-text">Número de contacto del restaurante</div>
                        </div>

                        <!-- Descripción -->
                        <div class="col-12">
                            <label for="descripcion_restaurante" class="form-label fw-semibold">
                                <i class="bi bi-card-text me-2"></i>
                                Descripción del Restaurante
                            </label>
                            <textarea class="form-control"
                                      id="descripcion_restaurante"
                                      name="descripcion_restaurante"
                                      rows="3"
                                      required
                                      maxlength="500"><?= htmlspecialchars($config['descripcion_restaurante'] ?? '') ?></textarea>
                            <div class="form-text">Descripción que aparecerá en tickets y documentos oficiales</div>
                        </div>

                        <!-- Dirección -->
                        <div class="col-12">
                            <label for="direccion" class="form-label fw-semibold">
                                <i class="bi bi-geo-alt me-2"></i>
                                Dirección del Restaurante
                            </label>
                            <input type="text"
                                   class="form-control"
                                   id="direccion"
                                   name="direccion"
                                   value="<?= htmlspecialchars($config['direccion'] ?? '') ?>"
                                   required
                                   maxlength="255"
                                   placeholder="Calle Principal #123, Ciudad, Departamento">
                            <div class="form-text">Dirección completa del restaurante</div>
                        </div>

                        <!-- Cantidad de Mesas -->
                        <div class="col-md-6">
                            <label for="total_mesas" class="form-label fw-semibold">
                                <i class="bi bi-table me-2"></i>
                                Cantidad de Mesas
                            </label>
                            <input type="number" 
                                   class="form-control" 
                                   id="total_mesas" 
                                   name="total_mesas" 
                                   value="<?= $config['total_mesas'] ?>" 
                                   required
                                   min="1"
                                   max="100">
                            <div class="form-text">Número total de mesas en el restaurante</div>
                        </div>

                        <!-- Moneda -->
                        <div class="col-md-6">
                            <label for="moneda" class="form-label fw-semibold">
                                <i class="bi bi-currency-exchange me-2"></i>
                                Moneda
                            </label>
                            <select class="form-select" id="moneda" name="moneda" required>
                                <option value="Q" <?= ($config['moneda'] ?? 'Q') == 'Q' ? 'selected' : '' ?>>Quetzales (Q)</option>
                                <option value="$" <?= ($config['moneda'] ?? 'Q') == '$' ? 'selected' : '' ?>>Dólares ($)</option>
                                <option value="€" <?= ($config['moneda'] ?? 'Q') == '€' ? 'selected' : '' ?>>Euros (€)</option>
                                <option value="COP" <?= ($config['moneda'] ?? 'Q') == 'COP' ? 'selected' : '' ?>>Pesos Colombianos (COP)</option>
                            </select>
                            <div class="form-text">Moneda utilizada en el restaurante</div>
                        </div>
                    </div>

                    <!-- Botones de acción -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <hr class="my-4">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Última actualización: <?= date('d/m/Y H:i', strtotime($config['updated_at'] ?? $config['fecha_actualizacion'] ?? 'now')) ?>
                                    </small>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-outline-secondary me-2" onclick="location.reload()">
                                        <i class="bi bi-arrow-clockwise me-2"></i>
                                        Cancelar
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-lg me-2"></i>
                                        Guardar Configuración
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Mensajes de Cortesía -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="bi bi-chat-heart me-2"></i>
                    Mensajes de Cortesía
                </h5>
                <small class="text-muted">Mensajes que se mostrarán en tickets, facturas y otros documentos</small>
            </div>
            <div class="card-body p-4">
                <!-- Lista de mensajes existentes -->
                <div id="listaMensajes" class="mb-4">
                    <?php
                    // Obtener mensajes existentes
                    $mensajes = [];
                    if (!empty($config['mensajes_cortesia'])) {
                        $mensajes = json_decode($config['mensajes_cortesia'], true) ?: [];
                    }

                    if (empty($mensajes)): ?>
                        <div class="text-center py-4" id="noMensajes">
                            <i class="bi bi-chat-dots text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-2">No hay mensajes de cortesía configurados</p>
                            <small class="text-muted">Agrega mensajes que aparecerán en tickets y documentos</small>
                        </div>
                    <?php else: ?>
                        <?php foreach ($mensajes as $index => $mensaje): ?>
                            <div class="mensaje-item border rounded p-3 mb-3" data-index="<?= $index ?>">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-2">
                                            <span class="badge bg-<?= $mensaje['activo'] ? 'success' : 'secondary' ?> me-2">
                                                <?= $mensaje['activo'] ? 'Activo' : 'Inactivo' ?>
                                            </span>
                                            <small class="text-muted">Prioridad: <?= $mensaje['prioridad'] ?></small>
                                        </div>
                                        <p class="mb-1 fw-semibold"><?= htmlspecialchars($mensaje['titulo']) ?></p>
                                        <p class="mb-0 text-muted"><?= htmlspecialchars($mensaje['mensaje']) ?></p>
                                    </div>
                                    <div class="ms-3">
                                        <button type="button" class="btn btn-sm btn-outline-primary me-1" onclick="editarMensaje(<?= $index ?>)">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="eliminarMensaje(<?= $index ?>)">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <!-- Formulario para agregar/editar mensaje -->
                <div class="border-top pt-4">
                    <h6 class="mb-3">
                        <i class="bi bi-plus-circle me-2"></i>
                        <span id="tituloFormMensaje">Agregar Nuevo Mensaje</span>
                    </h6>

                    <form id="formMensaje">
                        <input type="hidden" id="mensajeIndex" value="-1">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="mensajeTitulo" class="form-label fw-semibold">
                                    <i class="bi bi-tag me-2"></i>
                                    Título del Mensaje
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="mensajeTitulo"
                                       placeholder="Ej: Mensaje de agradecimiento"
                                       maxlength="100"
                                       required>
                                <div class="form-text">Título descriptivo para identificar el mensaje</div>
                            </div>

                            <div class="col-md-3">
                                <label for="mensajePrioridad" class="form-label fw-semibold">
                                    <i class="bi bi-sort-numeric-down me-2"></i>
                                    Prioridad
                                </label>
                                <input type="number"
                                       class="form-control"
                                       id="mensajePrioridad"
                                       min="1"
                                       max="10"
                                       value="1"
                                       required>
                                <div class="form-text">1 = Mayor prioridad</div>
                            </div>

                            <div class="col-md-3">
                                <label for="mensajeActivo" class="form-label fw-semibold">
                                    <i class="bi bi-toggle-on me-2"></i>
                                    Estado
                                </label>
                                <select class="form-select" id="mensajeActivo" required>
                                    <option value="1">Activo</option>
                                    <option value="0">Inactivo</option>
                                </select>
                                <div class="form-text">Solo los activos se mostrarán</div>
                            </div>

                            <div class="col-12">
                                <label for="mensajeTexto" class="form-label fw-semibold">
                                    <i class="bi bi-chat-text me-2"></i>
                                    Mensaje de Cortesía
                                </label>
                                <textarea class="form-control"
                                          id="mensajeTexto"
                                          rows="3"
                                          placeholder="Ej: ¡Gracias por visitarnos! Esperamos que haya disfrutado su experiencia."
                                          maxlength="500"
                                          required></textarea>
                                <div class="form-text">Mensaje que aparecerá en tickets y documentos (máximo 500 caracteres)</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end mt-3">
                            <button type="button" class="btn btn-outline-secondary me-2" onclick="cancelarMensaje()">
                                <i class="bi bi-x-lg me-2"></i>
                                Cancelar
                            </button>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-lg me-2"></i>
                                <span id="btnTextMensaje">Agregar Mensaje</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Información adicional -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light border-bottom">
                <h6 class="mb-0">
                    <i class="bi bi-lightbulb me-2"></i>
                    Información Importante
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="d-flex align-items-start">
                            <i class="bi bi-check-circle-fill text-success me-2 mt-1"></i>
                            <div>
                                <strong>Cambios en tiempo real:</strong>
                                <small class="d-block text-muted">Los cambios se aplicarán inmediatamente en todo el sistema.</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-start">
                            <i class="bi bi-table text-dark me-2 mt-1"></i>
                            <div>
                                <strong>Mesas:</strong>
                                <small class="d-block text-muted">Al cambiar la cantidad de mesas, se crearán o desactivarán automáticamente.</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// JavaScript adicional
$extra_js = '
<script>
document.getElementById("formConfiguracion").addEventListener("submit", function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector("button[type=submit]");
    const originalText = submitBtn.innerHTML;
    
    // Mostrar estado de carga
    submitBtn.disabled = true;
    submitBtn.innerHTML = `<i class="bi bi-hourglass-split me-2"></i>Guardando...`;
    
    fetch("/Restaurante/api/actualizar_configuracion.php", {
        method: "POST",
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Mostrar mensaje de éxito
            const alertDiv = document.createElement("div");
            alertDiv.className = "alert alert-success alert-dismissible fade show";
            alertDiv.innerHTML = `
                <i class="bi bi-check-circle me-2"></i>
                ${data.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            // Insertar el alert al inicio del contenido
            const container = document.querySelector(".row").parentNode;
            container.insertBefore(alertDiv, container.firstChild);
            
            // Scroll al top para mostrar el mensaje
            window.scrollTo({ top: 0, behavior: "smooth" });
            
            // Auto-cerrar el alert después de 5 segundos
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        } else {
            throw new Error(data.message || "Error desconocido");
        }
    })
    .catch(error => {
        // Mostrar mensaje de error
        const alertDiv = document.createElement("div");
        alertDiv.className = "alert alert-danger alert-dismissible fade show";
        alertDiv.innerHTML = `
            <i class="bi bi-exclamation-triangle me-2"></i>
            Error al guardar la configuración: ${error.message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector(".row").parentNode;
        container.insertBefore(alertDiv, container.firstChild);
        window.scrollTo({ top: 0, behavior: "smooth" });
    })
    .finally(() => {
        // Restaurar el botón
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// Variables globales para mensajes
let mensajesData = [];

// Cargar mensajes existentes
document.addEventListener("DOMContentLoaded", function() {
    cargarMensajes();
});

function cargarMensajes() {
    // Los mensajes ya están cargados desde PHP, solo necesitamos inicializar el array
    const mensajeItems = document.querySelectorAll(".mensaje-item");
    mensajesData = [];

    mensajeItems.forEach((item, index) => {
        const titulo = item.querySelector("p.fw-semibold").textContent;
        const mensaje = item.querySelector("p.text-muted").textContent;
        const activo = item.querySelector(".badge").textContent.trim() === "Activo";
        const prioridad = parseInt(item.querySelector("small").textContent.replace("Prioridad: ", ""));

        mensajesData.push({
            titulo: titulo,
            mensaje: mensaje,
            activo: activo,
            prioridad: prioridad
        });
    });
}

// Manejar formulario de mensajes
document.getElementById("formMensaje").addEventListener("submit", function(e) {
    e.preventDefault();

    const index = parseInt(document.getElementById("mensajeIndex").value);
    const titulo = document.getElementById("mensajeTitulo").value.trim();
    const mensaje = document.getElementById("mensajeTexto").value.trim();
    const prioridad = parseInt(document.getElementById("mensajePrioridad").value);
    const activo = document.getElementById("mensajeActivo").value === "1";

    if (!titulo || !mensaje) {
        mostrarAlerta("Por favor, completa todos los campos", "warning");
        return;
    }

    const nuevoMensaje = {
        titulo: titulo,
        mensaje: mensaje,
        prioridad: prioridad,
        activo: activo
    };

    if (index === -1) {
        // Agregar nuevo mensaje
        mensajesData.push(nuevoMensaje);
    } else {
        // Editar mensaje existente
        mensajesData[index] = nuevoMensaje;
    }

    // Ordenar por prioridad
    mensajesData.sort((a, b) => a.prioridad - b.prioridad);

    // Guardar mensajes
    guardarMensajes();
});

function editarMensaje(index) {
    const mensaje = mensajesData[index];

    document.getElementById("mensajeIndex").value = index;
    document.getElementById("mensajeTitulo").value = mensaje.titulo;
    document.getElementById("mensajeTexto").value = mensaje.mensaje;
    document.getElementById("mensajePrioridad").value = mensaje.prioridad;
    document.getElementById("mensajeActivo").value = mensaje.activo ? "1" : "0";

    document.getElementById("tituloFormMensaje").textContent = "Editar Mensaje";
    document.getElementById("btnTextMensaje").textContent = "Actualizar Mensaje";

    // Scroll al formulario
    document.getElementById("formMensaje").scrollIntoView({ behavior: "smooth" });
}

function eliminarMensaje(index) {
    // Eliminar directamente sin confirmación para mejor flujo de trabajo
    mensajesData.splice(index, 1);
    guardarMensajes();
    mostrarAlerta("Mensaje eliminado", "success");
}

function cancelarMensaje() {
    document.getElementById("formMensaje").reset();
    document.getElementById("mensajeIndex").value = "-1";
    document.getElementById("mensajePrioridad").value = "1";
    document.getElementById("tituloFormMensaje").textContent = "Agregar Nuevo Mensaje";
    document.getElementById("btnTextMensaje").textContent = "Agregar Mensaje";
}

function guardarMensajes() {
    const formData = new FormData();

    // Agregar datos de configuración existentes
    formData.append("nombre_restaurante", document.getElementById("nombre_restaurante").value);
    formData.append("descripcion_restaurante", document.getElementById("descripcion_restaurante").value);
    formData.append("direccion", document.getElementById("direccion").value);
    formData.append("telefono", document.getElementById("telefono").value);
    formData.append("total_mesas", document.getElementById("total_mesas").value);
    formData.append("moneda", document.getElementById("moneda").value);

    // Agregar mensajes de cortesía
    formData.append("mensajes_cortesia", JSON.stringify(mensajesData));

    fetch("/Restaurante/api/actualizar_configuracion.php", {
        method: "POST",
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            mostrarAlerta("Mensajes actualizados correctamente", "success");
            // Recargar la página para mostrar los cambios
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            throw new Error(data.message || "Error desconocido");
        }
    })
    .catch(error => {
        mostrarAlerta("Error al guardar mensajes: " + error.message, "danger");
    });
}

function mostrarAlerta(mensaje, tipo) {
    const alertDiv = document.createElement("div");
    alertDiv.className = "alert alert-" + tipo + " alert-dismissible fade show";

    let icono = "info-circle";
    if (tipo === "success") icono = "check-circle";
    if (tipo === "warning") icono = "exclamation-triangle";
    if (tipo === "danger") icono = "exclamation-triangle";

    alertDiv.innerHTML =
        "<i class=\"bi bi-" + icono + " me-2\"></i>" +
        mensaje +
        "<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>";

    const container = document.querySelector(".row").parentNode;
    container.insertBefore(alertDiv, container.firstChild);
    window.scrollTo({ top: 0, behavior: "smooth" });

    // Auto-cerrar después de 5 segundos
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
';

// CSS adicional
$extra_css = '
<style>
.card {
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    color: #495057;
}

.form-text {
    font-size: 0.875rem;
}

.alert {
    border: none;
    border-radius: 0.5rem;
}

.mensaje-item {
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.mensaje-item:hover {
    background-color: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.mensaje-item .badge {
    font-size: 0.75rem;
}

#formMensaje {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
}

#noMensajes {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
}
</style>
';

// Incluir el layout
include '../includes/layout.php';
?>
