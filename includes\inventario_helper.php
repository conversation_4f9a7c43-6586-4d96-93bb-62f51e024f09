<?php
/**
 * Funciones helper para gestión de inventario
 */

/**
 * Registra un movimiento de inventario y actualiza el stock
 */
function registrarMovimientoInventario($pdo, $producto_id, $tipo_movimiento, $cantidad, $referencia_tipo, $referencia_id, $motivo, $usuario_id, $costo_unitario = 0) {
    try {
        // Verificar si ya hay una transacción activa
        $transaccion_propia = false;
        if (!$pdo->inTransaction()) {
            $pdo->beginTransaction();
            $transaccion_propia = true;
        }

        // Asegurar que la cantidad sea un entero
        $cantidad = (int) $cantidad;

        // Obtener stock actual
        $stmt = $pdo->prepare("SELECT stock_actual FROM inventario WHERE producto_id = ?");
        $stmt->execute([$producto_id]);
        $inventario = $stmt->fetch();

        if (!$inventario) {
            throw new Exception("Producto no encontrado en inventario");
        }

        $stock_anterior = (int) $inventario['stock_actual'];
        
        // Calcular nuevo stock según el tipo de movimiento
        switch ($tipo_movimiento) {
            case 'entrada':
                $stock_nuevo = $stock_anterior + $cantidad;
                break;
            case 'salida':
                $stock_nuevo = $stock_anterior - $cantidad;
                if ($stock_nuevo < 0) {
                    throw new Exception("Stock insuficiente. Stock actual: $stock_anterior, cantidad solicitada: $cantidad");
                }
                break;
            case 'ajuste':
                $stock_nuevo = $cantidad; // En ajustes, la cantidad es el nuevo stock total
                break;
            case 'merma':
                $stock_nuevo = $stock_anterior - $cantidad;
                if ($stock_nuevo < 0) {
                    $stock_nuevo = 0; // En mermas permitimos que llegue a 0
                }
                break;
            default:
                throw new Exception("Tipo de movimiento no válido");
        }
        
        // Actualizar stock en inventario
        $stmt = $pdo->prepare("
            UPDATE inventario 
            SET stock_actual = ?, fecha_actualizacion = NOW() 
            WHERE producto_id = ?
        ");
        $stmt->execute([$stock_nuevo, $producto_id]);
        
        // Registrar movimiento en historial
        $stmt = $pdo->prepare("
            INSERT INTO movimientos_inventario (
                producto_id, tipo_movimiento, cantidad, stock_anterior, stock_nuevo, 
                costo_unitario, referencia_tipo, referencia_id, motivo, usuario_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $producto_id,
            $tipo_movimiento,
            $cantidad,
            $stock_anterior,
            $stock_nuevo,
            $costo_unitario,
            $referencia_tipo,
            $referencia_id,
            $motivo,
            $usuario_id
        ]);
        
        // Confirmar transacción solo si la iniciamos nosotros
        if ($transaccion_propia) {
            $pdo->commit();
        }
        
        return [
            'success' => true,
            'stock_anterior' => $stock_anterior,
            'stock_nuevo' => $stock_nuevo,
            'movimiento_id' => $pdo->lastInsertId()
        ];
        
    } catch (Exception $e) {
        // Rollback en caso de error solo si iniciamos la transacción
        if ($transaccion_propia && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Descuenta automáticamente del inventario cuando se sirve un producto
 */
function descontarInventarioVenta($pdo, $producto_id, $cantidad, $orden_id, $usuario_id) {
    $motivo = "Venta automática - Orden #$orden_id";
    
    return registrarMovimientoInventario(
        $pdo, 
        $producto_id, 
        'salida', 
        $cantidad, 
        'venta', 
        $orden_id, 
        $motivo, 
        $usuario_id
    );
}

/**
 * Verifica si hay stock suficiente para una venta
 */
function verificarStockDisponible($pdo, $producto_id, $cantidad_solicitada) {
    try {
        // Asegurar que la cantidad solicitada sea un entero
        $cantidad_solicitada = (int) $cantidad_solicitada;

        $stmt = $pdo->prepare("
            SELECT i.stock_actual, p.nombre as producto_nombre
            FROM inventario i
            LEFT JOIN productos p ON i.producto_id = p.id
            WHERE i.producto_id = ?
        ");
        $stmt->execute([$producto_id]);
        $inventario = $stmt->fetch();

        if (!$inventario) {
            return [
                'disponible' => false,
                'error' => 'Producto no encontrado en inventario'
            ];
        }

        $stock_actual = (int) $inventario['stock_actual'];

        if ($stock_actual < $cantidad_solicitada) {
            return [
                'disponible' => false,
                'error' => "Stock insuficiente para {$inventario['producto_nombre']}. Disponible: $stock_actual, Solicitado: $cantidad_solicitada"
            ];
        }

        return [
            'disponible' => true,
            'stock_actual' => $stock_actual,
            'producto_nombre' => $inventario['producto_nombre']
        ];
        
    } catch (PDOException $e) {
        return [
            'disponible' => false,
            'error' => 'Error al verificar stock: ' . $e->getMessage()
        ];
    }
}

/**
 * Obtiene productos con stock bajo
 */
function obtenerProductosStockBajo($pdo) {
    try {
        $stmt = $pdo->query("
            SELECT 
                i.*,
                p.nombre as producto_nombre,
                p.area,
                c.nombre as categoria_nombre
            FROM inventario i
            LEFT JOIN productos p ON i.producto_id = p.id
            LEFT JOIN categorias c ON p.categoria_id = c.id
            WHERE i.stock_actual <= i.stock_minimo
            ORDER BY (i.stock_actual / i.stock_minimo) ASC
        ");
        
        return $stmt->fetchAll();
        
    } catch (PDOException $e) {
        return [];
    }
}

/**
 * Obtiene el historial de movimientos de un producto
 */
function obtenerHistorialMovimientos($pdo, $producto_id, $limite = 50) {
    try {
        $stmt = $pdo->prepare("
            SELECT 
                m.*,
                u.nombre as usuario_nombre,
                p.nombre as producto_nombre
            FROM movimientos_inventario m
            LEFT JOIN usuarios u ON m.usuario_id = u.id
            LEFT JOIN productos p ON m.producto_id = p.id
            WHERE m.producto_id = ?
            ORDER BY m.fecha_movimiento DESC
            LIMIT ?
        ");
        
        $stmt->execute([$producto_id, $limite]);
        return $stmt->fetchAll();
        
    } catch (PDOException $e) {
        return [];
    }
}

/**
 * Inicializa inventario para un producto nuevo
 */
function inicializarInventarioProducto($pdo, $producto_id, $stock_inicial = 0, $stock_minimo = 10, $costo_promedio = 0) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO inventario (producto_id, stock_actual, stock_minimo, stock_maximo, unidad_medida, costo_promedio)
            VALUES (?, ?, ?, ?, 'unidad', ?)
            ON DUPLICATE KEY UPDATE
            stock_minimo = VALUES(stock_minimo),
            costo_promedio = VALUES(costo_promedio)
        ");
        
        $stmt->execute([$producto_id, $stock_inicial, $stock_minimo, $stock_inicial * 2, $costo_promedio]);
        
        return true;
        
    } catch (PDOException $e) {
        return false;
    }
}
?>
