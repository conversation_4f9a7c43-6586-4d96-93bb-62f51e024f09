<?php
// Layout público para documentación - NO requiere autenticación
?>
<!doctype html>
<html lang="es">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title><?= isset($page_title) ? $page_title : 'Documentación - Sistema Restaurante' ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="author" content="Sistema Restaurante" />
    <meta name="description" content="Documentación completa del Sistema de Restaurante - Guías para usuarios y desarrolladores" />
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Third Party Plugins -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" crossorigin="anonymous" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" crossorigin="anonymous" />
    
    <!-- Custom CSS for Documentation -->
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #28a745;
            --info-color: #17a2b8;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        
        .docs-navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .docs-navbar .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
        }
        
        .docs-navbar .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .docs-navbar .nav-link:hover {
            color: white !important;
            transform: translateY(-1px);
        }
        
        .docs-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }
        
        .docs-footer {
            background: var(--dark-color);
            color: white;
            padding: 2rem 0;
            margin-top: 4rem;
        }
        
        .docs-footer a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .docs-footer a:hover {
            color: white;
            text-decoration: underline;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .docs-container {
                padding: 1rem 0.5rem;
            }
        }
    </style>
    
    <?php if (isset($extra_css)): ?>
        <?= $extra_css ?>
    <?php endif; ?>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg docs-navbar">
        <div class="container">
            <a class="navbar-brand" href="/Restaurante/docs/">
                <i class="bi bi-book me-2"></i>
                Documentación
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/Restaurante/docs/">
                            <i class="bi bi-house me-1"></i>
                            Inicio
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/Restaurante/docs/usuario.php">
                            <i class="bi bi-person me-1"></i>
                            Manual Usuario
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/Restaurante/docs/desarrollador.php">
                            <i class="bi bi-code me-1"></i>
                            Documentación Técnica
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/Restaurante/">
                            <i class="bi bi-arrow-left me-1"></i>
                            Volver al Sistema
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="docs-container">
        <!-- Breadcrumbs -->
        <?php if (isset($breadcrumbs) && !empty($breadcrumbs)): ?>
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <?php foreach ($breadcrumbs as $breadcrumb): ?>
                    <?php if (isset($breadcrumb['url'])): ?>
                        <li class="breadcrumb-item">
                            <a href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['title'] ?></a>
                        </li>
                    <?php else: ?>
                        <li class="breadcrumb-item active"><?= $breadcrumb['title'] ?></li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ol>
        </nav>
        <?php endif; ?>
        
        <!-- Page Content -->
        <?php if (isset($content)): ?>
            <?= $content ?>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="docs-footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Sistema de Restaurante</h5>
                    <p class="mb-0">Documentación completa para usuarios y desarrolladores</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <a href="/Restaurante/">Acceder al Sistema</a> |
                        <a href="/Restaurante/docs/">Documentación</a>
                    </p>
                    <small class="text-muted">
                        © <?= date('Y') ?> Sistema de Restaurante
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.min.js" crossorigin="anonymous"></script>
    
    <?php if (isset($extra_js)): ?>
        <?= $extra_js ?>
    <?php endif; ?>
</body>
</html>
