<?php
header('Content-Type: application/json');

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();

// Verificar que sea una petición POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit();
}

// Verificar que el usuario esté logueado y sea administrador
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

require_once '../config/db.php';

try {
    // Verificar que sea administrador
    $stmt = $pdo->prepare("SELECT rol FROM usuarios WHERE id = ?");
    $stmt->execute([$_SESSION['usuario_id']]);
    $usuario = $stmt->fetch();
    
    if (!$usuario || $usuario['rol'] !== 'admin') {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Acceso denegado. Solo administradores pueden modificar la configuración.']);
        exit();
    }
    
    // Validar datos recibidos
    $required_fields = ['nombre_restaurante', 'descripcion_restaurante', 'direccion', 'telefono', 'total_mesas', 'moneda'];
    $missing_fields = [];

    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || trim($_POST[$field]) === '') {
            $missing_fields[] = $field;
        }
    }

    if (!empty($missing_fields)) {
        echo json_encode(['success' => false, 'message' => 'Campos requeridos faltantes: ' . implode(', ', $missing_fields)]);
        exit();
    }

    // Sanitizar y validar datos
    $nombre_restaurante = trim($_POST['nombre_restaurante']);
    $descripcion_restaurante = trim($_POST['descripcion_restaurante']);
    $direccion = trim($_POST['direccion']);
    $telefono = trim($_POST['telefono']);
    $total_mesas = intval($_POST['total_mesas']);
    $moneda = trim($_POST['moneda']);

    // Procesar mensajes de cortesía (opcional)
    $mensajes_cortesia = null;
    if (isset($_POST['mensajes_cortesia']) && !empty($_POST['mensajes_cortesia'])) {
        $mensajes_json = $_POST['mensajes_cortesia'];
        $mensajes_array = json_decode($mensajes_json, true);

        if (json_last_error() === JSON_ERROR_NONE && is_array($mensajes_array)) {
            // Validar estructura de cada mensaje
            $mensajes_validados = [];
            foreach ($mensajes_array as $mensaje) {
                if (isset($mensaje['titulo'], $mensaje['mensaje'], $mensaje['prioridad'], $mensaje['activo'])) {
                    $mensajes_validados[] = [
                        'titulo' => trim($mensaje['titulo']),
                        'mensaje' => trim($mensaje['mensaje']),
                        'prioridad' => intval($mensaje['prioridad']),
                        'activo' => (bool)$mensaje['activo']
                    ];
                }
            }

            if (!empty($mensajes_validados)) {
                $mensajes_cortesia = json_encode($mensajes_validados, JSON_UNESCAPED_UNICODE);
            }
        }
    }
    
    // Validaciones específicas
    if (strlen($nombre_restaurante) > 100) {
        echo json_encode(['success' => false, 'message' => 'El nombre del restaurante no puede exceder 100 caracteres']);
        exit();
    }
    
    if (strlen($descripcion_restaurante) > 500) {
        echo json_encode(['success' => false, 'message' => 'La descripción no puede exceder 500 caracteres']);
        exit();
    }
    
    if (strlen($direccion) > 255) {
        echo json_encode(['success' => false, 'message' => 'La dirección no puede exceder 255 caracteres']);
        exit();
    }

    if (strlen($telefono) > 20) {
        echo json_encode(['success' => false, 'message' => 'El teléfono no puede exceder 20 caracteres']);
        exit();
    }
    
    if ($total_mesas < 1 || $total_mesas > 100) {
        echo json_encode(['success' => false, 'message' => 'El número de mesas debe estar entre 1 y 100']);
        exit();
    }
    
    $monedas_validas = ['Q', '$', '€', 'COP'];
    if (!in_array($moneda, $monedas_validas)) {
        echo json_encode(['success' => false, 'message' => 'Moneda no válida']);
        exit();
    }
    
    // Iniciar transacción
    $pdo->beginTransaction();
    
    // Obtener configuración actual para comparar mesas
    $stmt = $pdo->query("SELECT total_mesas FROM configuracion WHERE id = 1");
    $config_actual = $stmt->fetch();
    $mesas_actuales = $config_actual ? $config_actual['total_mesas'] : 0;
    
    // Actualizar configuración
    $stmt = $pdo->prepare("
        UPDATE configuracion
        SET nombre_restaurante = ?,
            descripcion_restaurante = ?,
            direccion = ?,
            telefono = ?,
            total_mesas = ?,
            moneda = ?,
            mensajes_cortesia = ?
        WHERE id = 1
    ");

    $result = $stmt->execute([
        $nombre_restaurante,
        $descripcion_restaurante,
        $direccion,
        $telefono,
        $total_mesas,
        $moneda,
        $mensajes_cortesia
    ]);

    if (!$result) {
        throw new Exception('Error al actualizar la configuración');
    }

    // Si no existe configuración, crearla
    if ($stmt->rowCount() === 0) {
        $stmt = $pdo->prepare("
            INSERT INTO configuracion (id, nombre_restaurante, descripcion_restaurante, direccion, telefono, total_mesas, moneda, mensajes_cortesia)
            VALUES (1, ?, ?, ?, ?, ?, ?, ?)
        ");

        $result = $stmt->execute([
            $nombre_restaurante,
            $descripcion_restaurante,
            $direccion,
            $telefono,
            $total_mesas,
            $moneda,
            $mensajes_cortesia
        ]);

        if (!$result) {
            throw new Exception('Error al crear la configuración');
        }
    }
    
    // Gestionar mesas si cambió la cantidad
    if ($total_mesas != $mesas_actuales) {
        if ($total_mesas > $mesas_actuales) {
            // Agregar mesas nuevas
            for ($i = $mesas_actuales + 1; $i <= $total_mesas; $i++) {
                $stmt = $pdo->prepare("INSERT IGNORE INTO mesas (numero_mesa) VALUES (?)");
                $stmt->execute([$i]);
            }
        } else {
            // Verificar si hay mesas ocupadas que se van a eliminar
            $stmt = $pdo->prepare("SELECT COUNT(*) as ocupadas FROM mesas WHERE numero_mesa > ? AND estado = 'ocupada'");
            $stmt->execute([$total_mesas]);
            $mesas_ocupadas = $stmt->fetch()['ocupadas'];
            
            if ($mesas_ocupadas > 0) {
                $pdo->rollBack();
                echo json_encode(['success' => false, 'message' => "No se puede reducir el número de mesas. Hay $mesas_ocupadas mesa(s) ocupada(s) con número mayor a $total_mesas."]);
                exit();
            }
            
            // Eliminar mesas que exceden el nuevo límite
            $stmt = $pdo->prepare("DELETE FROM mesas WHERE numero_mesa > ?");
            $stmt->execute([$total_mesas]);
        }
    }
    
    // Confirmar transacción
    $pdo->commit();
    
    // Respuesta exitosa
    echo json_encode([
        'success' => true, 
        'message' => 'Configuración actualizada correctamente',
        'data' => [
            'nombre_restaurante' => $nombre_restaurante,
            'descripcion_restaurante' => $descripcion_restaurante,
            'direccion' => $direccion,
            'telefono' => $telefono,
            'total_mesas' => $total_mesas,
            'moneda' => $moneda,
            'mensajes_cortesia' => $mensajes_cortesia,
            'mesas_actualizadas' => $total_mesas != $mesas_actuales
        ]
    ]);
    
} catch (PDOException $e) {
    // Rollback en caso de error de base de datos
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("Error en actualizar_configuracion.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
    
} catch (Exception $e) {
    // Rollback en caso de error general
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("Error en actualizar_configuracion.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
