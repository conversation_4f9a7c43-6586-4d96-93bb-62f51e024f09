<?php
header('Content-Type: application/json');

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Verificar que sea un mesero
require_once '../config/db.php';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'mesero'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo los meseros pueden abrir mesas']);
    exit();
}

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);
$numero_mesa = $input['mesa'] ?? null;

if (!$numero_mesa || !is_numeric($numero_mesa)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Número de mesa inválido']);
    exit();
}

try {
    // Verificar que la mesa no esté ya ocupada
    $stmt = $pdo->prepare("SELECT * FROM mesas WHERE numero_mesa = ? AND estado = 'ocupada'");
    $stmt->execute([$numero_mesa]);
    $mesa_existente = $stmt->fetch();

    if ($mesa_existente) {
        echo json_encode(['success' => false, 'message' => 'La mesa ya está ocupada']);
        exit();
    }

    // Generar hash único para la mesa
    $random_string = bin2hex(random_bytes(5)); // 10 caracteres
    $hash = sha1($random_string . date('d-m-Y') . $usuario['id']);

    // Insertar o actualizar la mesa
    $stmt = $pdo->prepare("
        INSERT INTO mesas (numero_mesa, mesero_id, estado, fecha_apertura, hash_mesa)
        VALUES (?, ?, 'ocupada', NOW(), ?)
        ON DUPLICATE KEY UPDATE
        mesero_id = VALUES(mesero_id),
        estado = VALUES(estado),
        fecha_apertura = VALUES(fecha_apertura),
        hash_mesa = VALUES(hash_mesa)
    ");

    $stmt->execute([
        $numero_mesa,
        $usuario['id'], // Usamos el ID del usuario actual
        $hash
    ]);

    echo json_encode([
        'success' => true,
        'message' => 'Mesa abierta exitosamente',
        'hash' => $hash,
        'mesa' => $numero_mesa
    ]);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
