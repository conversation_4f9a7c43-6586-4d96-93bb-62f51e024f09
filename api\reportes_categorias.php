<?php
header('Content-Type: application/json');

// Iniciar sesión y conectar a la base de datos

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();
require_once '../config/db.php';

// Verificar que el usuario esté logueado y sea administrador
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden acceder a reportes']);
    exit();
}

// Obtener parámetros
$fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-m-d', strtotime('-7 days'));
$fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-d');
$mesero_id = $_GET['mesero_id'] ?? '';

// Validar fechas
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_inicio) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_fin)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Formato de fecha inválido']);
    exit();
}

try {
    // Obtener ventas por área (cocina vs bebidas)
    $sql = "
        SELECT p.area,
               COUNT(d.id) as total_items,
               SUM(d.cantidad) as cantidad_total,
               SUM(d.subtotal) as total_ventas,
               AVG(d.precio_unitario) as precio_promedio,
               COUNT(DISTINCT p.id) as productos_diferentes
        FROM detalle_orden d
        INNER JOIN productos p ON d.producto_id = p.id
        INNER JOIN ordenes o ON d.orden_id = o.id
        WHERE o.fecha_hora >= ? AND o.fecha_hora <= ?
        AND o.estado = 'finalizada'
    ";
    
    $params = [$fecha_inicio . ' 00:00:00', $fecha_fin . ' 23:59:59'];
    
    // Filtrar por mesero si se especifica
    if (!empty($mesero_id) && is_numeric($mesero_id)) {
        $sql .= " AND o.mesero_id = ?";
        $params[] = $mesero_id;
    }
    
    $sql .= " GROUP BY p.area ORDER BY total_ventas DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $categorias = $stmt->fetchAll();
    
    // Obtener productos más vendidos por área
    $productos_por_area = [];
    
    foreach (['cocina', 'bebidas'] as $area) {
        $sql_productos = "
            SELECT p.nombre, 
                   SUM(d.cantidad) as cantidad_vendida,
                   SUM(d.subtotal) as ingresos
            FROM detalle_orden d
            INNER JOIN productos p ON d.producto_id = p.id
            INNER JOIN ordenes o ON d.orden_id = o.id
            WHERE o.fecha_hora >= ? AND o.fecha_hora <= ?
            AND o.estado = 'finalizada'
            AND p.area = ?
        ";
        
        $params_productos = [$fecha_inicio . ' 00:00:00', $fecha_fin . ' 23:59:59', $area];
        
        if (!empty($mesero_id) && is_numeric($mesero_id)) {
            $sql_productos .= " AND o.mesero_id = ?";
            $params_productos[] = $mesero_id;
        }
        
        $sql_productos .= " 
            GROUP BY p.id, p.nombre
            ORDER BY cantidad_vendida DESC
            LIMIT 5
        ";
        
        $stmt_productos = $pdo->prepare($sql_productos);
        $stmt_productos->execute($params_productos);
        $productos_por_area[$area] = $stmt_productos->fetchAll();
    }
    
    // Calcular porcentajes
    $total_ventas = array_sum(array_column($categorias, 'total_ventas'));
    
    foreach ($categorias as &$categoria) {
        $categoria['porcentaje'] = $total_ventas > 0 ? 
            round(($categoria['total_ventas'] / $total_ventas) * 100, 1) : 0;
    }
    
    // Obtener tendencias por hora del día
    $sql_horas = "
        SELECT HOUR(o.fecha_hora) as hora,
               p.area,
               COUNT(d.id) as total_items,
               SUM(d.subtotal) as ventas_hora
        FROM detalle_orden d
        INNER JOIN productos p ON d.producto_id = p.id
        INNER JOIN ordenes o ON d.orden_id = o.id
        WHERE o.fecha_hora >= ? AND o.fecha_hora <= ?
        AND o.estado = 'finalizada'
    ";
    
    $params_horas = [$fecha_inicio . ' 00:00:00', $fecha_fin . ' 23:59:59'];
    
    if (!empty($mesero_id) && is_numeric($mesero_id)) {
        $sql_horas .= " AND o.mesero_id = ?";
        $params_horas[] = $mesero_id;
    }
    
    $sql_horas .= " GROUP BY HOUR(o.fecha_hora), p.area ORDER BY hora ASC";
    
    $stmt_horas = $pdo->prepare($sql_horas);
    $stmt_horas->execute($params_horas);
    $tendencias_horas = $stmt_horas->fetchAll();
    
    // Respuesta JSON
    echo json_encode([
        'success' => true,
        'categorias' => $categorias,
        'productos_por_area' => $productos_por_area,
        'tendencias_horas' => $tendencias_horas,
        'estadisticas' => [
            'total_ventas_periodo' => $total_ventas,
            'total_items' => array_sum(array_column($categorias, 'total_items')),
            'fecha_inicio' => $fecha_inicio,
            'fecha_fin' => $fecha_fin
        ]
    ]);
    
} catch (PDOException $e) {
    error_log("Error en reportes_categorias.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error al generar reporte de categorías',
        'categorias' => []
    ]);
}
?>
