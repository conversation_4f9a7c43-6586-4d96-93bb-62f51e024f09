<?php
// Iniciar sesión siempre
require_once '../config/session_config.php';
session_start();
require_once '../config/db.php';

// Verificar hash de mesa y cuenta_id
$hash_mesa = $_GET['hash'] ?? null;
$cuenta_id = $_GET['cuenta_id'] ?? null;
$cliente_nombre = $_GET['cliente'] ?? '';
$mesa_numero = null;
$cuenta_info = null;

// Si viene con hash, verificar que la mesa existe y pertenece al mesero
if ($hash_mesa) {
    $stmt = $pdo->prepare("SELECT * FROM mesas WHERE hash_mesa = ? AND estado = 'ocupada'");
    $stmt->execute([$hash_mesa]);
    $mesa_info = $stmt->fetch();

    if ($mesa_info) {
        $mesa_numero = $mesa_info['numero_mesa'];

        // Si viene con cuenta_id, verificar que la cuenta existe
        if ($cuenta_id) {
            $stmt = $pdo->prepare("SELECT * FROM cuentas WHERE id = ? AND hash_mesa = ? AND estado = 'abierta'");
            $stmt->execute([$cuenta_id, $hash_mesa]);
            $cuenta_info = $stmt->fetch();
        }
    }
}

// Configurar variables para el layout
if ($cuenta_info) {
    $page_title = "Agregar a Cuenta - " . $cuenta_info['nombre_cliente'] . " (Mesa #$mesa_numero)";
} else {
    $page_title = $mesa_numero ? "Tomar Orden - Mesa #$mesa_numero" : 'Tomar Orden';
}

$breadcrumbs = [
    ['title' => 'Inicio', 'url' => url('')],
    ['title' => 'Meseros', 'url' => url('meseros/')],
    ['title' => 'Tomar Orden']
];

// Iniciar el buffer de contenido
ob_start();

// Obtener productos por categoría
try {
    // Obtener productos con información de categoría
    $stmt = $pdo->query("
        SELECT p.*, c.nombre as categoria_nombre
        FROM productos p
        LEFT JOIN categorias c ON p.categoria_id = c.id
        WHERE p.disponible = 1
        ORDER BY c.nombre, p.nombre
    ");
    $productos = $stmt->fetchAll();

    // Obtener categorías activas que tienen productos
    $stmt = $pdo->query("
        SELECT DISTINCT c.id, c.nombre
        FROM categorias c
        INNER JOIN productos p ON c.id = p.categoria_id
        WHERE c.activo = 1 AND p.disponible = 1
        ORDER BY c.nombre
    ");
    $categorias = $stmt->fetchAll();

    // Agrupar productos por categoría
    $productos_por_categoria = [];
    foreach ($productos as $producto) {
        $categoria_key = $producto['categoria_id'] ?? 'sin_categoria';
        $productos_por_categoria[$categoria_key][] = $producto;
    }

} catch (PDOException $e) {
    $productos = [];
    $categorias = [];
    $productos_por_categoria = [];
}

// Obtener mesas disponibles si no viene con hash
if (!$hash_mesa) {
    try {
        // Obtener configuración para saber el total de mesas
        $stmt = $pdo->query("SELECT total_mesas FROM configuracion WHERE id = 1");
        $config_mesas = $stmt->fetch();
        $total_mesas = $config_mesas ? (int)$config_mesas['total_mesas'] : 10;

        // Obtener mis mesas ocupadas
        $stmt = $pdo->prepare("SELECT numero_mesa, hash_mesa, 'ocupada' as estado_mesa FROM mesas WHERE estado = 'ocupada' AND mesero_id = ?");
        $stmt->execute([$_SESSION['usuario_id']]);
        $mis_mesas_ocupadas = $stmt->fetchAll();

        // Obtener todas las mesas ocupadas para saber cuáles están libres
        $stmt = $pdo->query("SELECT numero_mesa FROM mesas WHERE estado = 'ocupada'");
        $mesas_ocupadas = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // Crear array con todas las mesas (ocupadas y libres)
        $mis_mesas = [];

        // Agregar mis mesas ocupadas
        foreach ($mis_mesas_ocupadas as $mesa) {
            $mis_mesas[] = $mesa;
        }

        // Agregar mesas libres
        for ($i = 1; $i <= $total_mesas; $i++) {
            if (!in_array($i, $mesas_ocupadas)) {
                $mis_mesas[] = [
                    'numero_mesa' => $i,
                    'hash_mesa' => null,
                    'estado_mesa' => 'libre'
                ];
            }
        }

        // Ordenar por número de mesa
        usort($mis_mesas, function($a, $b) {
            return $a['numero_mesa'] - $b['numero_mesa'];
        });

    } catch (PDOException $e) {
        $mis_mesas = [];
    }
}
?>

<!-- Selector de Mesa (si no viene con hash) -->
<?php if (!$hash_mesa): ?>
<!-- Título de sección -->
<div class="row mb-3">
    <div class="col-12">
        <h4 class="fw-bold text-dark d-flex align-items-center">
            <i class="bi bi-table me-2 text-primary"></i>
            Seleccionar Mesa
        </h4>
    </div>
</div>

<div class="row g-4 mb-4">
    <div class="col-12">
        <?php if (empty($mis_mesas)): ?>
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                No hay mesas disponibles en este momento. Las mesas se mostrarán automáticamente cuando estén disponibles.
            </div>
        <?php else: ?>
            <!-- Vista Desktop/Tablet -->
            <div class="d-none d-md-block">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <tbody>
                            <?php foreach ($mis_mesas as $mesa): ?>
                                <tr class="mesa-row" data-mesa="<?= $mesa['numero_mesa'] ?>">
                                    <td class="align-middle" style="width: 120px;">
                                        <div class="d-flex align-items-center">
                                            <?php if (isset($mesa['estado_mesa']) && $mesa['estado_mesa'] === 'libre'): ?>
                                                <div class="bg-success bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                                    <i class="bi bi-circle text-success"></i>
                                                </div>
                                            <?php else: ?>
                                                <div class="rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px; background-color: rgba(111, 66, 193, 0.1);">
                                                    <i class="bi bi-circle-fill" style="color: #6f42c1;"></i>
                                                </div>
                                            <?php endif; ?>
                                            <strong>Mesa #<?= $mesa['numero_mesa'] ?></strong>
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        <?php if (isset($mesa['estado_mesa']) && $mesa['estado_mesa'] === 'libre'): ?>
                                            <div class="text-muted">
                                                <em>Mesa libre</em>
                                            </div>
                                        <?php else: ?>
                                            <div class="cuentas-mesa" data-hash="<?= $mesa['hash_mesa'] ?>">
                                                <div class="d-flex align-items-center">
                                                    <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                                        <span class="visually-hidden">Cargando...</span>
                                                    </div>
                                                    <small class="text-muted">Cargando cuentas...</small>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="align-middle text-end" style="width: 200px;">
                                        <?php if (isset($mesa['estado_mesa']) && $mesa['estado_mesa'] === 'libre'): ?>
                                            <button class="btn btn-success btn-sm rounded-pill crear-cuenta-btn" data-mesa="<?= $mesa['numero_mesa'] ?>">
                                                <i class="bi bi-person-plus me-1"></i>
                                                Crear Cuenta
                                            </button>
                                        <?php else: ?>
                                            <a href="/Restaurante/meseros/mesa_cuentas.php?hash=<?= $mesa['hash_mesa'] ?>" class="btn btn-outline-secondary btn-sm rounded-pill">
                                                <i class="bi bi-eye me-1"></i>
                                                Ver Mesa
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Vista Mobile -->
            <div class="d-md-none">
                <div class="row g-3">
                    <?php foreach ($mis_mesas as $mesa): ?>
                        <div class="col-12">
                            <div class="card border-0 shadow-sm mesa-card-mobile" data-mesa="<?= $mesa['numero_mesa'] ?>">
                                <div class="card-body p-3">
                                            <!-- Header de la mesa -->
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <div class="d-flex align-items-center">
                                                    <?php if (isset($mesa['estado_mesa']) && $mesa['estado_mesa'] === 'libre'): ?>
                                                        <div class="bg-success bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 35px; height: 35px;">
                                                            <i class="bi bi-circle text-success fs-5"></i>
                                                        </div>
                                                    <?php else: ?>
                                                        <div class="rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 35px; height: 35px; background-color: rgba(111, 66, 193, 0.1);">
                                                            <i class="bi bi-circle-fill text-primary fs-5" style="color: #6f42c1;"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <h6 class="mb-0 fw-bold">Mesa #<?= $mesa['numero_mesa'] ?></h6>
                                                </div>

                                                <!-- Botón de acción principal -->
                                                <?php if (isset($mesa['estado_mesa']) && $mesa['estado_mesa'] === 'libre'): ?>
                                                    <button class="btn btn-success btn-sm rounded-pill crear-cuenta-btn" data-mesa="<?= $mesa['numero_mesa'] ?>">
                                                        <i class="bi bi-person-plus me-1"></i>
                                                        Crear Cuenta
                                                    </button>
                                                <?php else: ?>
                                                    <a href="/Restaurante/meseros/mesa_cuentas.php?hash=<?= $mesa['hash_mesa'] ?>" class="btn btn-outline-secondary btn-sm rounded-pill">
                                                        <i class="bi bi-eye me-1"></i>
                                                        Ver Mesa
                                                    </a>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Contenido de cuentas -->
                                            <?php if (isset($mesa['estado_mesa']) && $mesa['estado_mesa'] === 'libre'): ?>
                                                <div class="text-muted text-center py-2">
                                                    <small><em>Mesa libre - Toca "Crear Cuenta" para empezar</em></small>
                                                </div>
                                            <?php else: ?>
                                                <div class="cuentas-mesa-mobile" data-hash="<?= $mesa['hash_mesa'] ?>">
                                                    <div class="d-flex align-items-center justify-content-center py-3">
                                                        <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                                            <span class="visually-hidden">Cargando...</span>
                                                        </div>
                                                        <small class="text-muted">Cargando cuentas...</small>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Leyenda -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="d-flex justify-content-center gap-4">
                                <div class="d-flex align-items-center">
                                    <div class="bg-success bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 20px; height: 20px;">
                                        <i class="bi bi-circle text-success" style="font-size: 0.8rem;"></i>
                                    </div>
                                    <small class="text-muted">Mesa libre</small>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 20px; height: 20px; background-color: rgba(111, 66, 193, 0.1);">
                                        <i class="bi bi-circle-fill" style="font-size: 0.8rem; color: #6f42c1;"></i>
                                    </div>
                                    <small class="text-muted">Mesa ocupada</small>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Interfaz de Tomar Orden -->
<?php if ($hash_mesa && $mesa_numero): ?>
<div class="row g-4">
    <!-- Panel de Productos -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-grid-3x3-gap me-2"></i>
                        Menú de Productos
                    </h5>
                    <div class="btn-group" role="group">
                        <button class="btn btn-outline-primary btn-sm active" data-filter="all">
                            <i class="bi bi-grid-3x3-gap me-1"></i>
                            Todos
                        </button>
                        <?php foreach ($categorias as $categoria): ?>
                            <button class="btn btn-outline-primary btn-sm" data-filter="categoria-<?= $categoria['id'] ?>">
                                <i class="bi <?= $categoria['nombre'] === 'Bebidas' ? 'bi-cup-straw' : ($categoria['nombre'] === 'Postres' ? 'bi-cake2' : 'bi-egg-fried') ?> me-1"></i>
                                <?= htmlspecialchars($categoria['nombre']) ?>
                            </button>
                        <?php endforeach; ?>
                        <?php if (isset($productos_por_categoria['sin_categoria']) && !empty($productos_por_categoria['sin_categoria'])): ?>
                            <button class="btn btn-outline-primary btn-sm" data-filter="sin-categoria">
                                <i class="bi bi-question-circle me-1"></i>
                                Sin Categoría
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <div class="row g-3" id="productos-grid">
                    <?php foreach ($productos_por_categoria as $categoria_id => $productos_categoria): ?>
                        <?php foreach ($productos_categoria as $producto): ?>
                            <div class="col-md-6 col-lg-4 producto-item"
                                 data-categoria="<?= $categoria_id === 'sin_categoria' ? 'sin-categoria' : 'categoria-' . $categoria_id ?>"
                                 data-area="<?= $producto['area'] ?>"
                                 data-categoria-nombre="<?= htmlspecialchars($producto['categoria_nombre'] ?? 'Sin categoría') ?>">
                                <div class="card h-100 producto-card border-0 shadow-sm" data-producto-id="<?= $producto['id'] ?>">
                                    <!-- Imagen del producto -->
                                    <div class="position-relative">
                                        <?php if (!empty($producto['imagen']) && file_exists("../assets/img/productos/" . $producto['imagen'])): ?>
                                            <img src="/Restaurante/assets/img/productos/<?= htmlspecialchars($producto['imagen']) ?>"
                                                 class="card-img-top producto-imagen"
                                                 alt="<?= htmlspecialchars($producto['nombre']) ?>"
                                                 style="height: 180px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 180px;">
                                                <div class="text-center">
                                                    <i class="bi <?= $producto['area'] === 'cocina' ? 'bi-egg-fried' : 'bi-cup-straw' ?> text-<?= $producto['area'] === 'cocina' ? 'danger' : 'info' ?> fs-1 mb-2"></i>
                                                    <div class="text-muted small">Sin imagen</div>
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Badge del área -->
                                        <div class="position-absolute top-0 end-0 m-2">
                                            <span class="badge bg-<?= $producto['area'] === 'cocina' ? 'danger' : 'info' ?> rounded-pill">
                                                <i class="bi <?= $producto['area'] === 'cocina' ? 'bi-egg-fried' : 'bi-cup-straw' ?> me-1"></i>
                                                <?= ucfirst($producto['area']) ?>
                                            </span>
                                        </div>
                                    </div>

                                    <div class="card-body p-3 d-flex flex-column">
                                        <h6 class="card-title mb-2 text-center"><?= htmlspecialchars($producto['nombre']) ?></h6>
                                        <p class="card-text text-muted small mb-3 text-center flex-grow-1"><?= htmlspecialchars($producto['descripcion']) ?></p>

                                        <!-- Precio -->
                                        <div class="text-center mb-3">
                                            <span class="fw-bold text-success fs-5">Q<?= number_format($producto['precio_venta'] ?? $producto['precio'] ?? 0, 2) ?></span>
                                        </div>

                                        <!-- Botones de acción -->
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-primary btn-sm agregar-producto-btn"
                                                    data-id="<?= $producto['id'] ?>"
                                                    data-nombre="<?= htmlspecialchars($producto['nombre']) ?>"
                                                    data-precio="<?= $producto['precio_venta'] ?? $producto['precio'] ?? 0 ?>"
                                                    data-area="<?= $producto['area'] ?>">
                                                <i class="bi bi-plus-circle me-1"></i>
                                                Agregar
                                            </button>
                                            <button class="btn btn-outline-primary btn-sm agregar-con-nota-btn"
                                                    data-id="<?= $producto['id'] ?>"
                                                    data-nombre="<?= htmlspecialchars($producto['nombre']) ?>"
                                                    data-precio="<?= $producto['precio_venta'] ?? $producto['precio'] ?? 0 ?>"
                                                    data-area="<?= $producto['area'] ?>"
                                                    title="Agregar con nota">
                                                <i class="bi bi-chat-left-text me-1"></i>
                                                Agregar con Nota
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Panel de Orden Actual -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm sticky-top" style="top: 20px;">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-receipt me-2"></i>
                    Mesa #<?= $mesa_numero ?>
                </h5>
                <?php if ($cuenta_info): ?>
                    <small class="opacity-75">
                        <i class="bi bi-person me-1"></i>
                        <?= htmlspecialchars($cuenta_info['nombre_cliente']) ?>
                        <?php if (!empty($cuenta_info['apellido_cliente'])): ?>
                            <?= htmlspecialchars($cuenta_info['apellido_cliente']) ?>
                        <?php endif; ?>
                    </small>
                <?php endif; ?>
            </div>
            <div class="card-body p-0">
                <?php if (!$cuenta_info): ?>
                <div class="p-3 border-bottom">
                    <div class="mb-3">
                        <label class="form-label small fw-semibold">Nombre de la cuenta (opcional)</label>
                        <input type="text" class="form-control form-control-sm" id="nombre-cuenta" placeholder="Ej: Cliente 1, Mesa principal...">
                    </div>
                </div>
                <?php else: ?>
                <div class="p-3 border-bottom bg-light">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-info-circle text-primary me-2"></i>
                        <small class="text-muted">
                            Agregando productos a la cuenta de <strong><?= htmlspecialchars($cuenta_info['nombre_cliente']) ?></strong>
                        </small>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Lista de productos seleccionados -->
                <div id="orden-items" class="p-3" style="max-height: 400px; overflow-y: auto;">
                    <div class="text-center text-muted py-4" id="orden-vacia">
                        <i class="bi bi-cart3 fs-2 mb-2 d-block"></i>
                        <small>Selecciona productos del menú</small>
                    </div>
                </div>
                
                <!-- Total y acciones -->
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="fw-semibold">Total:</span>
                        <span class="fw-bold text-primary fs-5" id="total-orden">Q0.00</span>
                    </div>
                    <div class="d-grid gap-2">
                        <button class="btn btn-success rounded-pill" id="enviar-orden-btn" disabled>
                            <i class="bi bi-send me-2"></i>
                            Enviar Orden
                        </button>
                        <button class="btn btn-outline-secondary rounded-pill" id="limpiar-orden-btn">
                            <i class="bi bi-trash me-2"></i>
                            Limpiar Todo
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Botón para volver -->
<div class="row mt-4">
    <div class="col-12">
        <?php if ($cuenta_info): ?>
            <a href="/Restaurante/meseros/cuenta_detalle.php?cuenta_id=<?= $cuenta_id ?>" class="btn btn-outline-secondary rounded-pill me-2">
                <i class="bi bi-arrow-left me-2"></i>
                Volver a Cuenta
            </a>
            <a href="/Restaurante/meseros/mesa_cuentas.php?hash=<?= $hash_mesa ?>" class="btn btn-outline-primary rounded-pill">
                <i class="bi bi-table me-2"></i>
                Ver Todas las Cuentas
            </a>
        <?php else: ?>
            <a href="/Restaurante/meseros/mesa_cuentas.php?hash=<?= $hash_mesa ?>" class="btn btn-outline-secondary rounded-pill">
                <i class="bi bi-arrow-left me-2"></i>
                Volver a Mesa #<?= $mesa_numero ?>
            </a>
        <?php endif; ?>
    </div>
</div>

<?php else: ?>
    <?php if ($hash_mesa && !$mesa_numero): ?>
        <div class="alert alert-danger">
            <i class="bi bi-exclamation-triangle me-2"></i>
            Mesa no encontrada o no tienes permisos para acceder a ella.
        </div>
    <?php endif; ?>
<?php endif; ?>

<!-- Modal para crear cuenta nueva -->
<div class="modal fade" id="crearCuentaModal" tabindex="-1" aria-labelledby="crearCuentaModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="crearCuentaModalLabel">
                    <i class="bi bi-person-plus me-2"></i>
                    Crear Nueva Cuenta
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label fw-semibold">Mesa:</label>
                    <div id="mesa-seleccionada" class="text-primary fw-bold"></div>
                </div>
                <div class="mb-3">
                    <label for="nombre-cliente-nuevo" class="form-label fw-semibold">Nombre del cliente:</label>
                    <input type="text" class="form-control" id="nombre-cliente-nuevo" placeholder="Ej: Juan Pérez" required>
                </div>
                <div class="mb-3">
                    <label for="apellido-cliente-nuevo" class="form-label">Apellido (opcional):</label>
                    <input type="text" class="form-control" id="apellido-cliente-nuevo" placeholder="Ej: García">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-success" id="confirmar-crear-cuenta">
                    <i class="bi bi-person-plus me-1"></i>
                    Crear Cuenta y Abrir Mesa
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para agregar notas -->
<div class="modal fade" id="notaModal" tabindex="-1" aria-labelledby="notaModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="notaModalLabel">
                    <i class="bi bi-chat-left-text me-2"></i>
                    Agregar Nota al Producto
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label fw-semibold">Producto:</label>
                    <div id="producto-seleccionado" class="text-primary fw-bold"></div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-semibold">Notas rápidas:</label>
                    <div class="d-flex flex-wrap gap-2 mb-3">
                        <button type="button" class="btn btn-outline-secondary btn-sm nota-rapida-btn" data-nota="Sin cebolla">
                            Sin cebolla
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm nota-rapida-btn" data-nota="Sin queso">
                            Sin queso
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm nota-rapida-btn" data-nota="Sin salsa">
                            Sin salsa
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm nota-rapida-btn" data-nota="Extra queso">
                            Extra queso
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm nota-rapida-btn" data-nota="Término medio">
                            Término medio
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm nota-rapida-btn" data-nota="Bien cocido">
                            Bien cocido
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm nota-rapida-btn" data-nota="Sin picante">
                            Sin picante
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm nota-rapida-btn" data-nota="Extra picante">
                            Extra picante
                        </button>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="nota-producto" class="form-label fw-semibold">Nota personalizada:</label>
                    <textarea class="form-control" id="nota-producto" rows="2"
                              placeholder="Escribe una nota personalizada si es necesario..."></textarea>
                    <div class="form-text">Las notas rápidas se agregarán automáticamente. Usa este campo para notas adicionales.</div>
                </div>

                <div class="mb-3" id="notas-seleccionadas-container" style="display: none;">
                    <label class="form-label fw-semibold">Notas seleccionadas:</label>
                    <div id="notas-seleccionadas" class="d-flex flex-wrap gap-1"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="confirmar-agregar-nota">
                    <i class="bi bi-plus-circle me-1"></i>
                    Agregar Producto
                </button>
            </div>
        </div>
    </div>
</div>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// CSS adicional
$extra_css = '
<style>
.producto-card {
    transition: all 0.3s ease;
    cursor: pointer;
    border-radius: 12px !important;
    overflow: hidden;
}

.producto-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 35px rgba(0,0,0,0.15) !important;
}

.producto-imagen {
    transition: transform 0.3s ease;
    border-radius: 0;
}

.producto-card:hover .producto-imagen {
    transform: scale(1.05);
}

.producto-card .card-body {
    min-height: 200px;
}

.producto-card .btn {
    border-radius: 8px !important;
    font-weight: 500;
    transition: all 0.2s ease;
}

.producto-card .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.orden-item {
    border-bottom: 1px solid #e9ecef;
    padding: 0.75rem 0;
}

.orden-item:last-child {
    border-bottom: none;
}

.cantidad-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cantidad-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 1px solid #dee2e6;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
}

.cantidad-btn:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
}

.cantidad-input {
    width: 50px;
    text-align: center;
    border: none;
    background: transparent;
    font-weight: 600;
}

.sticky-top {
    position: sticky !important;
}

@media (max-width: 991px) {
    .sticky-top {
        position: relative !important;
    }
}

.agregar-producto-btn {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
}

.agregar-producto-btn:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-2px);
}

.agregar-con-nota-btn {
    border: 2px solid #0d6efd;
    color: #0d6efd;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
}

.agregar-con-nota-btn:hover {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
    transform: translateY(-2px);
}

.nota-rapida-btn {
    transition: all 0.2s ease;
    font-size: 0.8rem;
    padding: 0.375rem 0.75rem;
}

.nota-rapida-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nota-rapida-btn.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

#notas-seleccionadas .badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
}

#notas-seleccionadas-container {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
    border: 1px solid #dee2e6;
}

/* Estilos adicionales para mejorar la apariencia */
.card-header {
    border-radius: 12px 12px 0 0 !important;
}

.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    font-weight: 500;
}

.text-success {
    color: #198754 !important;
    font-weight: 700;
}

/* Animación para los filtros */
.btn-group .btn {
    transition: all 0.3s ease;
    border-radius: 20px !important;
    margin: 0 2px;
    font-weight: 500;
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
}

.btn-group .btn.active {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    border-color: #0d6efd;
    color: white;
}

.btn-group .btn:hover:not(.active) {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Responsive para filtros */
@media (max-width: 768px) {
    .btn-group {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .btn-group .btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
        margin: 0;
    }
}

/* Estilos para botones de mesa morados */
.btn-outline-primary[style*="6f42c1"]:hover {
    background-color: #6f42c1 !important;
    border-color: #6f42c1 !important;
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(111, 66, 193, 0.3);
}

/* Estilos para la vista de tabla de mesas */
.mesa-row {
    transition: all 0.2s ease;
}

.mesa-row:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: translateX(2px);
}

.cuenta-item {
    transition: all 0.2s ease;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    margin: 0.1rem 0;
}

.cuenta-item:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.cuentas-list {
    max-height: 200px;
    overflow-y: auto;
}

.table td {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    vertical-align: middle;
}

.table tbody tr:first-child td {
    border-top: none;
}

/* Estilos para vista móvil */
.mesa-card-mobile {
    transition: all 0.3s ease;
    border-radius: 12px;
}

.mesa-card-mobile:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
}

.mesa-card-mobile .card-body {
    border-radius: 12px;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .mesa-card-mobile .btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    .mesa-card-mobile h6 {
        font-size: 1rem;
    }

    .cuenta-item {
        padding: 0.5rem;
        margin: 0.2rem 0;
    }

    .btn-group-sm .btn {
        padding: 0.3rem 0.6rem;
        font-size: 0.75rem;
    }
}

/* Mejoras para touch devices */
@media (hover: none) and (pointer: coarse) {
    .mesa-card-mobile {
        transition: none;
    }

    .mesa-card-mobile:hover {
        transform: none;
    }

    .btn {
        min-height: 44px; /* Tamaño mínimo recomendado para touch */
    }

    .btn-sm {
        min-height: 38px;
    }
}

/* Mejoras para responsive */
@media (max-width: 768px) {
    .producto-card .card-body {
        min-height: auto;
        padding: 1rem;
    }

    .producto-card .btn {
        font-size: 0.8rem;
        padding: 0.5rem 1rem;
    }

    .producto-imagen,
    .card-img-top {
        height: 150px !important;
    }
}
</style>
';

// JavaScript adicional
$extra_js = '
<script>
let ordenActual = [];
let totalOrden = 0;

document.addEventListener("DOMContentLoaded", function() {
    // Manejar creación de cuentas en mesas libres
    document.querySelectorAll(".crear-cuenta-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const numeroMesa = this.dataset.mesa;
            mostrarModalCrearCuenta(numeroMesa);
        });
    });

    // Cargar cuentas de mesas ocupadas (vista desktop)
    document.querySelectorAll(".cuentas-mesa").forEach(container => {
        const hashMesa = container.dataset.hash;
        cargarCuentasMesa(hashMesa, container);
    });

    // Cargar cuentas de mesas ocupadas (vista mobile)
    document.querySelectorAll(".cuentas-mesa-mobile").forEach(container => {
        const hashMesa = container.dataset.hash;
        cargarCuentasMesaMobile(hashMesa, container);
    });

    // Filtros de productos por categoría
    document.querySelectorAll("[data-filter]").forEach(btn => {
        btn.addEventListener("click", function() {
            const filter = this.dataset.filter;

            // Actualizar botones activos
            document.querySelectorAll("[data-filter]").forEach(b => b.classList.remove("active"));
            this.classList.add("active");

            // Filtrar productos
            document.querySelectorAll(".producto-item").forEach(item => {
                if (filter === "all" || item.dataset.categoria === filter) {
                    item.style.display = "block";
                    // Agregar animación de entrada
                    item.style.opacity = "0";
                    item.style.transform = "translateY(20px)";
                    setTimeout(() => {
                        item.style.transition = "all 0.3s ease";
                        item.style.opacity = "1";
                        item.style.transform = "translateY(0)";
                    }, 50);
                } else {
                    item.style.display = "none";
                }
            });

            // Contar productos visibles
            let productosVisibles = 0;
            document.querySelectorAll(".producto-item").forEach(item => {
                if (item.style.display !== "none") {
                    productosVisibles++;
                }
            });

            // Remover mensaje anterior si existe
            const grid = document.getElementById("productos-grid");
            const mensajeAnterior = grid.querySelector(".mensaje-sin-productos");
            if (mensajeAnterior) {
                mensajeAnterior.remove();
            }

            // Mostrar mensaje si no hay productos
            if (productosVisibles === 0 && filter !== "all") {
                const mensaje = document.createElement("div");
                mensaje.className = "col-12 mensaje-sin-productos";
                mensaje.innerHTML = "<div class=\"text-center py-5\"><i class=\"bi bi-search text-muted fs-1 mb-3\"></i><h5 class=\"text-muted\">No hay productos en esta categoría</h5><p class=\"text-muted\">Selecciona otra categoría para ver más productos.</p></div>";
                grid.appendChild(mensaje);
            }
        });
    });
    
    // Agregar productos sin nota
    document.querySelectorAll(".agregar-producto-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const producto = {
                id: parseInt(this.dataset.id),
                nombre: this.dataset.nombre,
                precio: parseFloat(this.dataset.precio),
                area: this.dataset.area,
                cantidad: 1,
                notas: ""
            };

            agregarProductoAOrden(producto);
        });
    });

    // Agregar productos con nota
    document.querySelectorAll(".agregar-con-nota-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const producto = {
                id: parseInt(this.dataset.id),
                nombre: this.dataset.nombre,
                precio: parseFloat(this.dataset.precio),
                area: this.dataset.area,
                cantidad: 1
            };

            // Mostrar el modal para capturar la nota
            document.getElementById("producto-seleccionado").textContent = producto.nombre;
            document.getElementById("nota-producto").value = "";

            // Limpiar notas rápidas seleccionadas
            limpiarNotasRapidas();

            // Guardar el producto temporalmente
            window.productoTemporal = producto;
            window.notasRapidasSeleccionadas = [];

            // Mostrar el modal
            const modal = new bootstrap.Modal(document.getElementById("notaModal"));
            modal.show();
        });
    });

    // Manejar notas rápidas
    document.querySelectorAll(".nota-rapida-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const nota = this.dataset.nota;
            toggleNotaRapida(nota, this);
        });
    });

    // Confirmar crear cuenta
    document.getElementById("confirmar-crear-cuenta").addEventListener("click", function() {
        const numeroMesa = window.mesaSeleccionada;
        const nombreCliente = document.getElementById("nombre-cliente-nuevo").value.trim();
        const apellidoCliente = document.getElementById("apellido-cliente-nuevo").value.trim();

        if (!nombreCliente) {
            showToast("Por favor ingresa el nombre del cliente", "warning");
            return;
        }

        crearCuentaYAbrirMesa(numeroMesa, nombreCliente, apellidoCliente);
    });

    // Confirmar agregar producto con nota
    document.getElementById("confirmar-agregar-nota").addEventListener("click", function() {
        const notaPersonalizada = document.getElementById("nota-producto").value.trim();
        const producto = window.productoTemporal;

        if (producto) {
            // Combinar notas rápidas con nota personalizada
            const todasLasNotas = [];

            if (window.notasRapidasSeleccionadas && window.notasRapidasSeleccionadas.length > 0) {
                todasLasNotas.push(...window.notasRapidasSeleccionadas);
            }

            if (notaPersonalizada) {
                todasLasNotas.push(notaPersonalizada);
            }

            producto.notas = todasLasNotas.join(", ");
            agregarProductoAOrden(producto);

            // Cerrar el modal
            const modal = bootstrap.Modal.getInstance(document.getElementById("notaModal"));
            modal.hide();

            // Limpiar producto temporal y notas
            window.productoTemporal = null;
            window.notasRapidasSeleccionadas = [];
        }
    });
    
    // Enviar orden
    const enviarBtn = document.getElementById("enviar-orden-btn");
    if (enviarBtn) {
        enviarBtn.addEventListener("click", function() {
            if (ordenActual.length > 0) {
                enviarOrden();
            }
        });
    }

    // Limpiar orden
    const limpiarBtn = document.getElementById("limpiar-orden-btn");
    if (limpiarBtn) {
        limpiarBtn.addEventListener("click", function() {
            // Limpiar orden directamente
            limpiarOrden();
        });
    }
});

function agregarProductoAOrden(producto) {
    // Verificar si el producto ya existe (mismo ID y mismas notas)
    const existente = ordenActual.find(item =>
        item.id === producto.id &&
        (item.notas || "") === (producto.notas || "")
    );

    if (existente) {
        existente.cantidad++;
    } else {
        ordenActual.push(producto);
    }

    actualizarVistaOrden();
}

function actualizarVistaOrden() {
    const container = document.getElementById("orden-items");
    const totalElement = document.getElementById("total-orden");
    const enviarBtn = document.getElementById("enviar-orden-btn");

    // Verificar que los elementos esenciales existan
    if (!container || !totalElement || !enviarBtn) {
        console.error("Error: No se pudieron encontrar todos los elementos necesarios");
        console.log("Container:", container);
        console.log("TotalElement:", totalElement);
        console.log("EnviarBtn:", enviarBtn);
        return;
    }

    if (ordenActual.length === 0) {
        // Mostrar mensaje de orden vacía
        container.innerHTML = `
            <div class="text-center text-muted py-4" id="orden-vacia">
                <i class="bi bi-cart3 fs-2 mb-2 d-block"></i>
                <small>Selecciona productos del menú</small>
            </div>
        `;
        enviarBtn.disabled = true;
        totalOrden = 0;
    } else {
        enviarBtn.disabled = false;

        let html = "";
        totalOrden = 0;

        ordenActual.forEach((item, index) => {
            const subtotal = item.precio * item.cantidad;
            totalOrden += subtotal;

            html += `
                <div class="orden-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${item.nombre}</h6>
                            <small class="text-muted">$${item.precio.toLocaleString()}</small>
                            ${item.notas ? "<br><small class=\\"text-info\\"><i class=\\"bi bi-chat-left-text me-1\\"></i>" + item.notas + "</small>" : ""}
                        </div>
                        <button class="btn btn-sm btn-outline-danger rounded-circle ms-2" onclick="eliminarProducto(${index})" title="Eliminar">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-2">
                        <div class="cantidad-controls">
                            <button class="cantidad-btn" onclick="cambiarCantidad(${index}, -1)">
                                <i class="bi bi-dash"></i>
                            </button>
                            <input type="number" class="cantidad-input" value="${item.cantidad}" min="1" onchange="setCantidad(${index}, this.value)">
                            <button class="cantidad-btn" onclick="cambiarCantidad(${index}, 1)">
                                <i class="bi bi-plus"></i>
                            </button>
                        </div>
                        <span class="fw-semibold">Q${subtotal.toFixed(2)}</span>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    // Verificar que totalElement existe antes de actualizar
    if (totalElement) {
        totalElement.textContent = `Q${totalOrden.toFixed(2)}`;
    }
}

function cambiarCantidad(index, cambio) {
    if (ordenActual[index]) {
        ordenActual[index].cantidad += cambio;
        if (ordenActual[index].cantidad <= 0) {
            ordenActual.splice(index, 1);
        }
        actualizarVistaOrden();
    }
}

function setCantidad(index, nuevaCantidad) {
    const cantidad = parseInt(nuevaCantidad);
    if (cantidad > 0 && ordenActual[index]) {
        ordenActual[index].cantidad = cantidad;
        actualizarVistaOrden();
    }
}

function eliminarProducto(index) {
    ordenActual.splice(index, 1);
    actualizarVistaOrden();
}

function limpiarOrden() {
    ordenActual = [];
    actualizarVistaOrden();
}

// Funciones para manejar notas rápidas
function toggleNotaRapida(nota, boton) {
    if (!window.notasRapidasSeleccionadas) {
        window.notasRapidasSeleccionadas = [];
    }

    const index = window.notasRapidasSeleccionadas.indexOf(nota);

    if (index > -1) {
        // Quitar la nota
        window.notasRapidasSeleccionadas.splice(index, 1);
        boton.classList.remove("btn-primary");
        boton.classList.add("btn-outline-secondary");
    } else {
        // Agregar la nota
        window.notasRapidasSeleccionadas.push(nota);
        boton.classList.remove("btn-outline-secondary");
        boton.classList.add("btn-primary");
    }

    actualizarVistaNotasSeleccionadas();
}

function limpiarNotasRapidas() {
    // Limpiar array de notas seleccionadas
    window.notasRapidasSeleccionadas = [];

    // Resetear todos los botones de notas rápidas
    document.querySelectorAll(".nota-rapida-btn").forEach(btn => {
        btn.classList.remove("btn-primary");
        btn.classList.add("btn-outline-secondary");
    });

    // Ocultar contenedor de notas seleccionadas
    document.getElementById("notas-seleccionadas-container").style.display = "none";
}

function actualizarVistaNotasSeleccionadas() {
    const container = document.getElementById("notas-seleccionadas-container");
    const notasDiv = document.getElementById("notas-seleccionadas");

    if (window.notasRapidasSeleccionadas && window.notasRapidasSeleccionadas.length > 0) {
        container.style.display = "block";

        let html = "";
        window.notasRapidasSeleccionadas.forEach(nota => {
            html += `<span class="badge bg-primary me-1 mb-1">${nota}</span>`;
        });

        notasDiv.innerHTML = html;
    } else {
        container.style.display = "none";
    }
}

function enviarOrden() {
    const cuentaId = "' . ($cuenta_id ?? '') . '";
    const hashMesa = "' . ($hash_mesa ?? '') . '";
    const mesaNumero = ' . ($mesa_numero ?? 'null') . ';

    let ordenData, apiUrl;

    if (cuentaId) {
        // Usar API de cuentas múltiples
        ordenData = {
            cuenta_id: cuentaId,
            productos: ordenActual
        };
        apiUrl = "/Restaurante/api/crear_orden_cuenta.php";
    } else {
        // Usar API tradicional
        const nombreCuenta = document.getElementById("nombre-cuenta").value || "Cuenta sin nombre";
        ordenData = {
            hash_mesa: hashMesa,
            mesa_numero: mesaNumero,
            nombre_cuenta: nombreCuenta,
            productos: ordenActual,
            total: totalOrden
        };
        apiUrl = "/Restaurante/api/crear_orden.php";
    }

    fetch(apiUrl, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(ordenData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast("Orden enviada exitosamente", "success");
            limpiarOrden();
            if (document.getElementById("nombre-cuenta")) {
                document.getElementById("nombre-cuenta").value = "";
            }

            // Siempre redirigir a "Mis Órdenes" después de enviar una orden
            setTimeout(() => {
                window.location.href = "/Restaurante/meseros/ver_ordenes.php";
            }, 1500);
        } else {
            showToast("Error al enviar la orden: " + data.message, "error");
        }
    })
    .catch(error => {
        console.error("Error:", error);
        showToast("Error de conexión", "error");
    });
}

// Función para mostrar modal de crear cuenta
function mostrarModalCrearCuenta(numeroMesa) {
    document.getElementById("mesa-seleccionada").textContent = `Mesa #${numeroMesa}`;
    document.getElementById("nombre-cliente-nuevo").value = "";
    document.getElementById("apellido-cliente-nuevo").value = "";

    // Guardar el número de mesa temporalmente
    window.mesaSeleccionada = numeroMesa;

    // Mostrar el modal
    const modal = new bootstrap.Modal(document.getElementById("crearCuentaModal"));
    modal.show();
}

// Función para cargar cuentas de una mesa
function cargarCuentasMesa(hashMesa, container) {
    fetch("/Restaurante/api/obtener_cuentas_mesa.php?hash_mesa=" + hashMesa)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.cuentas && data.cuentas.length > 0) {
            let html = "";

            data.cuentas.forEach(cuenta => {
                const nombreCompleto = cuenta.nombre_cliente + (cuenta.apellido_cliente ? " " + cuenta.apellido_cliente : "");
                html += "<div class=\\"cuenta-item d-flex justify-content-between align-items-center py-1\\">";
                html += "<div class=\\"d-flex align-items-center\\">";
                html += "<i class=\\"bi bi-person-circle text-primary me-2\\"></i>";
                html += "<span class=\\"small\\">" + nombreCompleto + "</span>";
                html += "</div>";
                html += "<div class=\\"btn-group btn-group-sm\\">";
                html += "<a href=\\"?hash=" + hashMesa + "&cuenta_id=" + cuenta.id + "&cliente=" + encodeURIComponent(nombreCompleto) + "\\" class=\\"btn btn-outline-primary btn-sm\\" title=\\"Agregar orden\\">";
                html += "<i class=\\"bi bi-plus-circle\\"></i>";
                html += "</a>";
                html += "<a href=\\"/Restaurante/meseros/cuenta_detalle.php?cuenta_id=" + cuenta.id + "\\" class=\\"btn btn-outline-secondary btn-sm\\" title=\\"Ver detalle\\">";
                html += "<i class=\\"bi bi-eye\\"></i>";
                html += "</a>";
                html += "</div>";
                html += "</div>";
            });

            // Agregar botón para nueva cuenta
            html += "<div class=\\"cuenta-item d-flex justify-content-between align-items-center py-1 border-top mt-2 pt-2\\">";
            html += "<div class=\\"d-flex align-items-center\\">";
            html += "<i class=\\"bi bi-person-plus text-success me-2\\"></i>";
            html += "<span class=\\"small text-success\\">Agregar cuenta nueva</span>";
            html += "</div>";
            html += "<a href=\\"/Restaurante/meseros/mesa_cuentas.php?hash=" + hashMesa + "\\" class=\\"btn btn-success btn-sm\\" title=\\"Nueva cuenta\\">";
            html += "<i class=\\"bi bi-plus-circle\\"></i>";
            html += "</a>";
            html += "</div>";

            container.innerHTML = html;
        } else {
            container.innerHTML = "<div class=\\"text-muted\\"><em>Sin cuentas activas</em></div>";
        }
    })
    .catch(error => {
        console.error("Error cargando cuentas:", error);
        container.innerHTML = "<div class=\\"text-danger small\\"><i class=\\"bi bi-exclamation-triangle me-1\\"></i>Error al cargar</div>";
    });
}

// Función para cargar cuentas de una mesa (vista mobile)
function cargarCuentasMesaMobile(hashMesa, container) {
    fetch("/Restaurante/api/obtener_cuentas_mesa.php?hash_mesa=" + hashMesa)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.cuentas && data.cuentas.length > 0) {
            let html = "";

            data.cuentas.forEach(cuenta => {
                const nombreCompleto = cuenta.nombre_cliente + (cuenta.apellido_cliente ? " " + cuenta.apellido_cliente : "");
                html += "<div class=\\"d-flex justify-content-between align-items-center py-2 border-bottom\\">";
                html += "<div class=\\"d-flex align-items-center flex-grow-1\\">";
                html += "<i class=\\"bi bi-person-circle text-primary me-2 fs-5\\"></i>";
                html += "<span class=\\"fw-medium\\">" + nombreCompleto + "</span>";
                html += "</div>";
                html += "<div class=\\"btn-group btn-group-sm\\">";
                html += "<a href=\\"?hash=" + hashMesa + "&cuenta_id=" + cuenta.id + "&cliente=" + encodeURIComponent(nombreCompleto) + "\\" class=\\"btn btn-primary btn-sm\\" title=\\"Agregar orden\\">";
                html += "<i class=\\"bi bi-plus-circle\\"></i>";
                html += "</a>";
                html += "<a href=\\"/Restaurante/meseros/cuenta_detalle.php?cuenta_id=" + cuenta.id + "\\" class=\\"btn btn-outline-secondary btn-sm\\" title=\\"Ver detalle\\">";
                html += "<i class=\\"bi bi-eye\\"></i>";
                html += "</a>";
                html += "</div>";
                html += "</div>";
            });

            // Agregar botón para nueva cuenta
            html += "<div class=\\"d-flex justify-content-between align-items-center py-2 mt-2\\">";
            html += "<div class=\\"d-flex align-items-center flex-grow-1\\">";
            html += "<i class=\\"bi bi-person-plus text-success me-2 fs-5\\"></i>";
            html += "<span class=\\"text-success fw-medium\\">Agregar cuenta nueva</span>";
            html += "</div>";
            html += "<a href=\\"/Restaurante/meseros/mesa_cuentas.php?hash=" + hashMesa + "\\" class=\\"btn btn-success btn-sm\\" title=\\"Nueva cuenta\\">";
            html += "<i class=\\"bi bi-plus-circle\\"></i>";
            html += "</a>";
            html += "</div>";

            container.innerHTML = html;
        } else {
            container.innerHTML = "<div class=\\"text-muted text-center py-3\\"><em>Sin cuentas activas</em></div>";
        }
    })
    .catch(error => {
        console.error("Error cargando cuentas:", error);
        container.innerHTML = "<div class=\\"text-danger text-center py-3\\"><i class=\\"bi bi-exclamation-triangle me-1\\"></i>Error al cargar</div>";
    });
}

// Función para crear cuenta y abrir mesa automáticamente
function crearCuentaYAbrirMesa(numeroMesa, nombreCliente, apellidoCliente) {
    // Mostrar indicador de carga
    const btn = document.getElementById("confirmar-crear-cuenta");
    const originalContent = btn.innerHTML;
    btn.disabled = true;
    btn.innerHTML = \'<i class="bi bi-hourglass-split me-2"></i>Creando cuenta...\';

    fetch("/Restaurante/api/abrir_mesa_con_cuenta.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            mesa: numeroMesa,
            nombre_cliente: nombreCliente,
            apellido_cliente: apellidoCliente
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Cerrar el modal
            const modal = bootstrap.Modal.getInstance(document.getElementById("crearCuentaModal"));
            modal.hide();

            // Redirigir automáticamente a la mesa recién abierta
            window.location.href = `?hash=${data.hash}`;
        } else {
            // Restaurar botón en caso de error
            btn.disabled = false;
            btn.innerHTML = originalContent;
            showToast("Error al crear la cuenta: " + data.message, "error");
        }
    })
    .catch(error => {
        console.error("Error:", error);
        // Restaurar botón en caso de error
        btn.disabled = false;
        btn.innerHTML = originalContent;
        showToast("Error de conexión al crear la cuenta", "error");
    });
}

// Función local para mostrar notificaciones (compatible con el sistema global)
function showToast(message, type = "info", duration = 4000) {
    // Si existe la función global, usarla
    if (window.RestauranteApp && window.RestauranteApp.showToast) {
        window.RestauranteApp.showToast(message, type, duration);
        return;
    }

    // Crear contenedor si no existe
    let toastContainer = document.getElementById("toast-container");
    if (!toastContainer) {
        toastContainer = document.createElement("div");
        toastContainer.id = "toast-container";
        toastContainer.className = "position-fixed top-0 end-0 p-3";
        toastContainer.style.zIndex = "9999";
        toastContainer.style.maxWidth = "400px";
        document.body.appendChild(toastContainer);
    }

    // Mapear tipos a configuraciones
    const typeConfig = {
        "success": {
            bgClass: "bg-success",
            icon: "bi-check-circle-fill",
            title: "Éxito"
        },
        "error": {
            bgClass: "bg-danger",
            icon: "bi-exclamation-triangle-fill",
            title: "Error"
        },
        "warning": {
            bgClass: "bg-warning",
            icon: "bi-exclamation-triangle-fill",
            title: "Advertencia"
        },
        "info": {
            bgClass: "bg-info",
            icon: "bi-info-circle-fill",
            title: "Información"
        }
    };

    const config = typeConfig[type] || typeConfig["info"];
    const toastId = "toast-" + Date.now() + Math.random().toString(36).substr(2, 9);

    const toastHtml =
        "<div id=\\"" + toastId + "\\" class=\\"toast align-items-center text-white " + config.bgClass + " border-0 shadow-lg mb-2\\" role=\\"alert\\" style=\\"border-radius: 12px; backdrop-filter: blur(10px);\\">" +
            "<div class=\\"d-flex\\">" +
                "<div class=\\"toast-body d-flex align-items-center\\">" +
                    "<i class=\\"bi " + config.icon + " me-2 fs-5\\"></i>" +
                    "<div>" +
                        "<div class=\\"fw-bold small\\">" + config.title + "</div>" +
                        "<div>" + message + "</div>" +
                    "</div>" +
                "</div>" +
                "<button type=\\"button\\" class=\\"btn-close btn-close-white me-2 m-auto\\" data-toast-id=\\"" + toastId + "\\" aria-label=\\"Cerrar\\"></button>" +
            "</div>" +
        "</div>";

    toastContainer.insertAdjacentHTML("beforeend", toastHtml);

    const toastElement = document.getElementById(toastId);

    // Agregar event listener al botón de cerrar
    const closeBtn = toastElement.querySelector(".btn-close");
    if (closeBtn) {
        closeBtn.addEventListener("click", () => {
            hideToast(toastId);
        });
    }

    // Animación de entrada
    toastElement.style.transform = "translateX(100%)";
    toastElement.style.opacity = "0";

    // Usar setTimeout para permitir que el elemento se renderice
    setTimeout(() => {
        toastElement.style.transition = "all 0.3s ease-out";
        toastElement.style.transform = "translateX(0)";
        toastElement.style.opacity = "1";
    }, 10);

    // Auto-ocultar después del tiempo especificado
    setTimeout(() => {
        hideToast(toastId);
    }, duration);

    return toastElement;
}

// Función para ocultar notificación con animación
function hideToast(toastId) {
    const toastElement = document.getElementById(toastId);
    if (!toastElement || !toastElement.parentNode) return;

    toastElement.style.transition = "all 0.3s ease-in";
    toastElement.style.transform = "translateX(100%)";
    toastElement.style.opacity = "0";

    setTimeout(() => {
        if (toastElement.parentNode) {
            toastElement.remove();
        }
    }, 300);
}
</script>
';

// Incluir el layout
include '../includes/layout.php';
?>
