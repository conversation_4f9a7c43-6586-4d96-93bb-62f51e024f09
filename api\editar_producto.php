<?php
header('Content-Type: application/json');
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Verificar que sea administrador
require_once '../config/db.php';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden editar productos']);
    exit();
}

// Obtener datos del POST
$producto_id = $_POST['producto_id'] ?? '';
$nombre = trim($_POST['nombre'] ?? '');
$descripcion = trim($_POST['descripcion'] ?? '');
$precio_costo = $_POST['precio_costo'] ?? '';
$precio_venta = $_POST['precio_venta'] ?? '';
$categoria_id = $_POST['categoria_id'] ?? null;
$area = $_POST['area'] ?? '';
$disponible = isset($_POST['disponible']) ? 1 : 0;
$imagen_actual = $_POST['imagen_actual'] ?? '';

// Validaciones
if (empty($producto_id) || empty($nombre) || empty($precio_costo) || empty($precio_venta) || empty($area)) {
    echo json_encode(['success' => false, 'message' => 'Datos incompletos']);
    exit();
}

if (!is_numeric($precio_costo) || $precio_costo < 0) {
    echo json_encode(['success' => false, 'message' => 'El precio de costo debe ser un número válido mayor o igual a 0']);
    exit();
}

if (!is_numeric($precio_venta) || $precio_venta <= 0) {
    echo json_encode(['success' => false, 'message' => 'El precio de venta debe ser un número válido mayor a 0']);
    exit();
}

$areas_validas = ['cocina', 'bebidas'];
if (!in_array($area, $areas_validas)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Área inválida']);
    exit();
}

// Validar categoría si se proporciona
if ($categoria_id && !empty($categoria_id)) {
    $stmt = $pdo->prepare("SELECT id FROM categorias WHERE id = ? AND activo = 1");
    $stmt->execute([$categoria_id]);
    if (!$stmt->fetch()) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Categoría inválida']);
        exit();
    }
} else {
    $categoria_id = null;
}

try {
    // Verificar que el producto existe
    $stmt = $pdo->prepare("SELECT * FROM productos WHERE id = ?");
    $stmt->execute([$producto_id]);
    $producto_actual = $stmt->fetch();

    if (!$producto_actual) {
        echo json_encode(['success' => false, 'message' => 'Producto no encontrado']);
        exit();
    }

    // Verificar que el nombre no exista en otro producto
    $stmt = $pdo->prepare("SELECT id FROM productos WHERE nombre = ? AND id != ?");
    $stmt->execute([$nombre, $producto_id]);
    if ($stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'Ya existe otro producto con ese nombre']);
        exit();
    }

    // Manejar imagen
    $nueva_imagen = $producto_actual['imagen']; // Mantener imagen actual por defecto
    $upload_dir = '../assets/img/productos/';

    // Si se marcó para eliminar imagen
    if ($imagen_actual === 'ELIMINAR') {
        if ($producto_actual['imagen'] && file_exists($upload_dir . $producto_actual['imagen'])) {
            unlink($upload_dir . $producto_actual['imagen']);
        }
        $nueva_imagen = null;
    }

    // Si se subió nueva imagen
    if (isset($_FILES['imagen']) && $_FILES['imagen']['error'] === UPLOAD_ERR_OK) {
        $archivo = $_FILES['imagen'];

        // Validar tipo de archivo
        $tipos_permitidos = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($archivo['type'], $tipos_permitidos)) {
            echo json_encode(['success' => false, 'message' => 'Formato de imagen no válido. Use JPG, PNG, GIF o WEBP']);
            exit();
        }

        // Validar tamaño (5MB máximo)
        if ($archivo['size'] > 5 * 1024 * 1024) {
            echo json_encode(['success' => false, 'message' => 'La imagen es demasiado grande. El tamaño máximo es 5MB']);
            exit();
        }

        // Crear directorio si no existe
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        // Eliminar imagen anterior si existe
        if ($producto_actual['imagen'] && file_exists($upload_dir . $producto_actual['imagen'])) {
            unlink($upload_dir . $producto_actual['imagen']);
        }

        // Generar nombre único para la nueva imagen
        $extension = pathinfo($archivo['name'], PATHINFO_EXTENSION);
        $nueva_imagen = 'producto_' . time() . '_' . uniqid() . '.' . $extension;
        $ruta_destino = $upload_dir . $nueva_imagen;

        // Mover archivo
        if (!move_uploaded_file($archivo['tmp_name'], $ruta_destino)) {
            echo json_encode(['success' => false, 'message' => 'Error al subir la nueva imagen']);
            exit();
        }
    }

    // Actualizar el producto
    $stmt = $pdo->prepare("
        UPDATE productos
        SET nombre = ?, descripcion = ?, precio_costo = ?, precio_venta = ?, categoria_id = ?, area = ?, imagen = ?, disponible = ?, fecha_actualizacion = NOW()
        WHERE id = ?
    ");

    $stmt->execute([
        $nombre,
        $descripcion,
        $precio_costo,
        $precio_venta,
        $categoria_id,
        $area,
        $nueva_imagen,
        $disponible,
        $producto_id
    ]);

    echo json_encode([
        'success' => true,
        'message' => 'Producto actualizado exitosamente',
        'producto_id' => $producto_id,
        'nombre' => $nombre,
        'precio_costo' => $precio_costo,
        'precio_venta' => $precio_venta,
        'area' => $area,
        'imagen' => $nueva_imagen
    ]);

} catch (PDOException $e) {
    error_log("Error en editar_producto.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
