# Sistema de Restaurante - Archivos a ignorar

# Archivos temporales y de sesión
tmp/sessions/*
!tmp/sessions/.gitkeep

# Logs del sistema
logs/*.log
logs/*.txt
!logs/.gitkeep

# Archivos de configuración local
config/db_local.php
config/local_config.php

# Archivos de prueba y debug
test_*.php
debug_*.php
*_test.php
*_debug.php

# Archivos de documentación de desarrollo
CAMBIOS_*.md
CORRECCIONES_*.md
PROBLEMAS_*.md
FUNCIONALIDADES_*.md
INVENTARIO_*.md
SISTEMA_*.md
Detalles_Proyecto.md

# Archivos de backup
*.bak
*.backup
*.old
*_backup.*

# Archivos del sistema operativo
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Archivos de IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Archivos de Node.js (si se usan en el futuro)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Archivos de Composer (si se usan en el futuro)
vendor/
composer.lock

# Archivos de uploads temporales
uploads/temp/*
!uploads/temp/.gitkeep

# Archivos de cache
cache/*
!cache/.gitkeep

# Variables de entorno
.env
.env.local
.env.production

# Archivos de SQL de desarrollo
*_dev.sql
*_local.sql
dump_*.sql

# Archivos de imágenes temporales
assets/img/temp/*
!assets/img/temp/.gitkeep
