<?php
// Configurar variables para el layout
$page_title = 'Gestión de Productos';
$breadcrumbs = [
    ['title' => 'Inicio', 'url' => url('')],
    ['title' => 'Administración', 'url' => url('admin/')],
    ['title' => 'Productos']
];

// Iniciar el buffer de contenido
ob_start();

// Conectar a la base de datos
require_once '../config/session_config.php';
session_start();
require_once '../config/db.php';

// Verificar que sea administrador
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    echo '<div class="alert alert-danger">Acceso denegado. Solo administradores pueden acceder.</div>';
    $content = ob_get_clean();
    include '../includes/layout.php';
    exit();
}

// Obtener productos y categorías
try {
    // Obtener productos con información de categoría
    $stmt = $pdo->query("
        SELECT p.*, c.nombre as categoria_nombre 
        FROM productos p 
        LEFT JOIN categorias c ON p.categoria_id = c.id 
        ORDER BY p.area, p.nombre
    ");
    $productos = $stmt->fetchAll();
    
    // Obtener categorías activas
    $stmt = $pdo->query("SELECT * FROM categorias WHERE activo = 1 ORDER BY nombre");
    $categorias = $stmt->fetchAll();
    
    // Estadísticas
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM productos");
    $total_productos = $stmt->fetch()['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM productos WHERE disponible = 1");
    $productos_activos = $stmt->fetch()['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM productos WHERE area = 'cocina'");
    $productos_cocina = $stmt->fetch()['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM productos WHERE area = 'bebidas'");
    $productos_bebidas = $stmt->fetch()['total'];
    
} catch (PDOException $e) {
    $productos = [];
    $categorias = [];
    $total_productos = 0;
    $productos_activos = 0;
    $productos_cocina = 0;
    $productos_bebidas = 0;
}
?>

<!-- Estadísticas de Productos -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-grid-3x3-gap text-primary fs-4"></i>
                </div>
                <h3 class="text-primary mb-1"><?= $total_productos ?></h3>
                <p class="text-muted mb-0 small">Total Productos</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-check-circle text-success fs-4"></i>
                </div>
                <h3 class="text-success mb-1"><?= $productos_activos ?></h3>
                <p class="text-muted mb-0 small">Disponibles</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="bg-danger bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-egg-fried text-danger fs-4"></i>
                </div>
                <h3 class="text-danger mb-1"><?= $productos_cocina ?></h3>
                <p class="text-muted mb-0 small">Cocina</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-cup-straw text-info fs-4"></i>
                </div>
                <h3 class="text-info mb-1"><?= $productos_bebidas ?></h3>
                <p class="text-muted mb-0 small">Bebidas</p>
            </div>
        </div>
    </div>
</div>

<!-- Gestión de Productos -->
<div class="row g-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-grid-3x3-gap me-2"></i>
                        Lista de Productos
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary btn-sm rounded-pill" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise me-1"></i>
                            Actualizar
                        </button>
                        <button class="btn btn-primary rounded-pill" data-bs-toggle="modal" data-bs-target="#modalProducto">
                            <i class="bi bi-plus-circle me-2"></i>
                            Nuevo Producto
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (empty($productos)): ?>
                    <div class="text-center py-5">
                        <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                            <i class="bi bi-grid-3x3-gap text-muted fs-2"></i>
                        </div>
                        <h6 class="text-muted">No hay productos registrados</h6>
                        <p class="text-muted mb-4">Agrega el primer producto al menú</p>
                        <button class="btn btn-primary rounded-pill" data-bs-toggle="modal" data-bs-target="#modalProducto">
                            <i class="bi bi-plus-circle me-2"></i>
                            Crear Primer Producto
                        </button>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-semibold text-muted small">Producto</th>
                                    <th class="border-0 fw-semibold text-muted small">Categoría</th>
                                    <th class="border-0 fw-semibold text-muted small">Área</th>
                                    <th class="border-0 fw-semibold text-muted small">Precio</th>
                                    <th class="border-0 fw-semibold text-muted small">Estado</th>
                                    <th class="border-0 fw-semibold text-muted small">Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($productos as $producto): ?>
                                    <tr>
                                        <td class="border-0 py-3">
                                            <div class="d-flex align-items-center">
                                                <?php if (!empty($producto['imagen']) && file_exists('../assets/img/productos/' . $producto['imagen'])): ?>
                                                    <img src="/Restaurante/assets/img/productos/<?= htmlspecialchars($producto['imagen']) ?>"
                                                         alt="<?= htmlspecialchars($producto['nombre']) ?>"
                                                         class="rounded me-3"
                                                         style="width: 50px; height: 50px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-<?= $producto['area'] === 'cocina' ? 'danger' : 'info' ?> bg-opacity-10 rounded d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                                        <i class="bi <?= $producto['area'] === 'cocina' ? 'bi-egg-fried' : 'bi-cup-straw' ?> text-<?= $producto['area'] === 'cocina' ? 'danger' : 'info' ?>"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <h6 class="mb-1"><?= htmlspecialchars($producto['nombre']) ?></h6>
                                                    <small class="text-muted"><?= htmlspecialchars($producto['descripcion']) ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="border-0 py-3">
                                            <span class="badge bg-secondary bg-opacity-10 text-secondary rounded-pill">
                                                <?= htmlspecialchars($producto['categoria_nombre'] ?? 'Sin categoría') ?>
                                            </span>
                                        </td>
                                        <td class="border-0 py-3">
                                            <span class="badge bg-<?= $producto['area'] === 'cocina' ? 'danger' : 'info' ?> bg-opacity-10 text-<?= $producto['area'] === 'cocina' ? 'danger' : 'info' ?> rounded-pill">
                                                <?= ucfirst($producto['area']) ?>
                                            </span>
                                        </td>
                                        <td class="border-0 py-3">
                                            <div>
                                                <h6 class="mb-1 text-success">Q <?= number_format($producto['precio_venta'] ?? $producto['precio'] ?? 0, 2) ?></h6>
                                                <small class="text-muted">
                                                    Costo: Q <?= number_format($producto['precio_costo'] ?? 0, 2) ?>
                                                </small>
                                                <?php
                                                $precio_venta = $producto['precio_venta'] ?? $producto['precio'] ?? 0;
                                                $precio_costo = $producto['precio_costo'] ?? 0;
                                                $ganancia = $precio_venta - $precio_costo;
                                                $margen = $precio_venta > 0 ? ($ganancia / $precio_venta) * 100 : 0;
                                                ?>
                                                <br><small class="<?= $ganancia > 0 ? 'text-success' : ($ganancia < 0 ? 'text-danger' : 'text-dark') ?>">
                                                    Ganancia: Q <?= number_format($ganancia, 2) ?> (<?= number_format($margen, 1) ?>%)
                                                </small>
                                            </div>
                                        </td>
                                        <td class="border-0 py-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input toggle-disponible" type="checkbox" 
                                                       data-producto-id="<?= $producto['id'] ?>"
                                                       <?= $producto['disponible'] ? 'checked' : '' ?>>
                                                <label class="form-check-label small">
                                                    <?= $producto['disponible'] ? 'Disponible' : 'No disponible' ?>
                                                </label>
                                            </div>
                                        </td>
                                        <td class="border-0 py-3">
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button class="btn btn-outline-primary rounded-pill me-1 editar-producto-btn" 
                                                        data-producto='<?= json_encode($producto) ?>'>
                                                    <i class="bi bi-pencil me-1"></i>
                                                    Editar
                                                </button>
                                                <button class="btn btn-outline-danger rounded-pill eliminar-producto-btn" 
                                                        data-producto-id="<?= $producto['id'] ?>"
                                                        data-producto-nombre="<?= htmlspecialchars($producto['nombre']) ?>">
                                                    <i class="bi bi-trash me-1"></i>
                                                    Eliminar
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Crear/Editar Producto -->
<div class="modal fade" id="modalProducto" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalProductoTitle">Nuevo Producto</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="formProducto" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="producto_id" name="producto_id">
                    <input type="hidden" id="imagen_actual" name="imagen_actual">

                    <div class="row g-3">
                        <!-- Imagen del producto -->
                        <div class="col-12">
                            <label class="form-label">Imagen del Producto</label>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <div class="border rounded p-3 text-center bg-light">
                                        <div id="preview-imagen-container">
                                            <div id="preview-placeholder" class="bg-secondary bg-opacity-10 rounded d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 120px; height: 120px;">
                                                <i class="bi bi-image text-muted fs-1"></i>
                                            </div>
                                            <img id="preview-imagen" src="" alt="Vista previa" class="rounded mx-auto d-none" style="width: 120px; height: 120px; object-fit: cover;">
                                        </div>
                                        <small class="text-muted">Vista previa</small>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <input type="file" class="form-control" id="imagen" name="imagen" accept="image/*">
                                        <div class="form-text">
                                            <small>Formatos permitidos: JPG, PNG, GIF, WEBP. Tamaño máximo: 5MB</small>
                                        </div>
                                    </div>
                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="btn-quitar-imagen" style="display: none;">
                                            <i class="bi bi-trash me-1"></i>
                                            Quitar imagen
                                        </button>
                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="document.getElementById('imagen').click()">
                                            <i class="bi bi-upload me-1"></i>
                                            Seleccionar imagen
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Información básica -->
                        <div class="col-12">
                            <label for="nombre" class="form-label">Nombre del Producto *</label>
                            <input type="text" class="form-control" id="nombre" name="nombre" required maxlength="100">
                        </div>

                        <!-- Precios -->
                        <div class="col-md-6">
                            <label for="precio_costo" class="form-label">Precio de Costo *</label>
                            <div class="input-group">
                                <span class="input-group-text">Q</span>
                                <input type="number" class="form-control" id="precio_costo" name="precio_costo" min="0" step="0.01" required>
                            </div>
                            <div class="form-text">
                                <small>Costo de producción o compra del producto</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="precio_venta" class="form-label">Precio de Venta *</label>
                            <div class="input-group">
                                <span class="input-group-text">Q</span>
                                <input type="number" class="form-control" id="precio_venta" name="precio_venta" min="0" step="0.01" required>
                            </div>
                            <div class="form-text">
                                <small>Precio que paga el cliente</small>
                            </div>
                        </div>

                        <!-- Indicador de ganancia -->
                        <div class="col-12">
                            <div class="alert alert-info py-2 px-3" id="ganancia-info" style="display: none;">
                                <div class="row text-center">
                                    <div class="col-md-4">
                                        <small class="text-muted">Ganancia por unidad</small>
                                        <div class="fw-bold" id="ganancia-unitaria">Q 0.00</div>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">Margen de ganancia</small>
                                        <div class="fw-bold" id="margen-ganancia">0%</div>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">Estado</small>
                                        <div class="fw-bold" id="estado-ganancia">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <label for="descripcion" class="form-label">Descripción</label>
                            <textarea class="form-control" id="descripcion" name="descripcion" rows="3" maxlength="500"></textarea>
                            <div class="form-text">
                                <small><span id="descripcion-contador">0</span>/500 caracteres</small>
                            </div>
                        </div>

                        <!-- Categoría y área -->
                        <div class="col-md-6">
                            <label for="categoria_id" class="form-label">Categoría</label>
                            <select class="form-select" id="categoria_id" name="categoria_id">
                                <option value="">Sin categoría</option>
                                <?php foreach ($categorias as $categoria): ?>
                                    <option value="<?= $categoria['id'] ?>" data-icono="<?= $categoria['icono'] ?>" data-color="<?= $categoria['color'] ?>">
                                        <?= htmlspecialchars($categoria['nombre']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">
                                <small>
                                    <a href="/Restaurante/admin/categorias.php" target="_blank" class="text-decoration-none">
                                        <i class="bi bi-plus-circle me-1"></i>Gestionar categorías
                                    </a>
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="area" class="form-label">Área *</label>
                            <select class="form-select" id="area" name="area" required>
                                <option value="">Seleccionar área</option>
                                <option value="cocina">🍳 Cocina</option>
                                <option value="bebidas">🥤 Bebidas</option>
                            </select>
                        </div>

                        <!-- Estado -->
                        <div class="col-12">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="disponible" name="disponible" checked>
                                <label class="form-check-label" for="disponible">
                                    <strong>Producto disponible</strong>
                                    <br><small class="text-muted">Los clientes podrán ver y ordenar este producto</small>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i>
                        <span id="btnSubmitText">Crear Producto</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// JavaScript adicional
$extra_js = '
<script>
document.addEventListener("DOMContentLoaded", function() {
    // Editar producto
    document.querySelectorAll(".editar-producto-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const producto = JSON.parse(this.dataset.producto);
            editarProducto(producto);
        });
    });

    // Eliminar producto
    document.querySelectorAll(".eliminar-producto-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const productoId = this.dataset.productoId;
            const productoNombre = this.dataset.productoNombre;
            if (confirm(`¿Estás seguro de eliminar el producto "${productoNombre}"?`)) {
                eliminarProducto(productoId);
            }
        });
    });

    // Toggle disponibilidad
    document.querySelectorAll(".toggle-disponible").forEach(toggle => {
        toggle.addEventListener("change", function() {
            const productoId = this.dataset.productoId;
            const disponible = this.checked;
            toggleDisponibilidad(productoId, disponible);
        });
    });

    // Formulario de producto
    document.getElementById("formProducto").addEventListener("submit", function(e) {
        e.preventDefault();
        guardarProducto();
    });

    // Limpiar modal al cerrarse
    document.getElementById("modalProducto").addEventListener("hidden.bs.modal", function() {
        limpiarFormulario();
    });

    // Manejo de imagen
    const inputImagen = document.getElementById("imagen");
    const previewImagen = document.getElementById("preview-imagen");
    const previewPlaceholder = document.getElementById("preview-placeholder");
    const btnQuitarImagen = document.getElementById("btn-quitar-imagen");
    const descripcionTextarea = document.getElementById("descripcion");
    const descripcionContador = document.getElementById("descripcion-contador");

    // Vista previa de imagen
    inputImagen.addEventListener("change", function(e) {
        const file = e.target.files[0];
        if (file) {
            // Validar tamaño (5MB máximo)
            if (file.size > 5 * 1024 * 1024) {
                showToast("La imagen es demasiado grande. El tamaño máximo es 5MB.", "error");
                this.value = "";
                return;
            }

            // Validar tipo
            const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"];
            if (!allowedTypes.includes(file.type)) {
                showToast("Formato de imagen no válido. Use JPG, PNG, GIF o WEBP.", "error");
                this.value = "";
                return;
            }

            // Mostrar vista previa
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImagen.src = e.target.result;
                previewImagen.classList.remove("d-none");
                previewPlaceholder.classList.add("d-none");
                btnQuitarImagen.style.display = "inline-block";
            };
            reader.readAsDataURL(file);
        }
    });

    // Quitar imagen
    btnQuitarImagen.addEventListener("click", function() {
        inputImagen.value = "";
        previewImagen.src = "";
        previewImagen.classList.add("d-none");
        previewPlaceholder.classList.remove("d-none");
        this.style.display = "none";

        // Si estamos editando, marcar para eliminar imagen
        const imagenActual = document.getElementById("imagen_actual");
        if (imagenActual.value) {
            imagenActual.value = "ELIMINAR";
        }
    });

    // Contador de caracteres para descripción
    descripcionTextarea.addEventListener("input", function() {
        const length = this.value.length;
        descripcionContador.textContent = length;

        if (length > 450) {
            descripcionContador.style.color = "#dc3545";
        } else if (length > 400) {
            descripcionContador.style.color = "#fd7e14";
        } else {
            descripcionContador.style.color = "#6c757d";
        }
    });

    // Cálculo de ganancia en tiempo real
    const precioCostoInput = document.getElementById("precio_costo");
    const precioVentaInput = document.getElementById("precio_venta");
    const gananciaInfo = document.getElementById("ganancia-info");
    const gananciaUnitaria = document.getElementById("ganancia-unitaria");
    const margenGanancia = document.getElementById("margen-ganancia");
    const estadoGanancia = document.getElementById("estado-ganancia");

    function calcularGanancia() {
        const costo = parseFloat(precioCostoInput.value) || 0;
        const venta = parseFloat(precioVentaInput.value) || 0;

        if (costo > 0 || venta > 0) {
            const ganancia = venta - costo;
            const margen = venta > 0 ? (ganancia / venta) * 100 : 0;

            gananciaUnitaria.textContent = `Q ${ganancia.toFixed(2)}`;
            margenGanancia.textContent = `${margen.toFixed(1)}%`;

            // Determinar estado y colores
            if (ganancia > 0) {
                estadoGanancia.textContent = "Rentable";
                estadoGanancia.className = "fw-bold text-success";
                gananciaUnitaria.className = "fw-bold text-success";
                margenGanancia.className = "fw-bold text-success";
            } else if (ganancia < 0) {
                estadoGanancia.textContent = "Pérdida";
                estadoGanancia.className = "fw-bold text-danger";
                gananciaUnitaria.className = "fw-bold text-danger";
                margenGanancia.className = "fw-bold text-danger";
            } else {
                estadoGanancia.textContent = "Sin ganancia";
                estadoGanancia.className = "fw-bold text-dark";
                gananciaUnitaria.className = "fw-bold text-dark";
                margenGanancia.className = "fw-bold text-dark";
            }

            gananciaInfo.style.display = "block";
        } else {
            gananciaInfo.style.display = "none";
        }
    }

    precioCostoInput.addEventListener("input", calcularGanancia);
    precioVentaInput.addEventListener("input", calcularGanancia);
});

function editarProducto(producto) {
    document.getElementById("modalProductoTitle").textContent = "Editar Producto";
    document.getElementById("btnSubmitText").textContent = "Actualizar Producto";

    document.getElementById("producto_id").value = producto.id;
    document.getElementById("nombre").value = producto.nombre;
    document.getElementById("descripcion").value = producto.descripcion || "";
    document.getElementById("precio_costo").value = producto.precio_costo || 0;
    document.getElementById("precio_venta").value = producto.precio_venta || producto.precio || 0;
    document.getElementById("categoria_id").value = producto.categoria_id || "";
    document.getElementById("area").value = producto.area;
    document.getElementById("disponible").checked = producto.disponible == 1;

    // Manejar imagen existente
    const imagenActual = document.getElementById("imagen_actual");
    const previewImagen = document.getElementById("preview-imagen");
    const previewPlaceholder = document.getElementById("preview-placeholder");
    const btnQuitarImagen = document.getElementById("btn-quitar-imagen");
    const inputImagen = document.getElementById("imagen");

    imagenActual.value = producto.imagen || "";
    inputImagen.value = "";

    if (producto.imagen) {
        previewImagen.src = "/Restaurante/assets/img/productos/" + producto.imagen;
        previewImagen.classList.remove("d-none");
        previewPlaceholder.classList.add("d-none");
        btnQuitarImagen.style.display = "inline-block";
    } else {
        previewImagen.classList.add("d-none");
        previewPlaceholder.classList.remove("d-none");
        btnQuitarImagen.style.display = "none";
    }

    // Actualizar contador de descripción
    const descripcionContador = document.getElementById("descripcion-contador");
    descripcionContador.textContent = (producto.descripcion || "").length;

    new bootstrap.Modal(document.getElementById("modalProducto")).show();
}

function limpiarFormulario() {
    document.getElementById("modalProductoTitle").textContent = "Nuevo Producto";
    document.getElementById("btnSubmitText").textContent = "Crear Producto";
    document.getElementById("formProducto").reset();
    document.getElementById("producto_id").value = "";
    document.getElementById("imagen_actual").value = "";
    document.getElementById("disponible").checked = true;

    // Limpiar vista previa de imagen
    const previewImagen = document.getElementById("preview-imagen");
    const previewPlaceholder = document.getElementById("preview-placeholder");
    const btnQuitarImagen = document.getElementById("btn-quitar-imagen");
    const descripcionContador = document.getElementById("descripcion-contador");

    previewImagen.src = "";
    previewImagen.classList.add("d-none");
    previewPlaceholder.classList.remove("d-none");
    btnQuitarImagen.style.display = "none";
    descripcionContador.textContent = "0";
    descripcionContador.style.color = "#6c757d";
}

function guardarProducto() {
    const form = document.getElementById("formProducto");
    const formData = new FormData(form);

    // Validaciones del lado cliente
    const nombre = document.getElementById("nombre").value.trim();
    const precioCosto = parseFloat(document.getElementById("precio_costo").value) || 0;
    const precioVenta = parseFloat(document.getElementById("precio_venta").value) || 0;
    const area = document.getElementById("area").value;

    if (!nombre) {
        showToast("El nombre es requerido", "error");
        return;
    }

    if (precioCosto < 0) {
        showToast("El precio de costo debe ser mayor o igual a 0", "error");
        return;
    }

    if (precioVenta <= 0) {
        showToast("El precio de venta debe ser mayor a 0", "error");
        return;
    }

    if (precioVenta < precioCosto) {
        if (!confirm("El precio de venta es menor al precio de costo. Esto generará pérdidas. ¿Desea continuar?")) {
            return;
        }
    }

    if (!area) {
        showToast("Debe seleccionar un área", "error");
        return;
    }

    // Validar imagen si se seleccionó
    const inputImagen = document.getElementById("imagen");
    if (inputImagen.files.length > 0) {
        const archivo = inputImagen.files[0];

        // Validar tamaño
        if (archivo.size > 5 * 1024 * 1024) {
            showToast("La imagen es demasiado grande. El tamaño máximo es 5MB", "error");
            return;
        }

        // Validar tipo
        const tiposPermitidos = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"];
        if (!tiposPermitidos.includes(archivo.type)) {
            showToast("Formato de imagen no válido. Use JPG, PNG, GIF o WEBP", "error");
            return;
        }
    }

    const productoId = document.getElementById("producto_id").value;
    const url = productoId ? "/Restaurante/api/editar_producto.php" : "/Restaurante/api/crear_producto.php";

    // Mostrar indicador de carga
    const btnSubmit = document.querySelector("#formProducto button[type=submit]");
    const textoOriginal = btnSubmit.innerHTML;
    btnSubmit.innerHTML = "<i class=\"bi bi-hourglass-split me-2\"></i>Guardando...";
    btnSubmit.disabled = true;

    fetch(url, {
        method: "POST",
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, "success");
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showToast(data.message, "error");
            // Restaurar botón
            btnSubmit.innerHTML = textoOriginal;
            btnSubmit.disabled = false;
        }
    })
    .catch(error => {
        console.error("Error:", error);
        showToast("Error de conexión", "error");
        // Restaurar botón
        btnSubmit.innerHTML = textoOriginal;
        btnSubmit.disabled = false;
    });
}

function showToast(message, type = "info") {
    const toast = document.createElement("div");
    toast.className = `alert alert-${type === "success" ? "success" : "danger"} position-fixed`;
    toast.style.cssText = "top: 20px; right: 20px; z-index: 9999; min-width: 300px;";
    toast.innerHTML = `
        <i class="bi bi-${type === "success" ? "check-circle" : "exclamation-triangle"} me-2"></i>
        ${message}
    `;

    document.body.appendChild(toast);
    setTimeout(() => toast.remove(), 3000);
}

function eliminarProducto(productoId) {
    fetch("/Restaurante/api/eliminar_producto.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            producto_id: productoId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, "success");
            setTimeout(() => location.reload(), 2000);
        } else {
            showToast("Error: " + data.message, "error");
        }
    })
    .catch(error => {
        console.error("Error:", error);
        showToast("Error de conexión", "error");
    });
}

function toggleDisponibilidad(productoId, disponible) {
    fetch("/Restaurante/api/toggle_producto.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            producto_id: productoId,
            disponible: disponible
        })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            showToast("Error: " + data.message, "error");
            location.reload();
        }
    })
    .catch(error => {
        console.error("Error:", error);
        showToast("Error de conexión", "error");
        location.reload();
    });
}
</script>
';

// Incluir el layout
include '../includes/layout.php';
?>
