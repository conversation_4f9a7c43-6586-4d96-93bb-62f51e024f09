<?php
// Incluir configuración de sesiones
require_once '../config/session_config.php';
session_start();

// Si ya está logueado, redirigir al dashboard
if (isset($_SESSION['usuario_id'])) {
    header('Location: /Restaurante/');
    exit();
}

$error = '';

if ($_POST) {
    require_once '../config/db.php';

    $correo = $_POST['correo'] ?? '';
    $password = $_POST['password'] ?? '';

    if (empty($correo) || empty($password)) {
        $error = 'Por favor, complete todos los campos.';
    } else {
        try {
            $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE correo = ? AND activo = 1");
            $stmt->execute([$correo]);
            $usuario = $stmt->fetch();

            if ($usuario && md5($password) === $usuario['password']) {
                $_SESSION['usuario_id'] = $usuario['id'];
                $_SESSION['usuario_nombre'] = $usuario['nombre'];
                $_SESSION['usuario_rol'] = $usuario['rol'];

                header('Location: /Restaurante/');
                exit();
            } else {
                $error = 'Credenciales incorrectas o usuario inactivo.';
            }
        } catch (PDOException $e) {
            $error = 'Error de conexión a la base de datos.';
        }
    }
}
?>
<!doctype html>
<html lang="es">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Login - Sistema Restaurante</title>

    <!-- Fonts -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fontsource/source-sans-3@5.0.12/index.css" crossorigin="anonymous" />

    <!-- Third Party Plugins -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" crossorigin="anonymous" />

    <!-- AdminLTE CSS -->
    <link rel="stylesheet" href="/Restaurante/assets/adminlte/css/adminlte.min.css" />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="/Restaurante/assets/css/custom.css" />

    <style>
        .login-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-box {
            width: 400px;
            margin: 0 auto;
        }

        .login-card-body {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-logo img {
            width: 80px;
            height: 80px;
            margin-bottom: 15px;
        }

        .login-logo h1 {
            color: var(--restaurant-secondary);
            font-weight: bold;
            margin: 0;
        }

        .login-logo p {
            color: #666;
            margin: 5px 0 0 0;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s;
        }

        .form-control:focus {
            border-color: var(--restaurant-primary);
            box-shadow: 0 0 0 0.2rem rgba(212, 165, 116, 0.25);
        }

        .input-group-text {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: transparent;
            color: #666;
        }

        .btn-login {
            background: linear-gradient(135deg, var(--restaurant-primary), var(--restaurant-secondary));
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>

<body class="login-page">
    <div class="login-box">
        <div class="login-card-body">
            <div class="login-logo">
                <img src="/Restaurante/assets/adminlte/assets/img/AdminLTELogo.png" alt="Logo">
                <h1>Restaurante</h1>
                <p>Sistema de Gestión</p>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <form method="POST" action="">
                <div class="mb-3">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-envelope"></i>
                        </span>
                        <input type="email" class="form-control" name="correo" placeholder="Correo electrónico"
                               value="<?= htmlspecialchars($_POST['correo'] ?? '') ?>" required>
                    </div>
                </div>

                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-lock"></i>
                        </span>
                        <input type="password" class="form-control" name="password" placeholder="Contraseña" required>
                    </div>
                </div>

                <button type="submit" class="btn btn-login">
                    <i class="bi bi-box-arrow-in-right me-2"></i>
                    Iniciar Sesión
                </button>
            </form>

            <div class="text-center mt-4">
                <small class="text-muted">
                    Sistema de Gestión de Restaurante v1.0<br>
                    © <?= date('Y') ?> Todos los derechos reservados
                </small>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.min.js" crossorigin="anonymous"></script>
    <script src="/Restaurante/assets/adminlte/js/adminlte.min.js"></script>
</body>
</html>