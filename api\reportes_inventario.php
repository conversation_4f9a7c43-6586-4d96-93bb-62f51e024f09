<?php
// Determinar el tipo de respuesta
$view = $_GET['view'] ?? 'json';
if ($view === 'html') {
    header('Content-Type: text/html; charset=utf-8');
} else {
    header('Content-Type: application/json');
}

// Iniciar sesión y conectar a la base de datos
session_start();
require_once '../config/db.php';

// Verificar que el usuario esté logueado y sea administrador
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden acceder a reportes']);
    exit();
}

// Obtener parámetros
$fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-m-d', strtotime('-30 days'));
$fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-d');
$categoria = $_GET['categoria'] ?? '';
$tipo = $_GET['tipo'] ?? 'resumen';

// Validar fechas
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_inicio) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_fin)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Formato de fecha inválido']);
    exit();
}

try {
    // Resumen general del inventario
    $stmt = $pdo->query("
        SELECT
            COUNT(DISTINCT i.producto_id) as total_productos,
            SUM(i.stock_actual) as stock_total,
            SUM(i.stock_actual * i.costo_promedio) as valor_total_inventario,
            COUNT(CASE WHEN i.stock_actual <= i.stock_minimo THEN 1 END) as productos_stock_bajo,
            AVG(i.costo_promedio) as costo_promedio_general
        FROM inventario i
        INNER JOIN productos p ON i.producto_id = p.id
        WHERE p.disponible = 1
    ");
    $resumen_general = $stmt->fetch(PDO::FETCH_ASSOC);

    // Productos con stock bajo
    $stmt = $pdo->query("
        SELECT
            p.nombre,
            c.nombre as categoria,
            i.stock_actual,
            i.stock_minimo,
            i.unidad_medida,
            i.costo_promedio,
            (i.stock_actual * i.costo_promedio) as valor_stock
        FROM inventario i
        INNER JOIN productos p ON i.producto_id = p.id
        LEFT JOIN categorias c ON p.categoria_id = c.id
        WHERE i.stock_actual <= i.stock_minimo AND p.disponible = 1
        ORDER BY (i.stock_actual - i.stock_minimo) ASC
    ");
    $productos_stock_bajo = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Movimientos en el período especificado
    $where_conditions = ["DATE(mi.fecha_movimiento) >= ? AND DATE(mi.fecha_movimiento) <= ?"];
    $params = [$fecha_inicio, $fecha_fin];

    if (!empty($categoria)) {
        $where_conditions[] = "c.nombre = ?";
        $params[] = $categoria;
    }

    $where_clause = implode(' AND ', $where_conditions);

    // Estadísticas de movimientos por tipo
    $stmt = $pdo->prepare("
        SELECT
            mi.tipo_movimiento,
            COUNT(*) as cantidad_movimientos,
            SUM(mi.cantidad) as cantidad_total,
            SUM(mi.cantidad * mi.costo_unitario) as valor_total
        FROM movimientos_inventario mi
        INNER JOIN productos p ON mi.producto_id = p.id
        LEFT JOIN categorias c ON p.categoria_id = c.id
        WHERE $where_clause
        GROUP BY mi.tipo_movimiento
        ORDER BY cantidad_total DESC
    ");
    $stmt->execute($params);
    $movimientos_por_tipo = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Productos más movidos
    $stmt = $pdo->prepare("
        SELECT
            p.nombre,
            c.nombre as categoria,
            COUNT(mi.id) as total_movimientos,
            SUM(CASE WHEN mi.tipo_movimiento = 'entrada' THEN mi.cantidad ELSE 0 END) as total_entradas,
            SUM(CASE WHEN mi.tipo_movimiento = 'salida' THEN mi.cantidad ELSE 0 END) as total_salidas,
            i.stock_actual
        FROM movimientos_inventario mi
        INNER JOIN productos p ON mi.producto_id = p.id
        LEFT JOIN categorias c ON p.categoria_id = c.id
        LEFT JOIN inventario i ON p.id = i.producto_id
        WHERE $where_clause
        GROUP BY mi.producto_id, p.nombre, c.nombre, i.stock_actual
        ORDER BY total_movimientos DESC
        LIMIT 20
    ");
    $stmt->execute($params);
    $productos_mas_movidos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Análisis por categoría
    $stmt = $pdo->prepare("
        SELECT
            c.nombre as categoria,
            COUNT(DISTINCT p.id) as productos_categoria,
            SUM(i.stock_actual) as stock_total_categoria,
            SUM(i.stock_actual * i.costo_promedio) as valor_categoria,
            AVG(i.costo_promedio) as costo_promedio_categoria,
            COUNT(CASE WHEN i.stock_actual <= i.stock_minimo THEN 1 END) as productos_stock_bajo_categoria
        FROM productos p
        LEFT JOIN categorias c ON p.categoria_id = c.id
        LEFT JOIN inventario i ON p.id = i.producto_id
        WHERE p.disponible = 1
        GROUP BY c.nombre
        ORDER BY valor_categoria DESC
    ");
    $stmt->execute();
    $analisis_por_categoria = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Rotación de inventario (productos con más salidas)
    $stmt = $pdo->prepare("
        SELECT
            p.nombre,
            c.nombre as categoria,
            i.stock_actual,
            SUM(CASE WHEN mi.tipo_movimiento = 'salida' THEN mi.cantidad ELSE 0 END) as total_salidas,
            CASE
                WHEN i.stock_actual > 0 THEN
                    SUM(CASE WHEN mi.tipo_movimiento = 'salida' THEN mi.cantidad ELSE 0 END) / i.stock_actual
                ELSE 0
            END as rotacion
        FROM productos p
        LEFT JOIN categorias c ON p.categoria_id = c.id
        LEFT JOIN inventario i ON p.id = i.producto_id
        LEFT JOIN movimientos_inventario mi ON p.id = mi.producto_id AND $where_clause
        WHERE p.disponible = 1
        GROUP BY p.id, p.nombre, c.nombre, i.stock_actual
        HAVING total_salidas > 0
        ORDER BY rotacion DESC
        LIMIT 15
    ");
    $stmt->execute($params);
    $rotacion_inventario = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Valorización del inventario por mes (si el período es mayor a 30 días)
    $dias_diferencia = (strtotime($fecha_fin) - strtotime($fecha_inicio)) / (60 * 60 * 24);
    $valorizacion_mensual = [];
    
    if ($dias_diferencia > 30) {
        $stmt = $pdo->prepare("
            SELECT
                DATE_FORMAT(mi.fecha_movimiento, '%Y-%m') as mes,
                SUM(CASE WHEN mi.tipo_movimiento = 'entrada' THEN mi.cantidad * mi.costo_unitario ELSE 0 END) as valor_entradas,
                SUM(CASE WHEN mi.tipo_movimiento = 'salida' THEN mi.cantidad * mi.costo_unitario ELSE 0 END) as valor_salidas
            FROM movimientos_inventario mi
            INNER JOIN productos p ON mi.producto_id = p.id
            LEFT JOIN categorias c ON p.categoria_id = c.id
            WHERE $where_clause
            GROUP BY DATE_FORMAT(mi.fecha_movimiento, '%Y-%m')
            ORDER BY mes DESC
        ");
        $stmt->execute($params);
        $valorizacion_mensual = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Preparar datos para respuesta
    $data = [
        'success' => true,
        'resumen_general' => $resumen_general,
        'productos_stock_bajo' => $productos_stock_bajo,
        'movimientos_por_tipo' => $movimientos_por_tipo,
        'productos_mas_movidos' => $productos_mas_movidos,
        'analisis_por_categoria' => $analisis_por_categoria,
        'rotacion_inventario' => $rotacion_inventario,
        'valorizacion_mensual' => $valorizacion_mensual,
        'tipo_reporte' => $tipo,
        'periodo' => [
            'fecha_inicio' => $fecha_inicio,
            'fecha_fin' => $fecha_fin
        ],
        'filtros' => [
            'categoria' => $categoria
        ]
    ];

    // Respuesta según el tipo solicitado
    if ($view === 'html') {
        include 'views/reporte_inventario_html.php';
    } else {
        echo json_encode($data);
    }

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Error en la base de datos: ' . $e->getMessage()
    ]);
}
?>
