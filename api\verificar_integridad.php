<?php
session_start();
require_once '../config/db.php';

// Verificar autenticación
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

try {
    $problemas = [];
    
    // 1. Verificar órdenes sin cuenta asociada
    $stmt = $pdo->query("
        SELECT COUNT(*) as total 
        FROM ordenes 
        WHERE cuenta_id IS NULL
    ");
    $ordenes_sin_cuenta = $stmt->fetch()['total'];
    
    if ($ordenes_sin_cuenta > 0) {
        $problemas[] = [
            'tipo' => 'ordenes_sin_cuenta',
            'descripcion' => "Hay $ordenes_sin_cuenta órdenes sin cuenta asociada",
            'gravedad' => 'alta'
        ];
    }
    
    // 2. Verificar cuentas con total incorrecto
    $stmt = $pdo->query("
        SELECT c.id, c.nombre_cliente, c.total as total_bd,
               COALESCE(SUM(d.subtotal), 0) as total_real
        FROM cuentas c
        LEFT JOIN ordenes o ON c.id = o.cuenta_id
        LEFT JOIN detalle_orden d ON o.id = d.orden_id
        WHERE c.estado = 'abierta'
        GROUP BY c.id
        HAVING ABS(c.total - COALESCE(SUM(d.subtotal), 0)) > 0.01
    ");
    $cuentas_incorrectas = $stmt->fetchAll();
    
    if (!empty($cuentas_incorrectas)) {
        foreach ($cuentas_incorrectas as $cuenta) {
            $problemas[] = [
                'tipo' => 'total_incorrecto',
                'descripcion' => "Cuenta '{$cuenta['nombre_cliente']}' (ID: {$cuenta['id']}) tiene total incorrecto: BD={$cuenta['total_bd']}, Real={$cuenta['total_real']}",
                'gravedad' => 'media',
                'cuenta_id' => $cuenta['id'],
                'total_correcto' => $cuenta['total_real']
            ];
        }
    }
    
    // 3. Verificar mesas ocupadas sin cuentas abiertas
    $stmt = $pdo->query("
        SELECT m.numero_mesa, m.hash_mesa
        FROM mesas m
        LEFT JOIN cuentas c ON m.hash_mesa = c.hash_mesa AND c.estado = 'abierta'
        WHERE m.estado = 'ocupada' AND c.id IS NULL
    ");
    $mesas_sin_cuentas = $stmt->fetchAll();
    
    if (!empty($mesas_sin_cuentas)) {
        foreach ($mesas_sin_cuentas as $mesa) {
            $problemas[] = [
                'tipo' => 'mesa_sin_cuentas',
                'descripcion' => "Mesa {$mesa['numero_mesa']} está ocupada pero no tiene cuentas abiertas",
                'gravedad' => 'baja',
                'mesa' => $mesa['numero_mesa'],
                'hash_mesa' => $mesa['hash_mesa']
            ];
        }
    }
    
    echo json_encode([
        'success' => true,
        'problemas' => $problemas,
        'total_problemas' => count($problemas)
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
