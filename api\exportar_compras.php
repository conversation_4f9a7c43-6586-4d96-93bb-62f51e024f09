<?php
// Iniciar sesión y conectar a la base de datos
session_start();
require_once '../config/db.php';

// Verificar que el usuario esté logueado y sea administrador
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden exportar reportes']);
    exit();
}

// Obtener parámetros
$fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-m-d', strtotime('-30 days'));
$fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-d');
$proveedor_id = $_GET['proveedor_id'] ?? '';
$estado = $_GET['estado'] ?? '';

// Validar fechas
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_inicio) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_fin)) {
    http_response_code(400);
    echo "Error: Formato de fecha inválido";
    exit();
}

try {
    // Construir consulta
    $where_conditions = ["c.fecha_compra >= ? AND c.fecha_compra <= ?"];
    $params = [$fecha_inicio, $fecha_fin];

    if (!empty($proveedor_id)) {
        $where_conditions[] = "c.proveedor_id = ?";
        $params[] = $proveedor_id;
    }

    if (!empty($estado)) {
        $where_conditions[] = "c.estado = ?";
        $params[] = $estado;
    }

    $where_clause = implode(' AND ', $where_conditions);

    // Obtener datos
    $stmt = $pdo->prepare("
        SELECT 
            c.id,
            c.fecha_compra,
            c.numero_factura,
            p.nombre as proveedor_nombre,
            p.contacto as proveedor_contacto,
            c.subtotal,
            c.impuestos,
            c.total,
            c.estado,
            c.notas,
            u.nombre as usuario_nombre,
            c.fecha_creacion
        FROM compras c
        LEFT JOIN proveedores p ON c.proveedor_id = p.id
        LEFT JOIN usuarios u ON c.usuario_id = u.id
        WHERE $where_clause
        ORDER BY c.fecha_compra DESC, c.id DESC
    ");
    $stmt->execute($params);
    $compras = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Configurar headers para descarga CSV
    $filename = "reporte_compras_" . $fecha_inicio . "_" . $fecha_fin . ".csv";
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Pragma: no-cache');
    header('Expires: 0');

    // Crear archivo CSV
    $output = fopen('php://output', 'w');

    // BOM para UTF-8 (para que Excel abra correctamente los caracteres especiales)
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    // Encabezados del CSV
    fputcsv($output, [
        'ID',
        'Fecha Compra',
        'Número Factura',
        'Proveedor',
        'Contacto Proveedor',
        'Subtotal',
        'Impuestos',
        'Total',
        'Estado',
        'Notas',
        'Usuario',
        'Fecha Creación'
    ]);

    // Datos
    foreach ($compras as $compra) {
        fputcsv($output, [
            $compra['id'],
            $compra['fecha_compra'],
            $compra['numero_factura'] ?: 'Sin número',
            $compra['proveedor_nombre'],
            $compra['proveedor_contacto'],
            'Q' . number_format($compra['subtotal'], 2),
            'Q' . number_format($compra['impuestos'], 2),
            'Q' . number_format($compra['total'], 2),
            ucfirst($compra['estado']),
            $compra['notas'],
            $compra['usuario_nombre'],
            $compra['fecha_creacion']
        ]);
    }

    fclose($output);

} catch (PDOException $e) {
    http_response_code(500);
    echo "Error en la base de datos: " . $e->getMessage();
}
?>
