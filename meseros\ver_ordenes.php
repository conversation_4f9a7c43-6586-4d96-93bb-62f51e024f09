<?php
// Verificar autenticación y rol
require_once '../config/session_config.php';
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    header('Location: ' . url('auth/login.php'));
    exit();
}

require_once '../config/db.php';

// Configurar variables para el layout
$page_title = 'Mis Órdenes';
$breadcrumbs = [
    ['title' => 'Inicio', 'url' => url('')],
    ['title' => 'Meseros', 'url' => url('meseros/')],
    ['title' => 'Mis Órdenes']
];

// Iniciar el buffer de contenido
ob_start();

// Verificar que sea mesero
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'mesero'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    echo '<div class="alert alert-danger">Acceso denegado. Solo meseros pueden acceder.</div>';
    $content = ob_get_clean();
    include '../includes/layout.php';
    exit();
}

// Obtener órdenes del mesero
try {
    $stmt = $pdo->prepare("
        SELECT o.*, m.numero_mesa,
               COUNT(d.id) as total_productos,
               SUM(CASE WHEN d.estado = 'pendiente' THEN 1 ELSE 0 END) as pendientes,
               SUM(CASE WHEN d.estado = 'preparando' THEN 1 ELSE 0 END) as preparando,
               SUM(CASE WHEN d.estado = 'listo' THEN 1 ELSE 0 END) as listos,
               SUM(CASE WHEN d.estado = 'servido' THEN 1 ELSE 0 END) as servidos
        FROM ordenes o
        LEFT JOIN mesas m ON o.hash_mesa = m.hash_mesa
        LEFT JOIN detalle_orden d ON o.id = d.orden_id
        WHERE o.mesero_id = ?
        GROUP BY o.id
        ORDER BY o.fecha_hora DESC
        LIMIT 50
    ");
    $stmt->execute([$_SESSION['usuario_id']]);
    $ordenes = $stmt->fetchAll();
    
    // Agrupar por estado
    $activas = array_filter($ordenes, function($o) { return in_array($o['estado'], ['pendiente', 'en_proceso']); });
    $finalizadas = array_filter($ordenes, function($o) { return $o['estado'] === 'finalizada'; });
    
} catch (PDOException $e) {
    $ordenes = [];
    $activas = [];
    $finalizadas = [];
}
?>

<!-- Estadísticas del Mesero -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-receipt text-primary fs-4"></i>
                </div>
                <h3 class="text-primary mb-1"><?= count($ordenes) ?></h3>
                <p class="text-muted mb-0 small">Total Órdenes</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="bg-dark bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-clock-fill text-dark fs-4"></i>
                </div>
                <h3 class="text-dark mb-1"><?= count($activas) ?></h3>
                <p class="text-muted mb-0 small">Activas</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-check-circle-fill text-success fs-4"></i>
                </div>
                <h3 class="text-success mb-1"><?= count($finalizadas) ?></h3>
                <p class="text-muted mb-0 small">Finalizadas</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-currency-dollar text-info fs-4"></i>
                </div>
                <h3 class="text-info mb-1">$<?= number_format(array_sum(array_column($ordenes, 'total')), 0) ?></h3>
                <p class="text-muted mb-0 small">Total Ventas</p>
            </div>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="btn-group" role="group">
                        <button class="btn btn-outline-primary active" data-filter="all">Todas</button>
                        <button class="btn btn-outline-primary" data-filter="activas">Activas</button>
                        <button class="btn btn-outline-primary" data-filter="finalizadas">Finalizadas</button>
                    </div>
                    <button class="btn btn-outline-secondary rounded-pill" onclick="location.reload()">
                        <i class="bi bi-arrow-clockwise me-2"></i>
                        Actualizar
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Órdenes -->
<div class="row g-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="bi bi-list-ul me-2"></i>
                    Mis Órdenes
                </h5>
            </div>
            <div class="card-body p-0">
                <?php if (empty($ordenes)): ?>
                    <div class="text-center py-5">
                        <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                            <i class="bi bi-receipt text-muted fs-2"></i>
                        </div>
                        <h6 class="text-muted">No tienes órdenes registradas</h6>
                        <p class="text-muted mb-4">Comienza tomando tu primera orden</p>
                        <a href="tomar_orden.php" class="btn btn-primary rounded-pill">
                            <i class="bi bi-plus-circle me-2"></i>
                            Tomar Primera Orden
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-semibold text-muted small">Orden #</th>
                                    <th class="border-0 fw-semibold text-muted small">Mesa</th>
                                    <th class="border-0 fw-semibold text-muted small">Productos</th>
                                    <th class="border-0 fw-semibold text-muted small">Estado</th>
                                    <th class="border-0 fw-semibold text-muted small">Total</th>
                                    <th class="border-0 fw-semibold text-muted small">Fecha</th>
                                    <th class="border-0 fw-semibold text-muted small">Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($ordenes as $orden): ?>
                                    <tr class="orden-row" data-estado="<?= in_array($orden['estado'], ['pendiente', 'en_proceso']) ? 'activas' : 'finalizadas' ?>">
                                        <td class="border-0 py-3">
                                            <span class="fw-semibold text-primary">#<?= $orden['id'] ?></span>
                                        </td>
                                        <td class="border-0 py-3">
                                            <span class="fw-medium">Mesa #<?= $orden['numero_mesa'] ?></span>
                                        </td>
                                        <td class="border-0 py-3">
                                            <div class="small">
                                                <div><?= $orden['total_productos'] ?> productos</div>
                                                <?php if ($orden['pendientes'] > 0): ?>
                                                    <span class="badge bg-dark bg-opacity-10 text-dark rounded-pill me-1">
                                                        <?= $orden['pendientes'] ?> pendientes
                                                    </span>
                                                <?php endif; ?>
                                                <?php if ($orden['preparando'] > 0): ?>
                                                    <span class="badge bg-preparando bg-opacity-10 text-preparando rounded-pill me-1">
                                                        <?= $orden['preparando'] ?> preparando
                                                    </span>
                                                <?php endif; ?>
                                                <?php if ($orden['listos'] > 0): ?>
                                                    <span class="badge bg-listo bg-opacity-10 text-listo rounded-pill me-1">
                                                        <?= $orden['listos'] ?> listos
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td class="border-0 py-3">
                                            <span class="estado-<?= $orden['estado'] ?>">
                                                <i class="bi bi-circle-fill me-1" style="font-size: 0.5rem;"></i>
                                                <?= ucfirst(str_replace('_', ' ', $orden['estado'])) ?>
                                            </span>
                                        </td>
                                        <td class="border-0 py-3">
                                            <span class="fw-semibold">$<?= number_format($orden['total'], 0) ?></span>
                                        </td>
                                        <td class="border-0 py-3">
                                            <span class="text-muted small"><?= date('d/m/Y H:i', strtotime($orden['fecha_hora'])) ?></span>
                                        </td>
                                        <td class="border-0 py-3">
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="orden_detalle.php?id=<?= $orden['id'] ?>" class="btn btn-outline-primary rounded-pill me-1">
                                                    <i class="bi bi-eye me-1"></i>
                                                    Ver
                                                </a>
                                                <?php if ($orden['estado'] != 'finalizada'): ?>
                                                    <?php if ($orden['hash_mesa']): ?>
                                                        <a href="mesa_cuentas.php?hash=<?= $orden['hash_mesa'] ?>" class="btn btn-outline-info rounded-pill me-1">
                                                            <i class="bi bi-table me-1"></i>
                                                            Mesa
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if ($orden['listos'] > 0): ?>
                                                        <button class="btn btn-outline-success rounded-pill marcar-servido-btn" 
                                                                data-orden="<?= $orden['id'] ?>">
                                                            <i class="bi bi-check-all me-1"></i>
                                                            Servido
                                                        </button>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// JavaScript adicional
$extra_js = '
<script>
document.addEventListener("DOMContentLoaded", function() {
    // Filtros
    document.querySelectorAll("[data-filter]").forEach(btn => {
        btn.addEventListener("click", function() {
            const filter = this.dataset.filter;
            
            // Actualizar botones activos
            document.querySelectorAll("[data-filter]").forEach(b => b.classList.remove("active"));
            this.classList.add("active");
            
            // Filtrar filas
            document.querySelectorAll(".orden-row").forEach(row => {
                if (filter === "all" || row.dataset.estado === filter) {
                    row.style.display = "";
                } else {
                    row.style.display = "none";
                }
            });
        });
    });
    
    // Marcar como servido
    document.querySelectorAll(".marcar-servido-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const ordenId = this.dataset.orden;
            
            if (confirm("¿Marcar todos los productos listos como servidos?")) {
                marcarComoServido(ordenId);
            }
        });
    });
});

function marcarComoServido(ordenId) {
    fetch("../api/marcar_servido.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            orden_id: ordenId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            showToast(data.message, "error");
        }
    })
    .catch(error => {
        console.error("Error:", error);
        showToast("Error de conexión", "error");
    });
}
</script>
';

// Incluir el layout
include '../includes/layout.php';
?>
