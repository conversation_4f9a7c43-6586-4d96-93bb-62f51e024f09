<?php
// Configurar variables para el layout
$page_title = 'Detalle de Cuenta';
$breadcrumbs = [
    ['title' => 'Inicio', 'url' => '/Restaurante/'],
    ['title' => 'Meseros', 'url' => '/Restaurante/meseros/'],
    ['title' => 'Detalle de Cuenta']
];

// Iniciar el buffer de contenido
ob_start();

// Obtener ID de la cuenta
$cuenta_id = $_GET['cuenta_id'] ?? '';

if (empty($cuenta_id) || !is_numeric($cuenta_id)) {
    echo '<div class="alert alert-danger">ID de cuenta no válido.</div>';
    $content = ob_get_clean();
    include '../includes/layout.php';
    exit();
}

// Conectar a la base de datos
session_start();
require_once '../config/db.php';

try {
    // Obtener información de la cuenta
    $stmt = $pdo->prepare("
        SELECT c.*, u.nombre as mesero_nombre, m.numero_mesa
        FROM cuentas c
        INNER JOIN usuarios u ON c.mesero_id = u.id
        INNER JOIN mesas m ON c.hash_mesa = m.hash_mesa
        WHERE c.id = ?
    ");
    $stmt->execute([$cuenta_id]);
    $cuenta = $stmt->fetch();
    
    if (!$cuenta) {
        echo '<div class="alert alert-danger">Cuenta no encontrada.</div>';
        $content = ob_get_clean();
        include '../includes/layout.php';
        exit();
    }
    
    // Obtener todas las órdenes de la cuenta con sus productos
    $stmt = $pdo->prepare("
        SELECT o.*,
               COUNT(d.id) as total_productos,
               SUM(d.subtotal) as total_orden_real
        FROM ordenes o
        LEFT JOIN detalle_orden d ON o.id = d.orden_id
        WHERE o.cuenta_id = ?
        GROUP BY o.id
        ORDER BY o.fecha_hora DESC
    ");
    $stmt->execute([$cuenta_id]);
    $ordenes = $stmt->fetchAll();


    
    // Para cada orden, obtener el detalle de productos
    for ($i = 0; $i < count($ordenes); $i++) {
        $stmt = $pdo->prepare("
            SELECT d.*, p.nombre as producto_nombre, p.area as producto_area
            FROM detalle_orden d
            INNER JOIN productos p ON d.producto_id = p.id
            WHERE d.orden_id = ?
            ORDER BY d.id DESC
        ");
        $stmt->execute([$ordenes[$i]['id']]);
        $ordenes[$i]['productos'] = $stmt->fetchAll();


    }
    
    // Calcular estadísticas de la cuenta
    $total_productos = 0;
    $productos_pendientes = 0;
    $productos_preparando = 0;
    $productos_listos = 0;
    $productos_servidos = 0;
    $total_real = 0;
    
    foreach ($ordenes as $orden) {
        foreach ($orden['productos'] as $producto) {
            $total_productos++;
            $total_real += $producto['subtotal'];
            
            switch ($producto['estado']) {
                case 'pendiente': $productos_pendientes++; break;
                case 'preparando': $productos_preparando++; break;
                case 'listo': $productos_listos++; break;
                case 'servido': $productos_servidos++; break;
            }
        }
    }
    
    // Actualizar total de la cuenta si es diferente
    if ($total_real != $cuenta['total']) {
        $stmt = $pdo->prepare("UPDATE cuentas SET total = ? WHERE id = ?");
        $stmt->execute([$total_real, $cuenta_id]);
        $cuenta['total'] = $total_real;
    }
    
} catch (PDOException $e) {
    echo '<div class="alert alert-danger">Error de base de datos: ' . $e->getMessage() . '</div>';
    $content = ob_get_clean();
    include '../includes/layout.php';
    exit();
}
?>

<!-- Información de la Cuenta -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center">
                            <div class="bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                                <i class="bi bi-person-circle text-primary fs-3"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">
                                    <?= htmlspecialchars($cuenta['nombre_cliente']) ?>
                                    <?php if (!empty($cuenta['apellido_cliente'])): ?>
                                        <?= htmlspecialchars($cuenta['apellido_cliente']) ?>
                                    <?php endif; ?>
                                </h4>
                                <p class="text-muted mb-0">
                                    <i class="bi bi-table me-1"></i>
                                    Mesa #<?= $cuenta['numero_mesa'] ?> • 
                                    <i class="bi bi-person me-1"></i>
                                    Mesero: <?= htmlspecialchars($cuenta['mesero_nombre']) ?>
                                </p>
                                <small class="text-muted">
                                    <i class="bi bi-clock me-1"></i>
                                    Creada: <?= date('d/m/Y H:i', strtotime($cuenta['fecha_creacion'])) ?>
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="mb-2">
                            <span class="badge bg-<?= $cuenta['estado'] === 'abierta' ? 'success' : ($cuenta['estado'] === 'cerrada' ? 'warning' : 'secondary') ?> bg-opacity-10 text-<?= $cuenta['estado'] === 'abierta' ? 'success' : ($cuenta['estado'] === 'cerrada' ? 'warning' : 'secondary') ?> rounded-pill fs-6 px-3 py-2">
                                <?= ucfirst($cuenta['estado']) ?>
                            </span>
                        </div>
                        <h3 class="text-primary mb-0">Q<?= number_format($cuenta['total'], 2) ?></h3>
                        <small class="text-muted">Subtotal</small>
                        <?php
                        $propina_sugerida = $cuenta['total'] * 0.10; // 10% de propina
                        $total_con_propina = $cuenta['total'] + $propina_sugerida;
                        ?>
                        <div class="mt-2 pt-2 border-top">
                            <div class="d-flex justify-content-between small text-muted mb-1">
                                <span>Propina sugerida (10%):</span>
                                <span>+Q<?= number_format($propina_sugerida, 2) ?></span>
                            </div>
                            <div class="d-flex justify-content-between fw-bold text-success fs-5">
                                <span>Total con propina:</span>
                                <span>Q<?= number_format($total_con_propina, 2) ?></span>
                            </div>
                            <small class="text-muted d-block mt-1">
                                <i class="bi bi-info-circle me-1"></i>
                                Puedes ajustar la propina usando el botón "Propina"
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Estadísticas de Productos -->
<div class="row g-3 mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center p-3">
                <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">
                    <i class="bi bi-grid-3x3-gap text-primary"></i>
                </div>
                <h5 class="text-primary mb-1"><?= $total_productos ?></h5>
                <small class="text-muted">Total Productos</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center p-3">
                <div class="bg-dark bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">
                    <i class="bi bi-clock text-dark"></i>
                </div>
                <h5 class="text-dark mb-1"><?= $productos_pendientes ?></h5>
                <small class="text-muted">Pendientes</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center p-3">
                <div class="bg-preparando bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">
                    <i class="bi bi-arrow-repeat text-preparando"></i>
                </div>
                <h5 class="text-preparando mb-1"><?= $productos_preparando ?></h5>
                <small class="text-muted">Preparando</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center p-3">
                <div class="bg-servido bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">
                    <i class="bi bi-check-circle text-servido"></i>
                </div>
                <h5 class="text-servido mb-1"><?= $productos_servidos ?></h5>
                <small class="text-muted">Servidos</small>
            </div>
        </div>
    </div>
</div>

<!-- Órdenes de la Cuenta -->
<div class="row g-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-receipt me-2"></i>
                        Órdenes (<?= count($ordenes) ?>)
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary btn-sm rounded-pill" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise me-1"></i>
                            Actualizar
                        </button>
                        <a href="/Restaurante/meseros/mesa_cuentas.php?hash=<?= $cuenta['hash_mesa'] ?>" class="btn btn-outline-secondary btn-sm rounded-pill">
                            <i class="bi bi-arrow-left me-1"></i>
                            Volver a Cuentas
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (empty($ordenes)): ?>
                    <div class="text-center py-5">
                        <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                            <i class="bi bi-receipt text-muted fs-2"></i>
                        </div>
                        <h6 class="text-muted">No hay órdenes en esta cuenta</h6>
                        <p class="text-muted mb-4">Agrega la primera orden a esta cuenta</p>
                        <a href="/Restaurante/meseros/tomar_orden.php?hash=<?= $cuenta['hash_mesa'] ?>&cuenta_id=<?= $cuenta['id'] ?>" class="btn btn-primary rounded-pill">
                            <i class="bi bi-plus-circle me-2"></i>
                            Agregar Primera Orden
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-semibold text-muted small">Orden</th>
                                    <th class="border-0 fw-semibold text-muted small">Productos</th>
                                    <th class="border-0 fw-semibold text-muted small">Total</th>
                                    <th class="border-0 fw-semibold text-muted small">Estado</th>
                                    <th class="border-0 fw-semibold text-muted small">Fecha</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($ordenes as $orden): ?>
                                    <tr>
                                        <td class="border-0 py-3">
                                            <div class="fw-semibold">#<?= $orden['id'] ?></div>
                                        </td>
                                        <td class="border-0 py-3">
                                            <div class="d-flex flex-wrap gap-1">
                                                <?php foreach ($orden['productos'] as $producto): ?>
                                                    <span class="badge bg-<?= $producto['producto_area'] === 'cocina' ? 'danger' : 'info' ?> bg-opacity-10 text-<?= $producto['producto_area'] === 'cocina' ? 'danger' : 'info' ?> rounded-pill">
                                                        <?= $producto['cantidad'] ?>x <?= htmlspecialchars($producto['producto_nombre']) ?>
                                                        <span class="badge bg-<?=
                                                            $producto['estado'] === 'pendiente' ? 'pendiente' :
                                                            ($producto['estado'] === 'preparando' ? 'preparando' :
                                                            ($producto['estado'] === 'listo' ? 'listo' : 'servido'))
                                                        ?> ms-1"><?= ucfirst($producto['estado']) ?></span>
                                                    </span>
                                                <?php endforeach; ?>
                                            </div>
                                        </td>
                                        <td class="border-0 py-3">
                                            <span class="fw-semibold">Q<?= number_format($orden['total_orden_real'] ?? $orden['total'], 2) ?></span>
                                        </td>
                                        <td class="border-0 py-3">
                                            <span class="badge bg-<?= $orden['estado'] === 'pendiente' ? 'pendiente' : ($orden['estado'] === 'en_proceso' ? 'preparando' : 'servido') ?> bg-opacity-10 text-<?= $orden['estado'] === 'pendiente' ? 'pendiente' : ($orden['estado'] === 'en_proceso' ? 'preparando' : 'servido') ?> rounded-pill">
                                                <?= ucfirst(str_replace('_', ' ', $orden['estado'])) ?>
                                            </span>
                                        </td>
                                        <td class="border-0 py-3">
                                            <small class="text-muted">
                                                <?= date('d/m/Y H:i', strtotime($orden['fecha_hora'])) ?>
                                            </small>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Acciones de la Cuenta -->
<?php if ($cuenta['estado'] === 'abierta'): ?>
<div class="row g-4 mt-2">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4 text-center">
                <h6 class="mb-3">Acciones de la Cuenta</h6>
                <div class="d-flex gap-2 justify-content-center flex-wrap">
                    <a href="/Restaurante/meseros/tomar_orden.php?hash=<?= $cuenta['hash_mesa'] ?>&cuenta_id=<?= $cuenta['id'] ?>&cliente=<?= urlencode($cuenta['nombre_cliente']) ?>" class="btn btn-primary rounded-pill">
                        <i class="bi bi-plus-circle me-2"></i>
                        Agregar Productos
                    </a>
                    <button class="btn btn-info rounded-pill" onclick="generarTicket(<?= $cuenta['id'] ?>)">
                        <i class="bi bi-receipt me-2"></i>
                        Generar Ticket
                    </button>
                    <button class="btn btn-warning rounded-pill" onclick="abrirAjustesCuenta(<?= $cuenta['id'] ?>, '<?= htmlspecialchars($cuenta['nombre_cliente']) ?>')">
                        <i class="bi bi-gear me-2"></i>
                        Propina
                    </button>
                    <button class="btn btn-success rounded-pill pagar-cuenta-btn" data-cuenta-id="<?= $cuenta['id'] ?>">
                        <i class="bi bi-credit-card me-2"></i>
                        Procesar Pago
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// JavaScript adicional
$extra_js = '
<script>
document.addEventListener("DOMContentLoaded", function() {
    // Pagar cuenta
    document.querySelectorAll(".pagar-cuenta-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const cuentaId = this.dataset.cuentaId;
            // Pagar cuenta directamente sin confirmación
            pagarCuenta(cuentaId);
        });
    });
});

function generarTicket(cuentaId) {
    // Abrir ticket en nueva ventana
    const ticketUrl = "/Restaurante/imprimir_ticket.php?cuenta_id=" + cuentaId;
    const ticketWindow = window.open(ticketUrl, "ticket", "width=400,height=600,scrollbars=yes,resizable=yes");

    if (!ticketWindow) {
        showToast("Por favor, permita las ventanas emergentes para generar el ticket", "warning");
    }
}

function pagarCuenta(cuentaId) {
    fetch("/Restaurante/api/cerrar_cuenta.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            cuenta_id: cuentaId,
            accion: "pagar"
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Generar ticket automáticamente después del pago
            generarTicketAutomatico(cuentaId);

            showToast("Cuenta pagada exitosamente", "success");
            setTimeout(() => {
                // Redirigir a la lista de mesas o recargar
                window.location.href = "/Restaurante/meseros/";
            }, 3000);
        } else {
            showToast("Error: " + data.message, "error");
        }
    })
    .catch(error => {
        console.error("Error:", error);
        showToast("Error de conexión", "error");
    });
}

function generarTicketAutomatico(cuentaId) {
    // Abrir ticket en nueva ventana con auto-impresión
    const ticketUrl = "/Restaurante/imprimir_ticket.php?cuenta_id=" + cuentaId + "&auto_print=1";
    const ticketWindow = window.open(ticketUrl, "ticket", "width=400,height=600,scrollbars=yes,resizable=yes");

    if (!ticketWindow) {
        showToast("Por favor, permita las ventanas emergentes para generar el ticket", "warning");
    } else {
        showToast("Generando ticket para imprimir...", "info");
    }
}

function abrirAjustesCuenta(cuentaId, cuentaNombre) {
    // Abrir página de ajustes en nueva ventana
    const ajustesUrl = "/Restaurante/meseros/ajustes_cuenta.php?cuenta_id=" + cuentaId;
    const ajustesWindow = window.open(ajustesUrl, "ajustes", "width=800,height=700,scrollbars=yes,resizable=yes");

    if (!ajustesWindow) {
        showToast("Por favor, permita las ventanas emergentes para abrir los ajustes", "warning");
    } else {
        showToast(`Abriendo ajustes para ${cuentaNombre}`, "info");
    }
}

function pagarCuenta(cuentaId) {
    fetch("/Restaurante/api/cerrar_cuenta.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            cuenta_id: cuentaId,
            accion: "pagar"
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast("Cuenta pagada exitosamente", "success");
            setTimeout(() => location.reload(), 2000);
        } else {
            showToast("Error: " + data.message, "error");
        }
    })
    .catch(error => {
        console.error("Error:", error);
        showToast("Error de conexión", "error");
    });
}
</script>
';

// Incluir el layout
include '../includes/layout.php';
?>
