<?php
/**
 * Configuración de sesiones y rutas para el sistema de restaurante
 * Este archivo debe incluirse antes de cualquier llamada a session_start()
 */

// ========================================
// CONFIGURACIÓN DE RUTAS DINÁMICAS
// ========================================

// Detectar la ruta base del proyecto automáticamente
if (!defined('BASE_URL')) {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];

    // Obtener la ruta del script actual
    $script_name = $_SERVER['SCRIPT_NAME'];

    // Encontrar la carpeta del proyecto
    $project_path = '';
    if (strpos($script_name, '/Restaurante/') !== false) {
        $project_path = '/Restaurante';
    } else {
        // Si no está en /Restaurante/, detectar automáticamente
        $path_parts = explode('/', dirname($script_name));
        foreach ($path_parts as $part) {
            if (!empty($part)) {
                $project_path .= '/' . $part;
                // Verificar si este es el directorio del proyecto
                if (file_exists($_SERVER['DOCUMENT_ROOT'] . $project_path . '/index.php') &&
                    file_exists($_SERVER['DOCUMENT_ROOT'] . $project_path . '/config/db.php')) {
                    break;
                }
            }
        }
    }

    define('BASE_URL', $protocol . '://' . $host . $project_path);
    define('BASE_PATH', $project_path);
}

// ========================================
// CONFIGURACIÓN DE SESIONES
// ========================================

// Solo configurar si las sesiones no han sido iniciadas
if (session_status() === PHP_SESSION_NONE) {
    // Configurar directorio de sesiones personalizado
    $session_path = dirname(__DIR__) . '/tmp/sessions';

    // Crear el directorio si no existe
    if (!is_dir($session_path)) {
        mkdir($session_path, 0777, true);
    }

    // Configurar la ruta de sesiones
    session_save_path($session_path);

    // Configurar parámetros de sesión para mejor rendimiento y seguridad
    ini_set('session.gc_probability', 1);
    ini_set('session.gc_divisor', 100);
    ini_set('session.gc_maxlifetime', 3600); // 1 hora
    ini_set('session.cookie_lifetime', 0); // Hasta cerrar navegador
    ini_set('session.cookie_httponly', 1); // Solo HTTP, no JavaScript
    ini_set('session.use_strict_mode', 1); // Modo estricto

    // Configurar nombre de sesión personalizado
    session_name('RESTAURANTE_SESSION');

    // Configurar cookie path para el proyecto específico
    ini_set('session.cookie_path', BASE_PATH . '/');
}

// ========================================
// FUNCIONES AUXILIARES
// ========================================

/**
 * Generar URL completa para el proyecto
 */
function url($path = '') {
    return BASE_URL . '/' . ltrim($path, '/');
}

/**
 * Generar ruta relativa para el proyecto
 */
function path($path = '') {
    return BASE_PATH . '/' . ltrim($path, '/');
}
?>
