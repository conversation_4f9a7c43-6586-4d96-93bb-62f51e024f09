<?php
header('Content-Type: application/json');
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Verificar que sea un mesero o administrador
require_once '../config/db.php';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol IN ('mesero', 'admin')");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo meseros y administradores pueden crear órdenes']);
    exit();
}

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);
$cuenta_id = $input['cuenta_id'] ?? '';
$productos = $input['productos'] ?? [];

if (empty($cuenta_id) || empty($productos)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID de cuenta y productos son requeridos']);
    exit();
}

try {
    // Verificar que la cuenta existe y está abierta
    $stmt = $pdo->prepare("SELECT c.*, m.numero_mesa FROM cuentas c INNER JOIN mesas m ON c.hash_mesa = m.hash_mesa WHERE c.id = ? AND c.estado = 'abierta'");
    $stmt->execute([$cuenta_id]);
    $cuenta = $stmt->fetch();
    
    if (!$cuenta) {
        echo json_encode(['success' => false, 'message' => 'Cuenta no encontrada o ya está cerrada']);
        exit();
    }
    
    // Verificar que el mesero tenga acceso a esta cuenta (si no es admin)
    if ($usuario['rol'] === 'mesero' && $cuenta['mesero_id'] != $usuario['id']) {
        echo json_encode(['success' => false, 'message' => 'No tienes permisos para esta cuenta']);
        exit();
    }
    
    // Iniciar transacción
    $pdo->beginTransaction();
    
    try {
        // Crear la orden principal
        $stmt = $pdo->prepare("
            INSERT INTO ordenes (cuenta_id, mesa, mesero_id, hash_mesa, estado, fecha_hora) 
            VALUES (?, ?, ?, ?, 'pendiente', NOW())
        ");
        
        $stmt->execute([
            $cuenta_id,
            $cuenta['numero_mesa'],
            $cuenta['mesero_id'],
            $cuenta['hash_mesa']
        ]);
        
        $orden_id = $pdo->lastInsertId();
        $total_orden = 0;
        
        // Insertar los detalles de la orden
        foreach ($productos as $producto) {
            // Verificar que el producto existe y está disponible
            $stmt = $pdo->prepare("SELECT * FROM productos WHERE id = ? AND disponible = 1");
            $stmt->execute([$producto['id']]);
            $producto_db = $stmt->fetch();
            
            if (!$producto_db) {
                throw new Exception("Producto con ID {$producto['id']} no encontrado o no disponible");
            }
            
            // Calcular subtotal
            $cantidad = intval($producto['cantidad']);
            $precio_unitario = floatval($producto_db['precio_venta'] ?? $producto_db['precio'] ?? 0);
            $subtotal = $precio_unitario * $cantidad;
            $total_orden += $subtotal;
            
            // Obtener notas del producto (si las hay)
            $notas = isset($producto['notas']) ? trim($producto['notas']) : '';

            // Insertar detalle de orden
            $stmt = $pdo->prepare("
                INSERT INTO detalle_orden (orden_id, producto_id, cantidad, precio_unitario, subtotal, area, estado, notas)
                VALUES (?, ?, ?, ?, ?, ?, 'pendiente', ?)
            ");

            $stmt->execute([
                $orden_id,
                $producto['id'],
                $cantidad,
                $precio_unitario,
                $subtotal,
                $producto_db['area'],
                $notas
            ]);
        }
        
        // Actualizar total de la orden
        $stmt = $pdo->prepare("UPDATE ordenes SET total = ? WHERE id = ?");
        $stmt->execute([$total_orden, $orden_id]);
        
        // Actualizar total de la cuenta
        $stmt = $pdo->prepare("
            UPDATE cuentas 
            SET total = (
                SELECT COALESCE(SUM(d.subtotal), 0) 
                FROM detalle_orden d 
                INNER JOIN ordenes o ON d.orden_id = o.id 
                WHERE o.cuenta_id = ?
            )
            WHERE id = ?
        ");
        $stmt->execute([$cuenta_id, $cuenta_id]);
        
        // Confirmar transacción
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Orden creada exitosamente',
            'orden_id' => $orden_id,
            'cuenta_id' => $cuenta_id,
            'cliente' => $cuenta['nombre_cliente'] . ' ' . $cuenta['apellido_cliente'],
            'mesa' => $cuenta['numero_mesa'],
            'total_orden' => $total_orden,
            'productos_count' => count($productos)
        ]);
        
    } catch (Exception $e) {
        // Revertir transacción en caso de error
        $pdo->rollBack();
        throw $e;
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
