<?php

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();
require_once '../config/db.php';
require_once '../includes/inventario_helper.php';

header('Content-Type: application/json');

// Verificar que sea administrador
if (!isset($_SESSION['usuario_id'])) {
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

$stmt = $pdo->prepare("SELECT rol FROM usuarios WHERE id = ?");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario || $usuario['rol'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden ajustar stock']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit();
}

try {
    // Validar datos requeridos
    $producto_id = $_POST['producto_id'] ?? '';
    $tipo_ajuste = $_POST['tipo_ajuste'] ?? '';
    $cantidad = $_POST['cantidad'] ?? '';
    $motivo = $_POST['motivo'] ?? '';
    $motivo_otro = $_POST['motivo_otro'] ?? '';

    if (empty($producto_id) || empty($tipo_ajuste) || empty($cantidad) || empty($motivo)) {
        throw new Exception('Faltan datos requeridos');
    }

    $cantidad = (int) $cantidad;
    if ($cantidad <= 0) {
        throw new Exception('La cantidad debe ser mayor a 0');
    }

    // Si el motivo es "Otro", usar el motivo personalizado
    if ($motivo === 'Otro') {
        if (empty($motivo_otro)) {
            throw new Exception('Debe especificar el motivo cuando selecciona "Otro"');
        }
        $motivo = $motivo_otro;
    }

    // Obtener información del producto
    $stmt = $pdo->prepare("
        SELECT p.nombre, i.stock_actual, i.unidad_medida 
        FROM productos p 
        LEFT JOIN inventario i ON p.id = i.producto_id 
        WHERE p.id = ?
    ");
    $stmt->execute([$producto_id]);
    $producto = $stmt->fetch();

    if (!$producto) {
        throw new Exception('Producto no encontrado');
    }

    $stock_actual = (int) $producto['stock_actual'];
    $nombre_producto = $producto['nombre'];

    // Calcular el movimiento según el tipo de ajuste
    $tipo_movimiento = '';
    $cantidad_movimiento = 0;
    $stock_final = 0;

    switch ($tipo_ajuste) {
        case 'entrada':
            $tipo_movimiento = 'entrada';
            $cantidad_movimiento = $cantidad;
            $stock_final = $stock_actual + $cantidad;
            break;

        case 'salida':
            $tipo_movimiento = 'salida';
            $cantidad_movimiento = $cantidad;
            $stock_final = $stock_actual - $cantidad;
            
            if ($stock_final < 0) {
                throw new Exception("No se puede reducir el stock por debajo de 0. Stock actual: $stock_actual");
            }
            break;

        case 'ajuste':
            if ($cantidad > $stock_actual) {
                $tipo_movimiento = 'entrada';
                $cantidad_movimiento = $cantidad - $stock_actual;
            } elseif ($cantidad < $stock_actual) {
                $tipo_movimiento = 'salida';
                $cantidad_movimiento = $stock_actual - $cantidad;
            } else {
                throw new Exception('El stock ya tiene la cantidad especificada');
            }
            $stock_final = $cantidad;
            break;

        default:
            throw new Exception('Tipo de ajuste no válido');
    }

    // Registrar el movimiento usando la función helper
    $resultado = registrarMovimientoInventario(
        $pdo,
        $producto_id,
        $tipo_movimiento,
        $cantidad_movimiento,
        'ajuste_manual',
        null,
        "Ajuste manual: $motivo",
        $_SESSION['usuario_id']
    );

    if (!$resultado['success']) {
        throw new Exception("Error al registrar el ajuste: " . $resultado['error']);
    }

    echo json_encode([
        'success' => true,
        'message' => 'Stock ajustado exitosamente',
        'producto' => $nombre_producto,
        'stock_anterior' => $stock_actual,
        'stock_nuevo' => $stock_final,
        'tipo_ajuste' => $tipo_ajuste,
        'cantidad_movimiento' => $cantidad_movimiento,
        'motivo' => $motivo
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
