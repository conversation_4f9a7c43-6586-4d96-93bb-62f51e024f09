<?php
header('Content-Type: application/json');

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Verificar que sea mesero
require_once '../config/db.php';
require_once '../includes/inventario_helper.php';

$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'mesero'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo meseros pueden marcar productos como servidos']);
    exit();
}

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);
$orden_id = $input['orden_id'] ?? null;

// Validaciones
if (!$orden_id) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID de orden requerido']);
    exit();
}

try {
    // Verificar que la orden pertenece al mesero
    $stmt = $pdo->prepare("SELECT * FROM ordenes WHERE id = ? AND mesero_id = ?");
    $stmt->execute([$orden_id, $_SESSION['usuario_id']]);
    $orden = $stmt->fetch();

    if (!$orden) {
        echo json_encode(['success' => false, 'message' => 'Orden no encontrada o no tienes permisos']);
        exit();
    }

    // Iniciar transacción
    $pdo->beginTransaction();

    // Obtener productos que van a ser marcados como servidos para descontar inventario
    $stmt = $pdo->prepare("
        SELECT d.*, p.nombre as producto_nombre
        FROM detalle_orden d
        LEFT JOIN productos p ON d.producto_id = p.id
        WHERE d.orden_id = ? AND d.estado = 'listo'
    ");
    $stmt->execute([$orden_id]);
    $productos_a_servir = $stmt->fetchAll();

    // Marcar todos los productos listos como servidos
    $stmt = $pdo->prepare("
        UPDATE detalle_orden
        SET estado = 'servido'
        WHERE orden_id = ? AND estado = 'listo'
    ");
    $stmt->execute([$orden_id]);

    $productos_servidos = $stmt->rowCount();

    // Descontar inventario para cada producto servido
    $inventario_errores = [];
    foreach ($productos_a_servir as $producto) {
        $resultado = descontarInventarioVenta(
            $pdo,
            $producto['producto_id'],
            $producto['cantidad'],
            $orden_id,
            $_SESSION['usuario_id']
        );

        if (!$resultado['success']) {
            $inventario_errores[] = "Error en {$producto['producto_nombre']}: {$resultado['error']}";
            error_log("Error al descontar inventario: " . $resultado['error']);
        }
    }
    
    // Verificar si todos los productos están servidos para finalizar la orden
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as total,
               SUM(CASE WHEN estado = 'servido' THEN 1 ELSE 0 END) as servidos
        FROM detalle_orden 
        WHERE orden_id = ?
    ");
    $stmt->execute([$orden_id]);
    $estado_orden = $stmt->fetch();
    
    if ($estado_orden['total'] == $estado_orden['servidos']) {
        // Todos los productos están servidos, finalizar la orden
        $stmt = $pdo->prepare("UPDATE ordenes SET estado = 'finalizada' WHERE id = ?");
        $stmt->execute([$orden_id]);
        $orden_finalizada = true;
    } else {
        $orden_finalizada = false;
    }

    // Confirmar transacción
    $pdo->commit();

    // Preparar respuesta
    $response = [
        'success' => true,
        'message' => 'Productos marcados como servidos',
        'orden_id' => $orden_id,
        'productos_servidos' => $productos_servidos,
        'orden_finalizada' => $orden_finalizada
    ];

    // Agregar advertencias de inventario si las hay
    if (!empty($inventario_errores)) {
        $response['inventario_warnings'] = $inventario_errores;
        $response['message'] .= ' (con advertencias de inventario)';
    } else {
        $response['message'] .= ' e inventario actualizado';
    }

    echo json_encode($response);
    
} catch (Exception $e) {
    // Rollback en caso de error
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
