<?php
header('Content-Type: application/json');

// Iniciar sesión y conectar a la base de datos

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();
require_once '../config/db.php';

// Verificar que el usuario esté logueado y sea administrador
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden acceder a reportes']);
    exit();
}

// Obtener parámetros
$fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-m-d', strtotime('-7 days'));
$fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-d');
$mesero_id = $_GET['mesero_id'] ?? '';
$limit = $_GET['limit'] ?? 10;

// Validar fechas
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_inicio) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_fin)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Formato de fecha inválido']);
    exit();
}

try {
    // Construir consulta para productos más vendidos
    $sql = "
        SELECT p.id, p.nombre, p.area,
               SUM(d.cantidad) as cantidad_vendida,
               SUM(d.subtotal) as ingresos_totales,
               COUNT(DISTINCT o.id) as ordenes_diferentes,
               AVG(d.precio_unitario) as precio_promedio
        FROM detalle_orden d
        INNER JOIN productos p ON d.producto_id = p.id
        INNER JOIN ordenes o ON d.orden_id = o.id
        WHERE o.fecha_hora >= ? AND o.fecha_hora <= ?
        AND o.estado = 'finalizada'
    ";
    
    $params = [$fecha_inicio . ' 00:00:00', $fecha_fin . ' 23:59:59'];
    
    // Filtrar por mesero si se especifica
    if (!empty($mesero_id) && is_numeric($mesero_id)) {
        $sql .= " AND o.mesero_id = ?";
        $params[] = $mesero_id;
    }
    
    $sql .= " 
        GROUP BY p.id, p.nombre, p.area
        ORDER BY cantidad_vendida DESC, ingresos_totales DESC
        LIMIT ?
    ";
    
    $params[] = (int)$limit;
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $productos = $stmt->fetchAll();
    
    // Obtener estadísticas adicionales
    $sql_stats = "
        SELECT 
            COUNT(DISTINCT p.id) as productos_vendidos,
            SUM(d.cantidad) as total_items_vendidos,
            SUM(d.subtotal) as ingresos_totales_productos
        FROM detalle_orden d
        INNER JOIN productos p ON d.producto_id = p.id
        INNER JOIN ordenes o ON d.orden_id = o.id
        WHERE o.fecha_hora >= ? AND o.fecha_hora <= ?
        AND o.estado = 'finalizada'
    ";
    
    $params_stats = [$fecha_inicio . ' 00:00:00', $fecha_fin . ' 23:59:59'];
    
    if (!empty($mesero_id) && is_numeric($mesero_id)) {
        $sql_stats .= " AND o.mesero_id = ?";
        $params_stats[] = $mesero_id;
    }
    
    $stmt = $pdo->prepare($sql_stats);
    $stmt->execute($params_stats);
    $estadisticas = $stmt->fetch();
    
    // Respuesta JSON
    echo json_encode([
        'success' => true,
        'productos' => $productos,
        'estadisticas' => [
            'productos_vendidos' => $estadisticas['productos_vendidos'] ?? 0,
            'total_items_vendidos' => $estadisticas['total_items_vendidos'] ?? 0,
            'ingresos_totales_productos' => $estadisticas['ingresos_totales_productos'] ?? 0,
            'fecha_inicio' => $fecha_inicio,
            'fecha_fin' => $fecha_fin
        ]
    ]);
    
} catch (PDOException $e) {
    error_log("Error en reportes_productos.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error al generar reporte de productos',
        'productos' => []
    ]);
}
?>
