<?php
// Configurar variables para el layout
$page_title = 'Gestión de Categorías';
$breadcrumbs = [
    ['title' => 'Inicio', 'url' => '/Restaurante/'],
    ['title' => 'Administración', 'url' => '/Restaurante/admin/'],
    ['title' => 'Categorías']
];

// Iniciar el buffer de contenido
ob_start();

// Conectar a la base de datos
session_start();
require_once '../config/db.php';

// Verificar que sea administrador
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    echo '<div class="alert alert-danger">Acceso denegado. Solo administradores pueden acceder.</div>';
    $content = ob_get_clean();
    include '../includes/layout.php';
    exit();
}

// Verificar si la tabla categorías existe
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'categorias'");
    $categorias_existe = $stmt->rowCount() > 0;
    
    if (!$categorias_existe) {
        echo '<div class="alert alert-warning">';
        echo '<h5><i class="bi bi-exclamation-triangle me-2"></i>Tabla de categorías no encontrada</h5>';
        echo '<p>Para usar esta funcionalidad, primero debes ejecutar el script de actualización.</p>';
        echo '<a href="/Restaurante/actualizar_bd_fotos_categorias.php" class="btn btn-primary">Ejecutar Actualización</a>';
        echo '</div>';
        $content = ob_get_clean();
        include '../includes/layout.php';
        exit();
    }
    
    // Obtener todas las categorías
    $stmt = $pdo->query("SELECT * FROM categorias ORDER BY nombre ASC");
    $categorias = $stmt->fetchAll();
    
} catch (PDOException $e) {
    echo '<div class="alert alert-danger">Error al cargar categorías: ' . htmlspecialchars($e->getMessage()) . '</div>';
    $categorias = [];
}

// Iconos disponibles para categorías
$iconos_disponibles = [
    'bi-egg-fried' => 'Comida',
    'bi-cup-straw' => 'Bebidas',
    'bi-cake2' => 'Postres',
    'bi-bowl' => 'Sopas',
    'bi-leaf' => 'Ensaladas',
    'bi-pizza' => 'Pizza',
    'bi-cup-hot' => 'Café',
    'bi-droplet' => 'Líquidos',
    'bi-tag' => 'General',
    'bi-star' => 'Especiales',
    'bi-heart' => 'Favoritos',
    'bi-fire' => 'Picante'
];

$colores_disponibles = [
    'primary' => 'Azul',
    'success' => 'Verde',
    'danger' => 'Rojo',
    'warning' => 'Amarillo',
    'info' => 'Cian',
    'secondary' => 'Gris',
    'dark' => 'Negro'
];
?>

<!-- Botón para nueva categoría -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">
                <i class="bi bi-tags me-2"></i>
                Gestión de Categorías
            </h4>
            <button class="btn btn-primary rounded-pill" data-bs-toggle="modal" data-bs-target="#modalNuevaCategoria">
                <i class="bi bi-plus-circle me-2"></i>
                Nueva Categoría
            </button>
        </div>
    </div>
</div>

<!-- Lista de categorías -->
<div class="row g-4">
    <?php if (empty($categorias)): ?>
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center p-5">
                    <i class="bi bi-tags text-muted fs-1 mb-3 d-block"></i>
                    <h5 class="text-muted">No hay categorías registradas</h5>
                    <p class="text-muted">Crea tu primera categoría para organizar los productos</p>
                    <button class="btn btn-primary rounded-pill" data-bs-toggle="modal" data-bs-target="#modalNuevaCategoria">
                        <i class="bi bi-plus-circle me-2"></i>
                        Crear Primera Categoría
                    </button>
                </div>
            </div>
        </div>
    <?php else: ?>
        <?php foreach ($categorias as $categoria): ?>
            <div class="col-lg-4 col-md-6">
                <div class="card border-0 shadow-sm h-100 categoria-card">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-start justify-content-between mb-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-<?= $categoria['color'] ?> bg-opacity-10 rounded-3 p-3 me-3">
                                    <i class="<?= $categoria['icono'] ?> text-<?= $categoria['color'] ?> fs-4"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1"><?= htmlspecialchars($categoria['nombre']) ?></h5>
                                    <small class="text-muted">
                                        <?php
                                        // Contar productos en esta categoría
                                        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM productos WHERE categoria_id = ?");
                                        $stmt->execute([$categoria['id']]);
                                        $total_productos = $stmt->fetch()['total'];
                                        echo $total_productos . ' producto' . ($total_productos != 1 ? 's' : '');
                                        ?>
                                    </small>
                                </div>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-sm rounded-pill" data-bs-toggle="dropdown">
                                    <i class="bi bi-three-dots"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item editar-categoria-btn" href="#" 
                                           data-id="<?= $categoria['id'] ?>"
                                           data-nombre="<?= htmlspecialchars($categoria['nombre']) ?>"
                                           data-descripcion="<?= htmlspecialchars($categoria['descripcion']) ?>"
                                           data-icono="<?= $categoria['icono'] ?>"
                                           data-color="<?= $categoria['color'] ?>">
                                            <i class="bi bi-pencil me-2"></i>Editar
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item toggle-categoria-btn" href="#" 
                                           data-id="<?= $categoria['id'] ?>"
                                           data-activo="<?= $categoria['activo'] ?>">
                                            <i class="bi bi-<?= $categoria['activo'] ? 'eye-slash' : 'eye' ?> me-2"></i>
                                            <?= $categoria['activo'] ? 'Desactivar' : 'Activar' ?>
                                        </a>
                                    </li>
                                    <?php if ($total_productos == 0): ?>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item text-danger eliminar-categoria-btn" href="#" 
                                               data-id="<?= $categoria['id'] ?>"
                                               data-nombre="<?= htmlspecialchars($categoria['nombre']) ?>">
                                                <i class="bi bi-trash me-2"></i>Eliminar
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                        
                        <?php if (!empty($categoria['descripcion'])): ?>
                            <p class="text-muted small mb-3"><?= htmlspecialchars($categoria['descripcion']) ?></p>
                        <?php endif; ?>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-<?= $categoria['color'] ?> rounded-pill">
                                <?= $categoria['activo'] ? 'Activa' : 'Inactiva' ?>
                            </span>
                            <small class="text-muted">
                                Creada: <?= date('d/m/Y', strtotime($categoria['fecha_creacion'])) ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>

<!-- Modal Nueva/Editar Categoría -->
<div class="modal fade" id="modalNuevaCategoria" tabindex="-1" aria-labelledby="modalNuevaCategoriaLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalNuevaCategoriaLabel">
                    <i class="bi bi-tag me-2"></i>
                    <span id="modal-title-text">Nueva Categoría</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="formCategoria">
                <div class="modal-body">
                    <input type="hidden" id="categoria_id" name="categoria_id">
                    
                    <div class="mb-3">
                        <label for="nombre" class="form-label">Nombre *</label>
                        <input type="text" class="form-control" id="nombre" name="nombre" required maxlength="100">
                    </div>
                    
                    <div class="mb-3">
                        <label for="descripcion" class="form-label">Descripción</label>
                        <textarea class="form-control" id="descripcion" name="descripcion" rows="3" maxlength="500"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="icono" class="form-label">Icono</label>
                                <select class="form-select" id="icono" name="icono">
                                    <?php foreach ($iconos_disponibles as $clase => $nombre): ?>
                                        <option value="<?= $clase ?>"><?= $nombre ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="color" class="form-label">Color</label>
                                <select class="form-select" id="color" name="color">
                                    <?php foreach ($colores_disponibles as $clase => $nombre): ?>
                                        <option value="<?= $clase ?>"><?= $nombre ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Vista previa -->
                    <div class="mb-3">
                        <label class="form-label">Vista Previa</label>
                        <div class="border rounded p-3 bg-light">
                            <div class="d-flex align-items-center">
                                <div id="preview-icon-container" class="bg-primary bg-opacity-10 rounded-3 p-3 me-3">
                                    <i id="preview-icon" class="bi-tag text-primary fs-4"></i>
                                </div>
                                <div>
                                    <h6 id="preview-nombre" class="mb-1">Nombre de la categoría</h6>
                                    <small class="text-muted">Vista previa de la categoría</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i>
                        <span id="btn-submit-text">Crear Categoría</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// CSS adicional
$extra_css = '
<style>
.categoria-card {
    transition: all 0.3s ease;
}

.categoria-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.dropdown-toggle::after {
    display: none;
}

#preview-icon-container {
    transition: all 0.3s ease;
}
</style>
';

// JavaScript adicional
$extra_js = '
<script>
document.addEventListener("DOMContentLoaded", function() {
    const formCategoria = document.getElementById("formCategoria");
    const modalCategoria = document.getElementById("modalNuevaCategoria");
    const modalTitle = document.getElementById("modal-title-text");
    const btnSubmitText = document.getElementById("btn-submit-text");
    
    // Vista previa en tiempo real
    const nombreInput = document.getElementById("nombre");
    const iconoSelect = document.getElementById("icono");
    const colorSelect = document.getElementById("color");
    const previewNombre = document.getElementById("preview-nombre");
    const previewIcon = document.getElementById("preview-icon");
    const previewIconContainer = document.getElementById("preview-icon-container");
    
    function actualizarVistaPrevia() {
        const nombre = nombreInput.value || "Nombre de la categoría";
        const icono = iconoSelect.value;
        const color = colorSelect.value;
        
        previewNombre.textContent = nombre;
        previewIcon.className = `${icono} text-${color} fs-4`;
        previewIconContainer.className = `bg-${color} bg-opacity-10 rounded-3 p-3 me-3`;
    }
    
    nombreInput.addEventListener("input", actualizarVistaPrevia);
    iconoSelect.addEventListener("change", actualizarVistaPrevia);
    colorSelect.addEventListener("change", actualizarVistaPrevia);
    
    // Limpiar modal al abrirlo para nueva categoría
    modalCategoria.addEventListener("show.bs.modal", function(event) {
        if (!event.relatedTarget || !event.relatedTarget.classList.contains("editar-categoria-btn")) {
            formCategoria.reset();
            document.getElementById("categoria_id").value = "";
            modalTitle.textContent = "Nueva Categoría";
            btnSubmitText.textContent = "Crear Categoría";
            actualizarVistaPrevia();
        }
    });
    
    // Editar categoría
    document.querySelectorAll(".editar-categoria-btn").forEach(btn => {
        btn.addEventListener("click", function(e) {
            e.preventDefault();
            
            document.getElementById("categoria_id").value = this.dataset.id;
            document.getElementById("nombre").value = this.dataset.nombre;
            document.getElementById("descripcion").value = this.dataset.descripcion;
            document.getElementById("icono").value = this.dataset.icono;
            document.getElementById("color").value = this.dataset.color;
            
            modalTitle.textContent = "Editar Categoría";
            btnSubmitText.textContent = "Guardar Cambios";
            
            actualizarVistaPrevia();
            
            new bootstrap.Modal(modalCategoria).show();
        });
    });
    
    // Enviar formulario
    formCategoria.addEventListener("submit", function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const categoriaId = document.getElementById("categoria_id").value;
        const url = categoriaId ? "/Restaurante/api/editar_categoria.php" : "/Restaurante/api/crear_categoria.php";
        
        fetch(url, {
            method: "POST",
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, "success");
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(data.message, "error");
            }
        })
        .catch(error => {
            console.error("Error:", error);
            showToast("Error de conexión", "error");
        });
    });
    
    // Toggle activo/inactivo
    document.querySelectorAll(".toggle-categoria-btn").forEach(btn => {
        btn.addEventListener("click", function(e) {
            e.preventDefault();
            
            const categoriaId = this.dataset.id;
            const activo = this.dataset.activo === "1";
            const accion = activo ? "desactivar" : "activar";
            
            fetch("/Restaurante/api/toggle_categoria.php", {
                    method: "POST",
                    headers: {"Content-Type": "application/json"},
                    body: JSON.stringify({categoria_id: categoriaId})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(data.message, "success");
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showToast(data.message, "error");
                    }
                });
        });
    });
    
    // Eliminar categoría
    document.querySelectorAll(".eliminar-categoria-btn").forEach(btn => {
        btn.addEventListener("click", function(e) {
            e.preventDefault();
            
            const categoriaId = this.dataset.id;
            const nombre = this.dataset.nombre;
            
            // Eliminar directamente sin confirmación para mejor flujo de trabajo
            fetch("/Restaurante/api/eliminar_categoria.php", {
                    method: "POST",
                    headers: {"Content-Type": "application/json"},
                    body: JSON.stringify({categoria_id: categoriaId})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(data.message, "success");
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showToast(data.message, "error");
                    }
                });
        });
    });
    
    function showToast(message, type = "info") {
        const toast = document.createElement("div");
        toast.className = `alert alert-${type === "success" ? "success" : "danger"} position-fixed`;
        toast.style.cssText = "top: 20px; right: 20px; z-index: 9999; min-width: 300px;";
        toast.innerHTML = `
            <i class="bi bi-${type === "success" ? "check-circle" : "exclamation-triangle"} me-2"></i>
            ${message}
        `;
        
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 3000);
    }
});
</script>
';

// Incluir el layout
include '../includes/layout.php';
?>
