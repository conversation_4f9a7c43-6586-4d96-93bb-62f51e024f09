# Configuración para documentación pública
# Permitir acceso sin autenticación a toda la carpeta docs

# Permitir acceso a todos los archivos
<Files "*">
    Order allow,deny
    Allow from all
</Files>

# Configurar página de inicio por defecto
DirectoryIndex index.php

# Configurar tipos MIME para archivos de documentación
<IfModule mod_mime.c>
    AddType text/html .php
    AddType text/markdown .md
</IfModule>

# Configurar headers de seguridad básicos
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Configurar compresión para mejor rendimiento
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript
</IfModule>

# Configurar cache para recursos estáticos
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>
