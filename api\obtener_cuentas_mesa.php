<?php
header('Content-Type: application/json');

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Verificar que sea un mesero o administrador
require_once '../config/db.php';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol IN ('mesero', 'admin', 'cocina', 'bebidas')");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Obtener hash de mesa
$hash_mesa = $_GET['hash_mesa'] ?? '';

if (empty($hash_mesa)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Hash de mesa requerido']);
    exit();
}

try {
    // Verificar que la mesa existe
    $stmt = $pdo->prepare("SELECT * FROM mesas WHERE hash_mesa = ?");
    $stmt->execute([$hash_mesa]);
    $mesa = $stmt->fetch();
    
    if (!$mesa) {
        echo json_encode(['success' => false, 'message' => 'Mesa no encontrada']);
        exit();
    }
    
    // Obtener cuentas de la mesa con estadísticas
    $stmt = $pdo->prepare("
        SELECT 
            c.*,
            u.nombre as mesero_nombre,
            COUNT(o.id) as total_ordenes,
            COALESCE(SUM(o.total), 0) as total_cuenta,
            COUNT(CASE WHEN o.estado = 'pendiente' THEN 1 END) as ordenes_pendientes,
            COUNT(CASE WHEN o.estado = 'en_proceso' THEN 1 END) as ordenes_proceso,
            COUNT(CASE WHEN o.estado = 'finalizada' THEN 1 END) as ordenes_finalizadas
        FROM cuentas c
        LEFT JOIN usuarios u ON c.mesero_id = u.id
        LEFT JOIN ordenes o ON c.id = o.cuenta_id
        WHERE c.hash_mesa = ? AND c.estado != 'pagada'
        GROUP BY c.id
        ORDER BY c.fecha_creacion ASC
    ");
    $stmt->execute([$hash_mesa]);
    $cuentas = $stmt->fetchAll();
    
    // Para cada cuenta, obtener el detalle de productos
    foreach ($cuentas as &$cuenta) {
        $stmt = $pdo->prepare("
            SELECT 
                d.*,
                p.nombre as producto_nombre,
                p.area as producto_area,
                o.fecha_hora as orden_fecha
            FROM detalle_orden d
            INNER JOIN ordenes o ON d.orden_id = o.id
            INNER JOIN productos p ON d.producto_id = p.id
            WHERE o.cuenta_id = ?
            ORDER BY o.fecha_hora DESC, d.id DESC
        ");
        $stmt->execute([$cuenta['id']]);
        $cuenta['productos'] = $stmt->fetchAll();
        
        // Calcular estadísticas de productos
        $cuenta['productos_pendientes'] = 0;
        $cuenta['productos_preparando'] = 0;
        $cuenta['productos_listos'] = 0;
        $cuenta['productos_servidos'] = 0;
        
        foreach ($cuenta['productos'] as $producto) {
            switch ($producto['estado']) {
                case 'pendiente':
                    $cuenta['productos_pendientes']++;
                    break;
                case 'preparando':
                    $cuenta['productos_preparando']++;
                    break;
                case 'listo':
                    $cuenta['productos_listos']++;
                    break;
                case 'servido':
                    $cuenta['productos_servidos']++;
                    break;
            }
        }
        
        // Actualizar total de la cuenta
        $total_real = 0;
        foreach ($cuenta['productos'] as $producto) {
            $total_real += $producto['subtotal'];
        }
        
        if ($total_real != $cuenta['total']) {
            $stmt = $pdo->prepare("UPDATE cuentas SET total = ? WHERE id = ?");
            $stmt->execute([$total_real, $cuenta['id']]);
            $cuenta['total'] = $total_real;
        }
    }
    
    echo json_encode([
        'success' => true,
        'mesa' => $mesa,
        'cuentas' => $cuentas,
        'total_cuentas' => count($cuentas)
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
