<?php
header('Content-Type: application/json');

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Verificar que sea administrador
require_once '../config/db.php';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden eliminar productos']);
    exit();
}

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);
$producto_id = $input['producto_id'] ?? null;

if (!$producto_id || !is_numeric($producto_id)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID de producto inválido']);
    exit();
}

try {
    // Verificar que el producto existe
    $stmt = $pdo->prepare("SELECT * FROM productos WHERE id = ?");
    $stmt->execute([$producto_id]);
    $producto = $stmt->fetch();
    
    if (!$producto) {
        echo json_encode(['success' => false, 'message' => 'Producto no encontrado']);
        exit();
    }
    
    // Verificar si el producto está siendo usado en órdenes activas
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as total 
        FROM detalle_orden d 
        INNER JOIN ordenes o ON d.orden_id = o.id 
        WHERE d.producto_id = ? AND o.estado != 'finalizada'
    ");
    $stmt->execute([$producto_id]);
    $ordenes_activas = $stmt->fetch()['total'];
    
    if ($ordenes_activas > 0) {
        echo json_encode([
            'success' => false, 
            'message' => 'No se puede eliminar el producto porque está siendo usado en órdenes activas'
        ]);
        exit();
    }
    
    // Iniciar transacción
    $pdo->beginTransaction();
    
    try {
        // Eliminar detalles de órdenes finalizadas (historial)
        $stmt = $pdo->prepare("
            DELETE d FROM detalle_orden d 
            INNER JOIN ordenes o ON d.orden_id = o.id 
            WHERE d.producto_id = ? AND o.estado = 'finalizada'
        ");
        $stmt->execute([$producto_id]);
        
        // Eliminar el producto
        $stmt = $pdo->prepare("DELETE FROM productos WHERE id = ?");
        $stmt->execute([$producto_id]);
        
        // Confirmar transacción
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Producto eliminado exitosamente',
            'producto_id' => $producto_id,
            'nombre' => $producto['nombre']
        ]);
        
    } catch (Exception $e) {
        // Revertir transacción en caso de error
        $pdo->rollBack();
        throw $e;
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
