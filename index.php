<?php
// Configurar variables para el layout
$page_title = 'Dashboard Principal';
$breadcrumbs = [
    ['title' => 'Inicio', 'url' => url('')],
    ['title' => 'Dashboard']
];

// Iniciar sesión y conectar a la base de datos
require_once 'config/session_config.php';
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once 'config/db.php';

// Verificar autenticación
if (!isset($_SESSION['usuario_id'])) {
    header('Location: auth/login.php');
    exit();
}

// Obtener datos del usuario actual
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ?");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario_actual = $stmt->fetch();

// Obtener estadísticas completas
try {
    // Estadísticas básicas
    $stmt = $pdo->query("SELECT COUNT(*) AS total FROM ordenes WHERE DATE(fecha_hora) = CURDATE()");
    $ordenes_hoy = $stmt->fetch()['total'];

    $stmt = $pdo->query("SELECT COALESCE(SUM(total), 0) AS total FROM ordenes WHERE DATE(fecha_hora) = CURDATE() AND estado = 'finalizada'");
    $ventas_hoy = $stmt->fetch()['total'];

    $stmt = $pdo->query("SELECT COUNT(*) AS ocupadas, (SELECT COUNT(*) FROM mesas) AS total FROM mesas WHERE estado = 'ocupada'");
    $mesas_stats = $stmt->fetch();
    $mesas_ocupadas = $mesas_stats['ocupadas'];
    $total_mesas = $mesas_stats['total'];

    $stmt = $pdo->query("SELECT COUNT(*) AS total FROM usuarios WHERE activo = 1");
    $empleados_activos = $stmt->fetch()['total'];

    $stmt = $pdo->query("SELECT COUNT(*) AS total FROM productos WHERE disponible = 1");
    $productos_disponibles = $stmt->fetch()['total'];

    // Órdenes pendientes (solo para admin)
    if ($usuario_actual['rol'] === 'admin') {
        $stmt = $pdo->query("
            SELECT o.*, u.nombre as mesero_nombre, m.numero_mesa
            FROM ordenes o
            LEFT JOIN usuarios u ON o.mesero_id = u.id
            LEFT JOIN mesas m ON o.mesa = m.numero_mesa
            WHERE o.estado IN ('pendiente', 'en_proceso')
            ORDER BY o.fecha_hora ASC
            LIMIT 5
        ");
        $ordenes_pendientes = $stmt->fetchAll();

        // Ventas de la semana
        $stmt = $pdo->query("
            SELECT DATE(fecha_hora) as fecha, COALESCE(SUM(total), 0) as total
            FROM ordenes
            WHERE fecha_hora >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
            AND estado = 'finalizada'
            GROUP BY DATE(fecha_hora)
            ORDER BY fecha ASC
        ");
        $ventas_semana = $stmt->fetchAll();

        // Top productos
        $stmt = $pdo->query("
            SELECT p.nombre, SUM(d.cantidad) as total_vendido, SUM(d.subtotal) as ingresos
            FROM detalle_orden d
            INNER JOIN productos p ON d.producto_id = p.id
            INNER JOIN ordenes o ON d.orden_id = o.id
            WHERE o.fecha_hora >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            AND o.estado = 'finalizada'
            GROUP BY p.id, p.nombre
            ORDER BY total_vendido DESC
            LIMIT 5
        ");
        $top_productos = $stmt->fetchAll();
    } else {
        $ordenes_pendientes = [];
        $ventas_semana = [];
        $top_productos = [];
    }

    // Obtener mesas activas con información completa
    $stmt = $pdo->query("
        SELECT m.numero_mesa, m.hash_mesa, m.fecha_apertura,
               COUNT(DISTINCT c.id) as total_cuentas,
               COALESCE(SUM(DISTINCT c.total), 0) as total_a_pagar,
               COUNT(CASE WHEN d.estado = 'pendiente' THEN 1 END) as productos_pendientes,
               COUNT(CASE WHEN d.estado = 'preparando' THEN 1 END) as productos_preparando,
               COUNT(CASE WHEN d.estado = 'listo' THEN 1 END) as productos_listos,
               COUNT(CASE WHEN d.estado = 'servido' THEN 1 END) as productos_servidos,
               MAX(o.fecha_hora) as ultima_orden,
               GROUP_CONCAT(DISTINCT CONCAT(c.nombre_cliente, ' ', COALESCE(c.apellido_cliente, '')) SEPARATOR ', ') as nombres_clientes
        FROM mesas m
        INNER JOIN cuentas c ON m.hash_mesa = c.hash_mesa AND c.estado = 'abierta'
        LEFT JOIN ordenes o ON c.id = o.cuenta_id
        LEFT JOIN detalle_orden d ON o.id = d.orden_id
        WHERE m.estado = 'ocupada'
        GROUP BY m.numero_mesa, m.hash_mesa, m.fecha_apertura
        ORDER BY m.numero_mesa ASC
    ");
    $mesas_activas = $stmt->fetchAll();

} catch (PDOException $e) {
    $ordenes_hoy = 0;
    $ventas_hoy = 0;
    $mesas_ocupadas = 0;
    $total_mesas = 0;
    $empleados_activos = 0;
    $productos_disponibles = 0;
    $ordenes_pendientes = [];
    $ventas_semana = [];
    $top_productos = [];
    $mesas_activas = [];
}

// Iniciar el buffer de contenido
ob_start();
?>

<!-- Dashboard Content -->
<div class="row g-4 mb-4">
    <!-- Estadísticas generales -->
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="bi bi-list-check text-primary fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h3 class="mb-1 fw-bold text-dark"><?= $ordenes_hoy ?></h3>
                        <p class="mb-0 text-muted small">Órdenes Hoy</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if ($usuario_actual['rol'] === 'admin'): ?>
    <!-- Ventas de hoy (solo admin) -->
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="bi bi-currency-dollar text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h3 class="mb-1 fw-bold text-dark">Q <?= number_format($ventas_hoy, 2) ?></h3>
                        <p class="mb-0 text-muted small">Ventas Hoy</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mesas ocupadas (solo admin) -->
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="bi bi-table text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h3 class="mb-1 fw-bold text-dark"><?= $mesas_ocupadas ?>/<?= $total_mesas ?></h3>
                        <p class="mb-0 text-muted small">Mesas Ocupadas</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Empleados activos (solo admin) -->
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-dark bg-opacity-10 rounded-3 p-3">
                            <i class="bi bi-people text-dark fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h3 class="mb-1 fw-bold text-dark"><?= $empleados_activos ?></h3>
                        <p class="mb-0 text-muted small">Empleados Activos</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Estadísticas para roles no-admin -->
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-dark bg-opacity-10 rounded-3 p-3">
                            <i class="bi bi-clock-fill text-dark fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <?php
                        // Filtrar por área si es cocina o bebidas
                        if ($usuario_actual['rol'] === 'cocina' || $usuario_actual['rol'] === 'bebidas') {
                            $area = $usuario_actual['rol'] === 'cocina' ? 'cocina' : 'bebidas';
                            $stmt = $pdo->prepare("
                                SELECT COUNT(DISTINCT o.id) AS total
                                FROM ordenes o
                                INNER JOIN detalle_orden d ON o.id = d.orden_id
                                WHERE d.estado = 'pendiente' AND d.area = ?
                            ");
                            $stmt->execute([$area]);
                        } else {
                            $stmt = $pdo->query("SELECT COUNT(*) AS total FROM ordenes WHERE estado = 'pendiente'");
                        }
                        $ordenes_pendientes_count = $stmt->fetch()['total'];
                        ?>
                        <h3 class="mb-1 fw-bold text-dark"><?= $ordenes_pendientes_count ?></h3>
                        <p class="mb-0 text-muted small">Pendientes</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="bi bi-gear-fill text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <?php
                        // Filtrar por área si es cocina o bebidas
                        if ($usuario_actual['rol'] === 'cocina' || $usuario_actual['rol'] === 'bebidas') {
                            $area = $usuario_actual['rol'] === 'cocina' ? 'cocina' : 'bebidas';
                            $stmt = $pdo->prepare("
                                SELECT COUNT(DISTINCT o.id) AS total
                                FROM ordenes o
                                INNER JOIN detalle_orden d ON o.id = d.orden_id
                                WHERE d.estado = 'preparando' AND d.area = ?
                            ");
                            $stmt->execute([$area]);
                        } else {
                            $stmt = $pdo->query("SELECT COUNT(*) AS total FROM ordenes WHERE estado = 'en_proceso'");
                        }
                        $ordenes_proceso_count = $stmt->fetch()['total'];
                        ?>
                        <h3 class="mb-1 fw-bold text-dark"><?= $ordenes_proceso_count ?></h3>
                        <p class="mb-0 text-muted small">En Proceso</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <?php if ($usuario_actual['rol'] === 'mesero'): ?>
                                <i class="bi bi-table text-success fs-4"></i>
                            <?php elseif ($usuario_actual['rol'] === 'cocina'): ?>
                                <i class="bi bi-check-circle text-success fs-4"></i>
                            <?php elseif ($usuario_actual['rol'] === 'bebidas'): ?>
                                <i class="bi bi-cup-straw text-success fs-4"></i>
                            <?php else: ?>
                                <i class="bi bi-check-circle text-success fs-4"></i>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <?php
                        // Estadística específica según el rol
                        if ($usuario_actual['rol'] === 'mesero') {
                            // Mesas asignadas al mesero
                            $stmt = $pdo->prepare("SELECT COUNT(*) AS total FROM mesas WHERE mesero_id = ? AND estado = 'ocupada'");
                            $stmt->execute([$usuario_actual['id']]);
                            $total_especifico = $stmt->fetch()['total'];
                            $etiqueta = "Mesas Asignadas";
                        } elseif ($usuario_actual['rol'] === 'cocina' || $usuario_actual['rol'] === 'bebidas') {
                            // Productos listos
                            $area = $usuario_actual['rol'] === 'cocina' ? 'cocina' : 'bebidas';
                            $stmt = $pdo->prepare("
                                SELECT COUNT(*) AS total
                                FROM detalle_orden
                                WHERE estado = 'listo' AND area = ?
                            ");
                            $stmt->execute([$area]);
                            $total_especifico = $stmt->fetch()['total'];
                            $etiqueta = "Productos Listos";
                        } else {
                            $total_especifico = 0;
                            $etiqueta = "Completados";
                        }
                        ?>
                        <h3 class="mb-1 fw-bold text-dark"><?= $total_especifico ?></h3>
                        <p class="mb-0 text-muted small"><?= $etiqueta ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Tabla de Mesas Activas -->
<?php if (!empty($mesas_activas)): ?>
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="bi bi-table me-2"></i>
                    Mesas con Órdenes Activas
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive mesas-activas-table">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th class="border-0 ps-4">Mesa</th>
                                <th class="border-0">Clientes</th>
                                <th class="border-0">Cuentas</th>
                                <th class="border-0">Total a Pagar</th>
                                <th class="border-0">Estado Productos</th>
                                <th class="border-0">Última Orden</th>
                                <th class="border-0 text-center">Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($mesas_activas as $mesa): ?>
                                <tr class="mesa-row">
                                    <td class="border-0 ps-4 py-3">
                                        <span class="fw-bold text-primary">Mesa #<?= $mesa['numero_mesa'] ?></span>
                                    </td>
                                    <td class="border-0 py-3">
                                        <?php if (!empty($mesa['nombres_clientes'])): ?>
                                            <span class="text-dark small fw-medium"><?= htmlspecialchars($mesa['nombres_clientes']) ?></span>
                                        <?php else: ?>
                                            <span class="text-muted small fst-italic">Sin nombres registrados</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="border-0 py-3">
                                        <span class="fw-medium"><?= $mesa['total_cuentas'] ?> cuenta<?= $mesa['total_cuentas'] != 1 ? 's' : '' ?></span>
                                    </td>
                                    <td class="border-0 py-3">
                                        <span class="fw-bold text-success">Q <?= number_format($mesa['total_a_pagar'], 2) ?></span>
                                    </td>
                                    <td class="border-0 py-3">
                                        <div class="d-flex flex-wrap gap-1">
                                            <?php if ($mesa['productos_pendientes'] > 0): ?>
                                                <span class="badge-estado badge-pendiente">
                                                    <i class="bi bi-clock"></i>
                                                    <?= $mesa['productos_pendientes'] ?> Pendiente<?= $mesa['productos_pendientes'] != 1 ? 's' : '' ?>
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($mesa['productos_preparando'] > 0): ?>
                                                <span class="badge-estado badge-preparando">
                                                    <i class="bi bi-gear"></i>
                                                    <?= $mesa['productos_preparando'] ?> Preparando
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($mesa['productos_listos'] > 0): ?>
                                                <span class="badge-estado badge-listo">
                                                    <i class="bi bi-check-circle"></i>
                                                    <?= $mesa['productos_listos'] ?> Listo<?= $mesa['productos_listos'] != 1 ? 's' : '' ?>
                                                </span>
                                            <?php endif; ?>
                                            <?php
                                            $total_productos = $mesa['productos_pendientes'] + $mesa['productos_preparando'] + $mesa['productos_listos'] + $mesa['productos_servidos'];
                                            if ($total_productos == 0): ?>
                                                <span class="badge-estado badge-sin-productos">
                                                    <i class="bi bi-info-circle"></i>
                                                    Sin productos
                                                </span>
                                            <?php elseif ($mesa['productos_pendientes'] == 0 && $mesa['productos_preparando'] == 0 && $mesa['productos_listos'] == 0 && $mesa['productos_servidos'] > 0): ?>
                                                <span class="badge-estado badge-sin-productos">
                                                    <i class="bi bi-check-circle-fill"></i>
                                                    Todo servido
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="border-0 py-3">
                                        <?php if ($mesa['ultima_orden']): ?>
                                            <?php
                                            $fecha_orden = new DateTime($mesa['ultima_orden']);
                                            $ahora = new DateTime();
                                            $diferencia = $ahora->diff($fecha_orden);

                                            if ($diferencia->days > 0) {
                                                $tiempo_texto = $fecha_orden->format('d/m/Y');
                                            } elseif ($diferencia->h > 0) {
                                                $tiempo_texto = 'Hace ' . $diferencia->h . 'h ' . $diferencia->i . 'm';
                                            } else {
                                                $tiempo_texto = 'Hace ' . $diferencia->i . ' min';
                                            }
                                            ?>
                                            <small class="text-muted"><?= $tiempo_texto ?></small>
                                        <?php else: ?>
                                            <small class="text-muted">Sin órdenes</small>
                                        <?php endif; ?>
                                    </td>
                                    <td class="border-0 py-3 text-center">
                                        <a href="<?= url('meseros/mesa_cuentas.php?hash=' . $mesa['hash_mesa']) ?>"
                                           class="btn btn-outline-primary btn-sm rounded-pill"
                                           title="Ver cuentas de la mesa">
                                            <i class="bi bi-eye me-1"></i>
                                            Ver Mesa
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Secciones adicionales para administradores -->
<?php if ($usuario_actual['rol'] === 'admin'): ?>
<div class="row g-4 mb-4">
    <!-- Gráfico de ventas de la semana -->
    <div class="col-md-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    Ventas de la Semana
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($ventas_semana)): ?>
                    <canvas id="ventasChart" height="100"></canvas>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-graph-up text-muted" style="font-size: 3rem;"></i>
                        <h6 class="text-muted mt-3">No hay datos de ventas</h6>
                        <p class="text-muted">Las ventas aparecerán aquí cuando se completen órdenes</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Top productos -->
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="bi bi-star me-2"></i>
                    Top Productos (7 días)
                </h5>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($top_productos)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($top_productos as $index => $producto): ?>
                            <div class="list-group-item border-0 py-3">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <span class="badge bg-primary rounded-circle" style="width: 30px; height: 30px; display: flex; align-items: center; justify-content: center;">
                                            <?= $index + 1 ?>
                                        </span>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1"><?= htmlspecialchars($producto['nombre']) ?></h6>
                                        <div class="d-flex justify-content-between">
                                            <small class="text-muted"><?= $producto['total_vendido'] ?> vendidos</small>
                                            <small class="text-success fw-semibold">Q <?= number_format($producto['ingresos'], 2) ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-star text-muted" style="font-size: 3rem;"></i>
                        <h6 class="text-muted mt-3">No hay productos vendidos</h6>
                        <p class="text-muted">Los productos más vendidos aparecerán aquí</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Órdenes pendientes para admin -->
<?php if (!empty($ordenes_pendientes)): ?>
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="bi bi-clock me-2"></i>
                    Órdenes Pendientes de Atención
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th class="border-0 ps-4">Orden</th>
                                <th class="border-0">Mesa</th>
                                <th class="border-0">Mesero</th>
                                <th class="border-0">Estado</th>
                                <th class="border-0">Tiempo</th>
                                <th class="border-0 text-center">Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($ordenes_pendientes as $orden): ?>
                                <tr>
                                    <td class="border-0 ps-4 py-3">
                                        <span class="fw-bold text-primary">#<?= $orden['id'] ?></span>
                                    </td>
                                    <td class="border-0 py-3">
                                        <span class="fw-medium">Mesa #<?= $orden['numero_mesa'] ?? $orden['mesa'] ?></span>
                                    </td>
                                    <td class="border-0 py-3">
                                        <span><?= htmlspecialchars($orden['mesero_nombre'] ?? 'N/A') ?></span>
                                    </td>
                                    <td class="border-0 py-3">
                                        <span class="badge bg-dark bg-opacity-10 text-dark rounded-pill">
                                            <?= ucfirst(str_replace('_', ' ', $orden['estado'])) ?>
                                        </span>
                                    </td>
                                    <td class="border-0 py-3">
                                        <small class="text-muted">
                                            <?= date('H:i', strtotime($orden['fecha_hora'])) ?>
                                        </small>
                                    </td>
                                    <td class="border-0 py-3 text-center">
                                        <a href="/Restaurante/meseros/orden_detalle.php?id=<?= $orden['id'] ?>&admin=1"
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Acciones rápidas para admin -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    Acciones Rápidas de Administración
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <a href="/Restaurante/admin/empleados.php" class="btn btn-primary w-100 rounded-pill">
                            <i class="bi bi-people me-2"></i>
                            Gestionar Empleados
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/Restaurante/admin/productos.php" class="btn btn-outline-success w-100 rounded-pill">
                            <i class="bi bi-box me-2"></i>
                            Gestionar Productos
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/Restaurante/admin/inventario.php" class="btn btn-outline-info w-100 rounded-pill">
                            <i class="bi bi-box-seam me-2"></i>
                            Gestionar Inventario
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/Restaurante/admin/configuracion.php" class="btn btn-outline-info w-100 rounded-pill">
                            <i class="bi bi-gear me-2"></i>
                            Configuración
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/Restaurante/admin/reportes.php" class="btn btn-outline-warning w-100 rounded-pill">
                            <i class="bi bi-graph-up me-2"></i>
                            Ver Reportes
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Contenido específico por rol -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-list-ul me-1"></i>
                    Órdenes Recientes
                </h3>
            </div>
            <div class="card-body">
                <?php
                try {
                    // Obtener órdenes recientes según el rol del usuario
                    if ($usuario_actual['rol'] === 'admin') {
                        $sql = "SELECT o.*, u.nombre as mesero_nombre
                                FROM ordenes o
                                LEFT JOIN usuarios u ON o.mesero_id = u.id
                                ORDER BY o.fecha_hora DESC LIMIT 10";
                        $stmt = $pdo->query($sql);
                    } elseif ($usuario_actual['rol'] === 'mesero') {
                        $sql = "SELECT * FROM ordenes WHERE mesero_id = ? ORDER BY fecha_hora DESC LIMIT 10";
                        $stmt = $pdo->prepare($sql);
                        $stmt->execute([$_SESSION['usuario_id']]);
                    } else {
                        // Para cocina y bebidas, mostrar órdenes relevantes
                        $area = ($usuario_actual['rol'] === 'cocina') ? 'cocina' : 'bebidas';
                        $sql = "SELECT DISTINCT o.*, u.nombre as mesero_nombre
                                FROM ordenes o
                                LEFT JOIN usuarios u ON o.mesero_id = u.id
                                INNER JOIN detalle_orden d ON o.id = d.orden_id
                                WHERE d.area = ?
                                ORDER BY o.fecha_hora DESC LIMIT 10";
                        $stmt = $pdo->prepare($sql);
                        $stmt->execute([$area]);
                    }

                    $ordenes = $stmt->fetchAll();

                    if (count($ordenes) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead class="table-light">
                                    <tr>
                                        <th class="border-0 fw-semibold text-muted small">ID</th>
                                        <th class="border-0 fw-semibold text-muted small">Mesa</th>
                                        <?php if ($usuario_actual['rol'] === 'admin'): ?>
                                            <th class="border-0 fw-semibold text-muted small">Mesero</th>
                                        <?php endif; ?>
                                        <th class="border-0 fw-semibold text-muted small">Estado</th>
                                        <th class="border-0 fw-semibold text-muted small">Fecha</th>
                                        <th class="border-0 fw-semibold text-muted small">Acciones</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($ordenes as $orden): ?>
                                        <tr>
                                            <td class="border-0 py-3">
                                                <span class="fw-semibold text-primary">#<?= $orden['id'] ?></span>
                                            </td>
                                            <td class="border-0 py-3">
                                                <span class="fw-medium">Mesa <?= $orden['mesa'] ?></span>
                                            </td>
                                            <?php if ($usuario_actual['rol'] === 'admin'): ?>
                                                <td class="border-0 py-3">
                                                    <span class="text-muted"><?= htmlspecialchars($orden['mesero_nombre'] ?? 'N/A') ?></span>
                                                </td>
                                            <?php endif; ?>
                                            <td class="border-0 py-3">
                                                <span class="estado-<?= $orden['estado'] ?>">
                                                    <i class="bi bi-circle-fill me-1" style="font-size: 0.5rem;"></i>
                                                    <?= ucfirst(str_replace('_', ' ', $orden['estado'])) ?>
                                                </span>
                                            </td>
                                            <td class="border-0 py-3">
                                                <span class="text-muted small"><?= date('d/m/Y H:i', strtotime($orden['fecha_hora'])) ?></span>
                                            </td>
                                            <td class="border-0 py-3">
                                                <?php if ($usuario_actual['rol'] === 'mesero'): ?>
                                                    <a href="meseros/orden_detalle.php?id=<?= $orden['id'] ?>" class="btn btn-sm btn-outline-primary rounded-pill" title="Ver detalles">
                                                        <i class="bi bi-eye me-1"></i>
                                                        Ver
                                                    </a>
                                                <?php elseif ($usuario_actual['rol'] === 'admin'): ?>
                                                    <a href="meseros/orden_detalle.php?id=<?= $orden['id'] ?>&admin=1" class="btn btn-sm btn-outline-primary rounded-pill" title="Ver detalles">
                                                        <i class="bi bi-eye me-1"></i>
                                                        Ver
                                                    </a>
                                                <?php elseif ($usuario_actual['rol'] === 'cocina'): ?>
                                                    <a href="meseros/orden_detalle.php?id=<?= $orden['id'] ?>&area=cocina" class="btn btn-sm btn-outline-danger rounded-pill" title="Ver productos de cocina">
                                                        <i class="bi bi-egg-fried me-1"></i>
                                                        Ver Cocina
                                                    </a>
                                                <?php elseif ($usuario_actual['rol'] === 'bebidas'): ?>
                                                    <a href="meseros/orden_detalle.php?id=<?= $orden['id'] ?>&area=bebidas" class="btn btn-sm btn-outline-info rounded-pill" title="Ver productos de bebidas">
                                                        <i class="bi bi-cup-straw me-1"></i>
                                                        Ver Bebidas
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted small">-</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">No hay órdenes recientes.</p>
                    <?php endif;
                } catch (PDOException $e) {
                    echo "<p class='text-danger'>Error al cargar órdenes: " . $e->getMessage() . "</p>";
                }
                ?>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="bi bi-info-circle me-1"></i>
                    Información del Sistema
                </h3>
            </div>
            <div class="card-body">
                <p><strong>Bienvenido:</strong> <?= htmlspecialchars($usuario_actual['nombre']) ?></p>
                <p><strong>Rol:</strong> <?= ucfirst($usuario_actual['rol']) ?></p>
                <p><strong>Último acceso:</strong> <?= date('d/m/Y H:i') ?></p>

                <hr>

                <h6 class="fw-semibold mb-3">Accesos Rápidos</h6>
                <div class="d-grid gap-2">
                    <?php if ($usuario_actual['rol'] === 'mesero'): ?>
                        <a href="/Restaurante/meseros/tomar_orden.php" class="btn btn-primary rounded-pill">
                            <i class="bi bi-plus-circle me-2"></i> Tomar Orden
                        </a>
                        <a href="/Restaurante/meseros/ver_ordenes.php" class="btn btn-outline-primary rounded-pill">
                            <i class="bi bi-list-ul me-2"></i> Ver Mis Órdenes
                        </a>
                    <?php elseif ($usuario_actual['rol'] === 'cocina'): ?>
                        <a href="/Restaurante/cocina/ordenes.php" class="btn btn-primary rounded-pill">
                            <i class="bi bi-fire me-2"></i> Órdenes de Cocina
                        </a>
                        <a href="/Restaurante/cocina/preparacion.php" class="btn btn-outline-primary rounded-pill">
                            <i class="bi bi-clock me-2"></i> En Preparación
                        </a>
                    <?php elseif ($usuario_actual['rol'] === 'bebidas'): ?>
                        <a href="/Restaurante/bebidas/ordenes.php" class="btn btn-primary rounded-pill">
                            <i class="bi bi-cup-straw me-2"></i> Órdenes de Bebidas
                        </a>
                        <a href="/Restaurante/bebidas/preparacion.php" class="btn btn-outline-primary rounded-pill">
                            <i class="bi bi-clock me-2"></i> En Preparación
                        </a>
                    <?php elseif ($usuario_actual['rol'] === 'admin'): ?>
                        <a href="/Restaurante/admin/empleados.php" class="btn btn-primary rounded-pill">
                            <i class="bi bi-people me-2"></i> Gestionar Empleados
                        </a>
                        <a href="/Restaurante/admin/reportes.php" class="btn btn-outline-primary rounded-pill">
                            <i class="bi bi-graph-up me-2"></i> Ver Reportes
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// JavaScript adicional para gráficos (solo admin)
$extra_js = '';
if ($usuario_actual['rol'] === 'admin' && !empty($ventas_semana)) {
    $fechas = [];
    $ventas = [];
    foreach ($ventas_semana as $venta) {
        $fechas[] = date('d/m', strtotime($venta['fecha']));
        $ventas[] = $venta['total'];
    }

    $extra_js = '
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
    document.addEventListener("DOMContentLoaded", function() {
        const ctx = document.getElementById("ventasChart");
        if (ctx) {
            new Chart(ctx, {
                type: "line",
                data: {
                    labels: ' . json_encode($fechas) . ',
                    datasets: [{
                        label: "Ventas (Q)",
                        data: ' . json_encode($ventas) . ',
                        borderColor: "rgb(75, 192, 192)",
                        backgroundColor: "rgba(75, 192, 192, 0.1)",
                        tension: 0.1,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return "Q " + value.toFixed(2);
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }
    });
    </script>';
}

// Incluir el layout
include 'includes/layout.php';
?>
