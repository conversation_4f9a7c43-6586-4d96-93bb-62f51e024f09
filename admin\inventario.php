<?php
// Configurar variables para el layout
$page_title = 'Gestión de Inventario';
$breadcrumbs = [
    ['title' => 'Inicio', 'url' => url('')],
    ['title' => 'Administración', 'url' => url('admin/')],
    ['title' => 'Inventario']
];

// Iniciar el buffer de contenido
ob_start();

// Conectar a la base de datos
require_once '../config/session_config.php';
session_start();
require_once '../config/db.php';

// Verificar que sea administrador
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    echo '<div class="alert alert-danger">Acceso denegado. Solo administradores pueden acceder.</div>';
    $content = ob_get_clean();
    include '../includes/layout.php';
    exit();
}

// Obtener estadísticas de inventario
try {
    // Total de productos en inventario
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM inventario");
    $total_productos = $stmt->fetch()['total'];
    
    // Productos con stock bajo
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM inventario WHERE stock_actual <= stock_minimo");
    $productos_stock_bajo = $stmt->fetch()['total'];
    
    // Valor total del inventario
    $stmt = $pdo->query("SELECT SUM(stock_actual * costo_promedio) as valor_total FROM inventario");
    $valor_inventario = $stmt->fetch()['valor_total'] ?? 0;
    
    // Productos sin stock
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM inventario WHERE stock_actual = 0");
    $productos_sin_stock = $stmt->fetch()['total'];
    
    // Obtener inventario completo con información de productos
    $stmt = $pdo->query("
        SELECT 
            i.*,
            p.nombre as producto_nombre,
            p.precio_venta,
            p.area,
            c.nombre as categoria_nombre
        FROM inventario i
        LEFT JOIN productos p ON i.producto_id = p.id
        LEFT JOIN categorias c ON p.categoria_id = c.id
        ORDER BY p.nombre
    ");
    $inventario = $stmt->fetchAll();
    
    // Obtener productos con stock bajo
    $stmt = $pdo->query("
        SELECT 
            i.*,
            p.nombre as producto_nombre,
            p.area,
            c.nombre as categoria_nombre
        FROM inventario i
        LEFT JOIN productos p ON i.producto_id = p.id
        LEFT JOIN categorias c ON p.categoria_id = c.id
        WHERE i.stock_actual <= i.stock_minimo
        ORDER BY (i.stock_actual / i.stock_minimo) ASC
    ");
    $stock_bajo = $stmt->fetchAll();

} catch (PDOException $e) {
    $total_productos = 0;
    $productos_stock_bajo = 0;
    $valor_inventario = 0;
    $productos_sin_stock = 0;
    $inventario = [];
    $stock_bajo = [];
}
?>

<!-- Estadísticas de Inventario -->
<div class="inventario-estadisticas-container mb-4">
    <div class="inventario-estadisticas-header">
        <div class="inventario-estadisticas-title">
            <span class="inventario-icon">📦</span>
            <h4>Gestión de Inventario</h4>
        </div>
        <div class="inventario-acciones">
            <a href="compras.php" class="btn-nueva-compra">
                🛒 Ver Compras
            </a>
            <button class="btn-ajuste-inventario" data-bs-toggle="modal" data-bs-target="#ajusteInventarioModal">
                ⚖️ Ajustar Stock
            </button>
        </div>
    </div>

    <div class="inventario-estadisticas-grid">
        <div class="stat-card stat-total">
            <div class="stat-icon">📦</div>
            <div class="stat-info">
                <span class="stat-numero"><?= $total_productos ?></span>
                <span class="stat-label">Productos en Inventario</span>
            </div>
        </div>

        <div class="stat-card stat-stock-bajo">
            <div class="stat-icon">⚠️</div>
            <div class="stat-info">
                <span class="stat-numero"><?= $productos_stock_bajo ?></span>
                <span class="stat-label">Stock Bajo</span>
            </div>
        </div>

        <div class="stat-card stat-sin-stock">
            <div class="stat-icon">❌</div>
            <div class="stat-info">
                <span class="stat-numero"><?= $productos_sin_stock ?></span>
                <span class="stat-label">Sin Stock</span>
            </div>
        </div>

        <div class="stat-card stat-valor">
            <div class="stat-icon">💰</div>
            <div class="stat-info">
                <span class="stat-numero">Q<?= number_format($valor_inventario, 2) ?></span>
                <span class="stat-label">Valor Total</span>
            </div>
        </div>
    </div>
</div>

<style>
/* Estilos para estadísticas de inventario */
.inventario-estadisticas-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.inventario-estadisticas-header {
    background: linear-gradient(135deg, #e8f4fd 0%, #c3e9ff 100%);
    padding: 20px;
    border-bottom: 2px solid #90caf9;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.inventario-estadisticas-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.inventario-icon {
    font-size: 24px;
}

.inventario-estadisticas-title h4 {
    margin: 0;
    color: #1976d2;
    font-weight: 600;
}

.inventario-acciones {
    display: flex;
    gap: 12px;
}

.btn-nueva-compra, .btn-ajuste-inventario {
    background: #2196f3;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    text-decoration: none;
    display: inline-block;
}

.btn-nueva-compra:hover, .btn-ajuste-inventario:hover {
    background: #1976d2;
    transform: translateY(-1px);
    color: white;
    text-decoration: none;
}

.btn-ajuste-inventario {
    background: #ff9800;
}

.btn-ajuste-inventario:hover {
    background: #f57c00;
}

.inventario-estadisticas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0;
}

.stat-card {
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    border-right: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.stat-card:last-child {
    border-right: none;
}

.stat-card:hover {
    background: #f8f9fa;
}

.stat-icon {
    font-size: 32px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.stat-total .stat-icon {
    background: #e3f2fd;
}

.stat-stock-bajo .stat-icon {
    background: #fff3e0;
}

.stat-sin-stock .stat-icon {
    background: #ffebee;
}

.stat-valor .stat-icon {
    background: #e8f5e8;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-numero {
    font-size: 24px;
    font-weight: 700;
    color: #212529;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
    margin-top: 4px;
}

@media (max-width: 768px) {
    .inventario-estadisticas-header {
        flex-direction: column;
        text-align: center;
    }
    
    .inventario-acciones {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-nueva-compra, .btn-ajuste-inventario {
        width: 100%;
    }

    .inventario-estadisticas-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .stat-card {
        border-right: none;
        border-bottom: 1px solid #e9ecef;
        justify-content: center;
        text-align: center;
    }

    .stat-card:nth-child(2n) {
        border-right: 1px solid #e9ecef;
    }
}
</style>

<!-- Alertas de Stock Bajo -->
<?php if (!empty($stock_bajo)): ?>
<div class="stock-bajo-container mb-4">
    <div class="stock-bajo-header">
        <div class="stock-bajo-title">
            <span class="alerta-icon">⚠️</span>
            <h5>Productos con Stock Bajo</h5>
        </div>
        <span class="badge bg-warning"><?= count($stock_bajo) ?> productos</span>
    </div>
    <div class="stock-bajo-content">
        <div class="stock-bajo-grid">
            <?php foreach ($stock_bajo as $producto): ?>
                <div class="stock-bajo-item">
                    <div class="producto-info">
                        <h6><?= htmlspecialchars($producto['producto_nombre']) ?></h6>
                        <small class="text-muted"><?= htmlspecialchars($producto['categoria_nombre'] ?? 'Sin categoría') ?></small>
                    </div>
                    <div class="stock-info">
                        <span class="stock-actual"><?= $producto['stock_actual'] ?></span>
                        <span class="stock-separador">/</span>
                        <span class="stock-minimo"><?= $producto['stock_minimo'] ?></span>
                        <small class="unidad"><?= $producto['unidad_medida'] ?></small>
                    </div>
                    <div class="area-badge">
                        <span class="badge bg-<?= $producto['area'] === 'cocina' ? 'danger' : 'info' ?>">
                            <?= ucfirst($producto['area']) ?>
                        </span>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Tabla de Inventario Completo -->
<div class="inventario-tabla-container">
    <div class="inventario-tabla-header">
        <div class="inventario-tabla-title">
            <span class="tabla-icon">📋</span>
            <h5>Inventario Completo</h5>
        </div>
        <div class="inventario-filtros">
            <select class="filtro-area" id="filtroArea">
                <option value="">Todas las áreas</option>
                <option value="cocina">🔥 Cocina</option>
                <option value="bebidas">🍹 Bebidas</option>
            </select>
            <select class="filtro-stock" id="filtroStock">
                <option value="">Todos los stocks</option>
                <option value="bajo">⚠️ Stock bajo</option>
                <option value="sin_stock">❌ Sin stock</option>
                <option value="normal">✅ Stock normal</option>
            </select>
        </div>
    </div>

    <div class="inventario-tabla-content">
        <?php if (empty($inventario)): ?>
            <div class="inventario-empty">
                <span class="empty-icon">📦</span>
                <h5>No hay productos en inventario</h5>
                <p>Agrega productos al sistema para comenzar a gestionar el inventario</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Producto</th>
                            <th>Categoría</th>
                            <th>Área</th>
                            <th>Stock Actual</th>
                            <th>Stock Mínimo</th>
                            <th>Unidad</th>
                            <th>Costo Promedio</th>
                            <th>Valor Total</th>
                            <th>Estado</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($inventario as $item): ?>
                            <?php
                            $valor_total = $item['stock_actual'] * $item['costo_promedio'];
                            $estado_stock = '';
                            $clase_estado = '';

                            if ($item['stock_actual'] == 0) {
                                $estado_stock = 'Sin stock';
                                $clase_estado = 'danger';
                            } elseif ($item['stock_actual'] <= $item['stock_minimo']) {
                                $estado_stock = 'Stock bajo';
                                $clase_estado = 'warning';
                            } else {
                                $estado_stock = 'Normal';
                                $clase_estado = 'success';
                            }
                            ?>
                            <tr data-area="<?= $item['area'] ?>" data-stock-estado="<?= $estado_stock ?>">
                                <td>
                                    <strong><?= htmlspecialchars($item['producto_nombre']) ?></strong>
                                </td>
                                <td><?= htmlspecialchars($item['categoria_nombre'] ?? 'Sin categoría') ?></td>
                                <td>
                                    <span class="badge bg-<?= $item['area'] === 'cocina' ? 'danger' : 'info' ?>">
                                        <?= $item['area'] === 'cocina' ? '🔥' : '🍹' ?> <?= ucfirst($item['area']) ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="stock-numero"><?= $item['stock_actual'] ?></span>
                                </td>
                                <td>
                                    <span class="stock-minimo-numero"><?= $item['stock_minimo'] ?></span>
                                </td>
                                <td><?= ucfirst($item['unidad_medida']) ?></td>
                                <td>Q<?= number_format($item['costo_promedio'], 2) ?></td>
                                <td>Q<?= number_format($valor_total, 2) ?></td>
                                <td>
                                    <span class="badge bg-<?= $clase_estado ?>"><?= $estado_stock ?></span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary btn-sm ajustar-stock-btn"
                                                data-id="<?= $item['producto_id'] ?>"
                                                data-nombre="<?= htmlspecialchars($item['producto_nombre']) ?>"
                                                data-stock="<?= $item['stock_actual'] ?>"
                                                title="Ajustar stock">
                                            ⚖️
                                        </button>
                                        <!-- Botón de movimientos removido temporalmente -->
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>



<!-- Modal Ajustar Stock -->
<div class="modal fade" id="ajusteInventarioModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">⚖️ Ajustar Stock de Inventario</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="formAjusteStock">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="producto_ajuste" class="form-label">📦 Producto *</label>
                        <select class="form-select" id="producto_ajuste" name="producto_id" required>
                            <option value="">Seleccionar producto...</option>
                            <?php
                            try {
                                $stmt = $pdo->query("
                                    SELECT p.id, p.nombre, i.stock_actual, i.unidad_medida
                                    FROM productos p
                                    LEFT JOIN inventario i ON p.id = i.producto_id
                                    ORDER BY p.nombre
                                ");
                                while ($producto = $stmt->fetch()) {
                                    echo "<option value='{$producto['id']}' data-stock='{$producto['stock_actual']}' data-unidad='{$producto['unidad_medida']}'>";
                                    echo "{$producto['nombre']} (Stock: {$producto['stock_actual']} {$producto['unidad_medida']})";
                                    echo "</option>";
                                }
                            } catch (PDOException $e) {
                                echo "<option value=''>Error al cargar productos</option>";
                            }
                            ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">📊 Stock Actual</label>
                        <input type="text" class="form-control" id="stock-actual-display" readonly>
                    </div>

                    <div class="mb-3">
                        <label for="tipo_ajuste" class="form-label">🔄 Tipo de Ajuste *</label>
                        <select class="form-select" id="tipo_ajuste" name="tipo_ajuste" required>
                            <option value="">Seleccionar tipo...</option>
                            <option value="entrada">➕ Entrada (Aumentar stock)</option>
                            <option value="salida">➖ Salida (Reducir stock)</option>
                            <option value="ajuste">⚖️ Ajuste (Establecer cantidad exacta)</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="cantidad_ajuste" class="form-label">🔢 Cantidad *</label>
                        <input type="number" class="form-control" id="cantidad_ajuste" name="cantidad"
                               min="1" required>
                        <div class="form-text" id="ajuste-help"></div>
                    </div>

                    <div class="mb-3">
                        <label for="motivo_ajuste" class="form-label">📝 Motivo del Ajuste *</label>
                        <select class="form-select" id="motivo_ajuste" name="motivo" required>
                            <option value="">Seleccionar motivo...</option>
                            <option value="Corrección de inventario">Corrección de inventario</option>
                            <option value="Producto dañado">Producto dañado</option>
                            <option value="Producto vencido">Producto vencido</option>
                            <option value="Merma">Merma</option>
                            <option value="Robo/Pérdida">Robo/Pérdida</option>
                            <option value="Donación">Donación</option>
                            <option value="Otro">Otro</option>
                        </select>
                    </div>

                    <div class="mb-3" id="motivo-otro-container" style="display: none;">
                        <label for="motivo_otro" class="form-label">📝 Especificar Motivo</label>
                        <input type="text" class="form-control" id="motivo_otro" name="motivo_otro"
                               placeholder="Describir el motivo del ajuste">
                    </div>

                    <div class="alert alert-info">
                        <strong>ℹ️ Información:</strong>
                        <div id="preview-ajuste"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-warning">⚖️ Realizar Ajuste</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* Estilos para alertas de stock bajo */
.stock-bajo-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #ff9800;
    overflow: hidden;
}

.stock-bajo-header {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    padding: 16px 20px;
    border-bottom: 1px solid #ffcc02;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stock-bajo-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.alerta-icon {
    font-size: 20px;
}

.stock-bajo-title h5 {
    margin: 0;
    color: #f57c00;
    font-weight: 600;
}

.stock-bajo-content {
    padding: 20px;
}

.stock-bajo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
}

.stock-bajo-item {
    background: #fafafa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.stock-bajo-item:hover {
    border-color: #ff9800;
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.15);
}

.producto-info h6 {
    margin: 0 0 4px 0;
    color: #212529;
    font-weight: 600;
}

.stock-info {
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 600;
}

.stock-actual {
    color: #f44336;
    font-size: 18px;
}

.stock-separador {
    color: #9e9e9e;
}

.stock-minimo {
    color: #ff9800;
}

.unidad {
    color: #6c757d;
    margin-left: 4px;
}

/* Estilos para tabla de inventario */
.inventario-tabla-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.inventario-tabla-header {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    padding: 20px;
    border-bottom: 2px solid #ce93d8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.inventario-tabla-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.tabla-icon {
    font-size: 20px;
}

.inventario-tabla-title h5 {
    margin: 0;
    color: #7b1fa2;
    font-weight: 600;
}

.inventario-filtros {
    display: flex;
    gap: 12px;
}

.filtro-area, .filtro-stock {
    padding: 8px 16px;
    border: 2px solid #ce93d8;
    border-radius: 20px;
    background: white;
    color: #7b1fa2;
    font-weight: 500;
    cursor: pointer;
}

.inventario-tabla-content {
    padding: 20px;
}

.inventario-empty {
    text-align: center;
    padding: 40px 20px;
}

.empty-icon {
    font-size: 48px;
    display: block;
    margin-bottom: 16px;
}

.inventario-empty h5 {
    color: #6c757d;
    margin: 0 0 8px 0;
}

.inventario-empty p {
    color: #6c757d;
    margin-bottom: 20px;
}

.table th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.table td {
    vertical-align: middle;
    font-size: 14px;
}

.stock-numero {
    font-weight: 600;
    font-size: 16px;
}

.stock-minimo-numero {
    color: #6c757d;
}

@media (max-width: 768px) {
    .inventario-tabla-header {
        flex-direction: column;
        text-align: center;
    }

    .inventario-filtros {
        flex-direction: column;
        width: 100%;
    }

    .filtro-area, .filtro-stock {
        width: 100%;
    }

    .stock-bajo-grid {
        grid-template-columns: 1fr;
    }

    .stock-bajo-item {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }
}
</style>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// JavaScript adicional
$extra_js = '
<script>
document.addEventListener("DOMContentLoaded", function() {
    // Filtros
    const filtroArea = document.getElementById("filtroArea");
    const filtroStock = document.getElementById("filtroStock");

    if (filtroArea) {
        filtroArea.addEventListener("change", aplicarFiltros);
    }
    if (filtroStock) {
        filtroStock.addEventListener("change", aplicarFiltros);
    }

    function aplicarFiltros() {
        const areaSeleccionada = filtroArea ? filtroArea.value : "";
        const stockSeleccionado = filtroStock ? filtroStock.value : "";

        const filas = document.querySelectorAll("tbody tr");

        filas.forEach(fila => {
            let mostrar = true;

            // Filtro por área
            if (areaSeleccionada && fila.dataset.area !== areaSeleccionada) {
                mostrar = false;
            }

            // Filtro por estado de stock
            if (stockSeleccionado) {
                const estadoStock = fila.dataset.stockEstado;
                if (stockSeleccionado === "bajo" && estadoStock !== "Stock bajo") {
                    mostrar = false;
                } else if (stockSeleccionado === "sin_stock" && estadoStock !== "Sin stock") {
                    mostrar = false;
                } else if (stockSeleccionado === "normal" && estadoStock !== "Normal") {
                    mostrar = false;
                }
            }

            fila.style.display = mostrar ? "" : "none";
        });
    }



    // ===== MODAL AJUSTAR STOCK =====
    const productoAjusteSelect = document.getElementById("producto_ajuste");
    const stockActualDisplay = document.getElementById("stock-actual-display");
    const tipoAjusteSelect = document.getElementById("tipo_ajuste");
    const cantidadAjusteInput = document.getElementById("cantidad_ajuste");
    const motivoAjusteSelect = document.getElementById("motivo_ajuste");
    const motivoOtroContainer = document.getElementById("motivo-otro-container");
    const previewAjuste = document.getElementById("preview-ajuste");
    const ajusteHelp = document.getElementById("ajuste-help");

    // Cuando se selecciona un producto
    productoAjusteSelect.addEventListener("change", function() {
        const option = this.selectedOptions[0];
        if (option && option.value) {
            const stock = option.dataset.stock;
            const unidad = option.dataset.unidad;
            stockActualDisplay.value = `${stock} ${unidad}`;
            actualizarPreview();
        } else {
            stockActualDisplay.value = "";
            previewAjuste.innerHTML = "";
        }
    });

    // Cuando cambia el tipo de ajuste
    tipoAjusteSelect.addEventListener("change", function() {
        const tipo = this.value;
        cantidadAjusteInput.value = "";

        if (tipo === "entrada") {
            ajusteHelp.textContent = "Cantidad a agregar al stock actual";
            cantidadAjusteInput.placeholder = "Ej: 10";
        } else if (tipo === "salida") {
            ajusteHelp.textContent = "Cantidad a reducir del stock actual";
            cantidadAjusteInput.placeholder = "Ej: 5";
        } else if (tipo === "ajuste") {
            ajusteHelp.textContent = "Cantidad exacta que debe quedar en stock";
            cantidadAjusteInput.placeholder = "Ej: 25";
        }

        actualizarPreview();
    });

    // Cuando cambia la cantidad
    cantidadAjusteInput.addEventListener("input", actualizarPreview);

    // Cuando cambia el motivo
    motivoAjusteSelect.addEventListener("change", function() {
        if (this.value === "Otro") {
            motivoOtroContainer.style.display = "block";
            document.getElementById("motivo_otro").required = true;
        } else {
            motivoOtroContainer.style.display = "none";
            document.getElementById("motivo_otro").required = false;
        }
    });

    function actualizarPreview() {
        const productoOption = productoAjusteSelect.selectedOptions[0];
        const tipo = tipoAjusteSelect.value;
        const cantidad = parseInt(cantidadAjusteInput.value) || 0;

        if (!productoOption || !tipo || !cantidad) {
            previewAjuste.innerHTML = "Selecciona todos los campos para ver el preview";
            return;
        }

        const stockActual = parseInt(productoOption.dataset.stock) || 0;
        const unidad = productoOption.dataset.unidad;
        let stockFinal;

        if (tipo === "entrada") {
            stockFinal = stockActual + cantidad;
        } else if (tipo === "salida") {
            stockFinal = stockActual - cantidad;
        } else if (tipo === "ajuste") {
            stockFinal = cantidad;
        }

        if (stockFinal < 0) {
            previewAjuste.innerHTML = `<span class="text-danger">⚠️ Error: El stock no puede ser negativo</span>`;
        } else {
            previewAjuste.innerHTML = `Stock actual: ${stockActual} ${unidad} → Stock final: ${stockFinal} ${unidad}`;
        }
    }

    // Enviar formulario de ajuste de stock
    document.getElementById("formAjusteStock").addEventListener("submit", function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        fetch("api/procesar_ajuste_stock.php", {
            method: "POST",
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                mostrarToast("✅ Stock ajustado exitosamente", "success");
                bootstrap.Modal.getInstance(document.getElementById("ajusteInventarioModal")).hide();
                location.reload();
            } else {
                mostrarToast("❌ Error: " + data.message, "error");
            }
        })
        .catch(error => {
            mostrarToast("❌ Error al ajustar el stock", "error");
            console.error("Error:", error);
        });
    });

    // Botones de ajustar stock en la tabla
    document.querySelectorAll(".ajustar-stock-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const id = this.dataset.id;
            const nombre = this.dataset.nombre;
            const stockActual = this.dataset.stock;

            // Pre-seleccionar el producto en el modal
            productoAjusteSelect.value = id;
            productoAjusteSelect.dispatchEvent(new Event("change"));

            // Mostrar el modal
            const modal = new bootstrap.Modal(document.getElementById("ajusteInventarioModal"));
            modal.show();
        });
    });

    // Funcionalidad de movimientos removida temporalmente

    // Función para mostrar toasts
    function mostrarToast(mensaje, tipo) {
        // Crear el toast
        const toastContainer = document.querySelector(".toast-container") || createToastContainer();

        const toastId = "toast-" + Date.now();
        const bgClass = tipo === "success" ? "bg-success" : tipo === "error" ? "bg-danger" : "bg-info";

        const toastHTML = `
            <div id="${toastId}" class="toast ${bgClass} text-white" role="alert">
                <div class="toast-body">
                    ${mensaje}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML("beforeend", toastHTML);

        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
        toast.show();

        // Eliminar el toast después de que se oculte
        toastElement.addEventListener("hidden.bs.toast", function() {
            this.remove();
        });
    }

    function createToastContainer() {
        const container = document.createElement("div");
        container.className = "toast-container position-fixed top-0 end-0 p-3";
        container.style.zIndex = "9999";
        document.body.appendChild(container);
        return container;
    }

    // Funciones de movimientos removidas
});
</script>
';

// Incluir el layout
include '../includes/layout.php';
?>
