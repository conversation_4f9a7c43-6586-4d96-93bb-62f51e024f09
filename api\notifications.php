<?php
header('Content-Type: application/json');

// Iniciar sesión y conectar a la base de datos
session_start();
require_once '../config/db.php';

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Obtener información del usuario
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ?");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Usuario no válido']);
    exit();
}

$notifications = [];
$total_count = 0;

try {
    // Notificaciones según el rol del usuario
    switch ($usuario['rol']) {
        case 'admin':
            // Notificaciones para administradores

            // 1. Órdenes pendientes críticas (más de 20 minutos)
            $stmt = $pdo->query("
                SELECT COUNT(*) as count
                FROM detalle_orden d
                INNER JOIN ordenes o ON d.orden_id = o.id
                WHERE d.estado IN ('pendiente', 'preparando')
                AND TIMESTAMPDIFF(MINUTE, d.fecha_creacion, NOW()) > 20
            ");
            $ordenes_criticas = $stmt->fetch()['count'];

            if ($ordenes_criticas > 0) {
                $notifications[] = [
                    'id' => 'ordenes_criticas',
                    'type' => 'danger',
                    'title' => 'Órdenes Críticas',
                    'message' => "$ordenes_criticas órdenes llevan más de 20 minutos pendientes",
                    'icon' => 'bi-exclamation-triangle',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'priority' => 'high'
                ];
            }

            // 2. Mesas ocupadas sin órdenes recientes
            $stmt = $pdo->query("
                SELECT m.numero_mesa, m.hash_mesa, u.nombre as mesero_nombre,
                       TIMESTAMPDIFF(MINUTE, m.fecha_apertura, NOW()) as tiempo_ocupada
                FROM mesas m
                LEFT JOIN usuarios u ON m.mesero_id = u.id
                LEFT JOIN ordenes o ON m.hash_mesa = o.hash_mesa AND o.fecha_hora > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
                WHERE m.estado = 'ocupada'
                AND o.id IS NULL
                AND TIMESTAMPDIFF(MINUTE, m.fecha_apertura, NOW()) > 30
            ");
            $mesas_inactivas = $stmt->fetchAll();

            if (!empty($mesas_inactivas)) {
                $notifications[] = [
                    'id' => 'mesas_inactivas',
                    'type' => 'info',
                    'title' => 'Mesas Inactivas',
                    'message' => count($mesas_inactivas) . " mesas ocupadas sin órdenes recientes",
                    'icon' => 'bi-table',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'priority' => 'medium',
                    'details' => $mesas_inactivas
                ];
            }

            // 3. Ventas del día vs objetivo
            $stmt = $pdo->query("
                SELECT COALESCE(SUM(total), 0) as ventas_hoy
                FROM ordenes
                WHERE DATE(fecha_hora) = CURDATE() AND estado = 'finalizada'
            ");
            $ventas_hoy = $stmt->fetch()['ventas_hoy'];
            $objetivo_diario = 500000; // $500,000 objetivo diario

            if ($ventas_hoy < $objetivo_diario * 0.5 && date('H') > 18) {
                $notifications[] = [
                    'id' => 'ventas_bajas',
                    'type' => 'info',
                    'title' => 'Ventas Bajas',
                    'message' => "Ventas del día: $" . number_format($ventas_hoy, 0) . " (Meta: $" . number_format($objetivo_diario, 0) . ")",
                    'icon' => 'bi-graph-down',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'priority' => 'medium'
                ];
            }

            break;

        case 'mesero':
            // Notificaciones para meseros

            // 1. Órdenes listas para servir en sus mesas
            $stmt = $pdo->prepare("
                SELECT d.id, p.nombre as producto_nombre, m.numero_mesa,
                       TIMESTAMPDIFF(MINUTE, d.fecha_actualizacion, NOW()) as tiempo_listo
                FROM detalle_orden d
                INNER JOIN ordenes o ON d.orden_id = o.id
                INNER JOIN productos p ON d.producto_id = p.id
                INNER JOIN mesas m ON o.hash_mesa = m.hash_mesa
                WHERE d.estado = 'listo'
                AND m.mesero_id = ?
                ORDER BY d.fecha_actualizacion ASC
            ");
            $stmt->execute([$usuario['id']]);
            $ordenes_listas = $stmt->fetchAll();

            if (!empty($ordenes_listas)) {
                $notifications[] = [
                    'id' => 'ordenes_listas',
                    'type' => 'success',
                    'title' => 'Órdenes Listas',
                    'message' => count($ordenes_listas) . " productos listos para servir",
                    'icon' => 'bi-check-circle',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'priority' => 'high',
                    'details' => $ordenes_listas
                ];
            }

            // 2. Mesas asignadas que necesitan atención
            $stmt = $pdo->prepare("
                SELECT m.numero_mesa, m.hash_mesa,
                       TIMESTAMPDIFF(MINUTE, m.fecha_apertura, NOW()) as tiempo_ocupada,
                       COUNT(c.id) as total_cuentas
                FROM mesas m
                LEFT JOIN cuentas c ON m.hash_mesa = c.hash_mesa AND c.estado = 'abierta'
                WHERE m.mesero_id = ? AND m.estado = 'ocupada'
                GROUP BY m.id
                HAVING total_cuentas = 0 OR tiempo_ocupada > 45
            ");
            $stmt->execute([$usuario['id']]);
            $mesas_atencion = $stmt->fetchAll();

            if (!empty($mesas_atencion)) {
                $notifications[] = [
                    'id' => 'mesas_atencion',
                    'type' => 'info',
                    'title' => 'Mesas Requieren Atención',
                    'message' => count($mesas_atencion) . " mesas necesitan atención",
                    'icon' => 'bi-exclamation-circle',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'priority' => 'medium',
                    'details' => $mesas_atencion
                ];
            }

            break;

        case 'cocina':
            // Notificaciones para cocina

            // 1. Órdenes pendientes de cocina
            $stmt = $pdo->query("
                SELECT COUNT(*) as pendientes,
                       COUNT(CASE WHEN TIMESTAMPDIFF(MINUTE, fecha_creacion, NOW()) > 15 THEN 1 END) as criticas
                FROM detalle_orden
                WHERE area = 'cocina' AND estado = 'pendiente'
            ");
            $stats_cocina = $stmt->fetch();

            if ($stats_cocina['pendientes'] > 0) {
                $type = $stats_cocina['criticas'] > 0 ? 'danger' : 'info';
                $notifications[] = [
                    'id' => 'ordenes_cocina',
                    'type' => $type,
                    'title' => 'Órdenes de Cocina',
                    'message' => $stats_cocina['pendientes'] . " órdenes pendientes" .
                               ($stats_cocina['criticas'] > 0 ? " ({$stats_cocina['criticas']} críticas)" : ""),
                    'icon' => 'bi-fire',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'priority' => $stats_cocina['criticas'] > 0 ? 'high' : 'medium'
                ];
            }

            // 2. Órdenes en preparación hace mucho tiempo
            $stmt = $pdo->query("
                SELECT COUNT(*) as count
                FROM detalle_orden
                WHERE area = 'cocina' AND estado = 'preparando'
                AND TIMESTAMPDIFF(MINUTE, fecha_actualizacion, NOW()) > 20
            ");
            $preparando_criticas = $stmt->fetch()['count'];

            if ($preparando_criticas > 0) {
                $notifications[] = [
                    'id' => 'preparando_criticas',
                    'type' => 'danger',
                    'title' => 'Preparación Crítica',
                    'message' => "$preparando_criticas platos llevan más de 20 minutos en preparación",
                    'icon' => 'bi-clock',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'priority' => 'high'
                ];
            }

            break;

        case 'bebidas':
            // Notificaciones para bebidas

            // 1. Órdenes pendientes de bebidas
            $stmt = $pdo->query("
                SELECT COUNT(*) as pendientes,
                       COUNT(CASE WHEN TIMESTAMPDIFF(MINUTE, fecha_creacion, NOW()) > 8 THEN 1 END) as criticas
                FROM detalle_orden
                WHERE area = 'bebidas' AND estado = 'pendiente'
            ");
            $stats_bebidas = $stmt->fetch();

            if ($stats_bebidas['pendientes'] > 0) {
                $type = $stats_bebidas['criticas'] > 0 ? 'danger' : 'info';
                $notifications[] = [
                    'id' => 'ordenes_bebidas',
                    'type' => $type,
                    'title' => 'Órdenes de Bebidas',
                    'message' => $stats_bebidas['pendientes'] . " bebidas pendientes" .
                               ($stats_bebidas['criticas'] > 0 ? " ({$stats_bebidas['criticas']} críticas)" : ""),
                    'icon' => 'bi-cup-straw',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'priority' => $stats_bebidas['criticas'] > 0 ? 'high' : 'medium'
                ];
            }

            // 2. Bebidas en preparación hace mucho tiempo
            $stmt = $pdo->query("
                SELECT COUNT(*) as count
                FROM detalle_orden
                WHERE area = 'bebidas' AND estado = 'preparando'
                AND TIMESTAMPDIFF(MINUTE, fecha_actualizacion, NOW()) > 10
            ");
            $bebidas_criticas = $stmt->fetch()['count'];

            if ($bebidas_criticas > 0) {
                $notifications[] = [
                    'id' => 'bebidas_criticas',
                    'type' => 'danger',
                    'title' => 'Preparación Crítica',
                    'message' => "$bebidas_criticas bebidas llevan más de 10 minutos en preparación",
                    'icon' => 'bi-clock',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'priority' => 'high'
                ];
            }

            break;
    }

    $total_count = count($notifications);

    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'notifications' => $notifications,
        'count' => $total_count,
        'user_role' => $usuario['rol'],
        'timestamp' => date('Y-m-d H:i:s')
    ]);

} catch (PDOException $e) {
    error_log("Error en notifications.php: " . $e->getMessage());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error al obtener notificaciones',
        'notifications' => [],
        'count' => 0
    ]);
}
?>
