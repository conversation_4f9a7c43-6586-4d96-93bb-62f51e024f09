<?php
header('Content-Type: application/json');

// Iniciar sesión y conectar a la base de datos
session_start();
require_once '../config/db.php';

// Verificar que el usuario esté logueado y sea administrador
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden acceder a reportes']);
    exit();
}

// Obtener parámetros
$fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-m-d', strtotime('-7 days'));
$fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-d');

// Validar fechas
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_inicio) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_fin)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Formato de fecha inválido']);
    exit();
}

try {
    // Tiempo promedio de preparación por área
    $sql_tiempos = "
        SELECT d.area,
               AVG(TIMESTAMPDIFF(MINUTE, d.fecha_creacion, 
                   CASE 
                       WHEN d.estado = 'servido' THEN d.fecha_actualizacion
                       ELSE NOW()
                   END
               )) as tiempo_promedio,
               COUNT(d.id) as total_items,
               COUNT(CASE WHEN d.estado = 'servido' THEN 1 END) as items_completados,
               COUNT(CASE WHEN TIMESTAMPDIFF(MINUTE, d.fecha_creacion, NOW()) > 20 AND d.area = 'cocina' THEN 1 END) as items_criticos_cocina,
               COUNT(CASE WHEN TIMESTAMPDIFF(MINUTE, d.fecha_creacion, NOW()) > 10 AND d.area = 'bebidas' THEN 1 END) as items_criticos_bebidas
        FROM detalle_orden d
        INNER JOIN ordenes o ON d.orden_id = o.id
        WHERE o.fecha_hora >= ? AND o.fecha_hora <= ?
        GROUP BY d.area
    ";
    
    $stmt = $pdo->prepare($sql_tiempos);
    $stmt->execute([$fecha_inicio . ' 00:00:00', $fecha_fin . ' 23:59:59']);
    $tiempos_area = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    
    // Estadísticas generales de eficiencia
    $sql_general = "
        SELECT 
            COUNT(o.id) as ordenes_completadas,
            COUNT(CASE WHEN o.estado = 'finalizada' THEN 1 END) as ordenes_finalizadas,
            AVG(TIMESTAMPDIFF(MINUTE, o.fecha_hora, o.fecha_actualizacion)) as tiempo_promedio_orden,
            COUNT(DISTINCT o.hash_mesa) as mesas_atendidas,
            COUNT(DISTINCT o.mesero_id) as meseros_activos
        FROM ordenes o
        WHERE o.fecha_hora >= ? AND o.fecha_hora <= ?
    ";
    
    $stmt = $pdo->prepare($sql_general);
    $stmt->execute([$fecha_inicio . ' 00:00:00', $fecha_fin . ' 23:59:59']);
    $stats_general = $stmt->fetch();
    
    // Análisis de picos de demanda
    $sql_picos = "
        SELECT HOUR(o.fecha_hora) as hora,
               COUNT(o.id) as ordenes_hora,
               AVG(TIMESTAMPDIFF(MINUTE, o.fecha_hora, o.fecha_actualizacion)) as tiempo_promedio_hora
        FROM ordenes o
        WHERE o.fecha_hora >= ? AND o.fecha_hora <= ?
        AND o.estado = 'finalizada'
        GROUP BY HOUR(o.fecha_hora)
        ORDER BY ordenes_hora DESC
        LIMIT 5
    ";
    
    $stmt = $pdo->prepare($sql_picos);
    $stmt->execute([$fecha_inicio . ' 00:00:00', $fecha_fin . ' 23:59:59']);
    $picos_demanda = $stmt->fetchAll();
    
    // Calcular eficiencia general
    $tiempo_cocina = $tiempos_area['cocina'] ?? 0;
    $tiempo_bebidas = $tiempos_area['bebidas'] ?? 0;
    
    // Eficiencia basada en tiempos ideales (cocina: 15 min, bebidas: 5 min)
    $eficiencia_cocina = $tiempo_cocina > 0 ? max(0, 100 - (($tiempo_cocina - 15) * 2)) : 100;
    $eficiencia_bebidas = $tiempo_bebidas > 0 ? max(0, 100 - (($tiempo_bebidas - 5) * 5)) : 100;
    $eficiencia_general = ($eficiencia_cocina + $eficiencia_bebidas) / 2;
    
    // Tasa de finalización
    $tasa_finalizacion = $stats_general['ordenes_completadas'] > 0 ? 
        ($stats_general['ordenes_finalizadas'] / $stats_general['ordenes_completadas']) * 100 : 0;
    
    // Análisis de cuellos de botella
    $cuellos_botella = [];
    
    if ($tiempo_cocina > 20) {
        $cuellos_botella[] = [
            'area' => 'cocina',
            'problema' => 'Tiempo de preparación elevado',
            'valor' => round($tiempo_cocina, 1) . ' min',
            'recomendacion' => 'Revisar procesos de cocina y capacidad del personal'
        ];
    }
    
    if ($tiempo_bebidas > 8) {
        $cuellos_botella[] = [
            'area' => 'bebidas',
            'problema' => 'Tiempo de preparación elevado',
            'valor' => round($tiempo_bebidas, 1) . ' min',
            'recomendacion' => 'Optimizar procesos de preparación de bebidas'
        ];
    }
    
    if ($tasa_finalizacion < 95) {
        $cuellos_botella[] = [
            'area' => 'general',
            'problema' => 'Baja tasa de finalización',
            'valor' => round($tasa_finalizacion, 1) . '%',
            'recomendacion' => 'Revisar procesos de seguimiento de órdenes'
        ];
    }
    
    // Respuesta JSON
    echo json_encode([
        'success' => true,
        'eficiencia' => [
            'tiempo_promedio_cocina' => round($tiempo_cocina, 1),
            'tiempo_promedio_bebidas' => round($tiempo_bebidas, 1),
            'eficiencia_cocina' => round($eficiencia_cocina, 1),
            'eficiencia_bebidas' => round($eficiencia_bebidas, 1),
            'eficiencia_general' => round($eficiencia_general, 1),
            'ordenes_completadas' => $stats_general['ordenes_completadas'],
            'ordenes_finalizadas' => $stats_general['ordenes_finalizadas'],
            'tasa_finalizacion' => round($tasa_finalizacion, 1),
            'tiempo_promedio_orden' => round($stats_general['tiempo_promedio_orden'] ?? 0, 1),
            'mesas_atendidas' => $stats_general['mesas_atendidas'],
            'meseros_activos' => $stats_general['meseros_activos']
        ],
        'picos_demanda' => $picos_demanda,
        'cuellos_botella' => $cuellos_botella,
        'estadisticas' => [
            'fecha_inicio' => $fecha_inicio,
            'fecha_fin' => $fecha_fin,
            'periodo_dias' => (strtotime($fecha_fin) - strtotime($fecha_inicio)) / (60 * 60 * 24) + 1
        ]
    ]);
    
} catch (PDOException $e) {
    error_log("Error en reportes_eficiencia.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error al generar reporte de eficiencia',
        'eficiencia' => []
    ]);
}
?>
