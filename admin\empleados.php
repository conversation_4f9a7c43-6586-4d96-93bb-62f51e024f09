<?php
// Configurar variables para el layout
$page_title = 'Gestión de Empleados';
$breadcrumbs = [
    ['title' => 'Inicio', 'url' => url('')],
    ['title' => 'Administración', 'url' => url('admin/')],
    ['title' => 'Empleados']
];

// Iniciar el buffer de contenido
ob_start();

// Conectar a la base de datos
require_once '../config/session_config.php';\nsession_start();
require_once '../config/db.php';

// Verificar que sea administrador
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    echo '<div class="alert alert-danger">Acceso denegado. Solo administradores pueden acceder.</div>';
    $content = ob_get_clean();
    include '../includes/layout.php';
    exit();
}

// Obtener todos los empleados
try {
    $stmt = $pdo->query("
        SELECT id, nombre, correo, rol, activo,
               COALESCE(creado_en, NOW()) as fecha_creacion
        FROM usuarios
        ORDER BY rol, nombre
    ");
    $empleados = $stmt->fetchAll();

    // Agrupar por rol
    $empleados_por_rol = [];
    foreach ($empleados as $empleado) {
        $empleados_por_rol[$empleado['rol']][] = $empleado;
    }

} catch (PDOException $e) {
    $empleados = [];
    $empleados_por_rol = [];
    error_log("Error al obtener empleados: " . $e->getMessage());
}
?>

<!-- Estadísticas de Empleados - Diseño Personalizado -->
<div class="empleados-estadisticas-container mb-4">
    <div class="empleados-estadisticas-header">
        <div class="empleados-estadisticas-title">
            <span class="empleados-icon">👥</span>
            <h4>Gestión de Empleados</h4>
        </div>
        <button class="btn-nuevo-empleado" data-bs-toggle="modal" data-bs-target="#nuevoEmpleadoModal">
            ➕ Nuevo Empleado
        </button>
    </div>

    <div class="empleados-estadisticas-grid">
        <div class="stat-card stat-total">
            <div class="stat-icon">👥</div>
            <div class="stat-info">
                <span class="stat-numero"><?= count($empleados) ?></span>
                <span class="stat-label">Total Empleados</span>
            </div>
        </div>

        <div class="stat-card stat-activos">
            <div class="stat-icon">✅</div>
            <div class="stat-info">
                <span class="stat-numero"><?= count(array_filter($empleados, function($e) { return $e['activo'] == 1; })) ?></span>
                <span class="stat-label">Activos</span>
            </div>
        </div>

        <div class="stat-card stat-inactivos">
            <div class="stat-icon">❌</div>
            <div class="stat-info">
                <span class="stat-numero"><?= count(array_filter($empleados, function($e) { return $e['activo'] == 0; })) ?></span>
                <span class="stat-label">Inactivos</span>
            </div>
        </div>

        <div class="stat-card stat-roles">
            <div class="stat-icon">🏷️</div>
            <div class="stat-info">
                <span class="stat-numero"><?= count($empleados_por_rol) ?></span>
                <span class="stat-label">Roles Diferentes</span>
            </div>
        </div>
    </div>
</div>

<style>
/* Estilos para estadísticas de empleados */
.empleados-estadisticas-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.empleados-estadisticas-header {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    padding: 20px;
    border-bottom: 2px solid #a5d6a7;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.empleados-estadisticas-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.empleados-icon {
    font-size: 24px;
}

.empleados-estadisticas-title h4 {
    margin: 0;
    color: #2e7d32;
    font-weight: 600;
}

.btn-nuevo-empleado {
    background: #4caf50;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-nuevo-empleado:hover {
    background: #45a049;
    transform: translateY(-1px);
}

.empleados-estadisticas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0;
}

.stat-card {
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    border-right: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.stat-card:last-child {
    border-right: none;
}

.stat-card:hover {
    background: #f8f9fa;
}

.stat-icon {
    font-size: 32px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.stat-total .stat-icon {
    background: #e3f2fd;
}

.stat-activos .stat-icon {
    background: #e8f5e8;
}

.stat-inactivos .stat-icon {
    background: #ffebee;
}

.stat-roles .stat-icon {
    background: #fff3e0;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-numero {
    font-size: 24px;
    font-weight: 700;
    color: #212529;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
    margin-top: 4px;
}

@media (max-width: 768px) {
    .empleados-estadisticas-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }

    .empleados-estadisticas-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .stat-card {
        border-right: none;
        border-bottom: 1px solid #e9ecef;
        justify-content: center;
        text-align: center;
    }

    .stat-card:nth-child(2n) {
        border-right: 1px solid #e9ecef;
    }
}
</style>

<!-- Lista de Empleados - Diseño Personalizado -->
<div class="empleados-lista-container">
    <div class="empleados-lista-header">
        <div class="empleados-lista-title">
            <span class="lista-icon">📋</span>
            <h4>Lista de Empleados</h4>
        </div>
        <div class="empleados-filtros">
            <select class="filtro-rol" id="filtroRol">
                <option value="">Todos los roles</option>
                <option value="admin">👑 Administrador</option>
                <option value="mesero">🍽️ Mesero</option>
                <option value="cocina">🔥 Cocina</option>
                <option value="bebidas">🍹 Bebidas</option>
            </select>
            <select class="filtro-estado" id="filtroEstado">
                <option value="">Todos los estados</option>
                <option value="1">✅ Activos</option>
                <option value="0">❌ Inactivos</option>
            </select>
        </div>
    </div>

    <div class="empleados-lista-content">
        <?php if (empty($empleados)): ?>
            <div class="empleados-empty">
                <span class="empty-icon">👥</span>
                <h5>No hay empleados registrados</h5>
                <p>Agrega el primer empleado al sistema</p>
                <button class="btn-agregar-primero" data-bs-toggle="modal" data-bs-target="#nuevoEmpleadoModal">
                    ➕ Agregar Primer Empleado
                </button>
            </div>
        <?php else: ?>
            <div class="empleados-grid">
                <?php foreach ($empleados as $empleado): ?>
                    <div class="empleado-card" data-rol="<?= $empleado['rol'] ?>" data-estado="<?= $empleado['activo'] ?>">
                        <div class="empleado-header">
                            <div class="empleado-avatar">
                                <?php
                                $rol_icons = [
                                    'admin' => '👑',
                                    'mesero' => '🍽️',
                                    'cocina' => '🔥',
                                    'bebidas' => '🍹'
                                ];
                                echo $rol_icons[$empleado['rol']] ?? '👤';
                                ?>
                            </div>
                            <div class="empleado-info">
                                <h5 class="empleado-nombre"><?= htmlspecialchars($empleado['nombre']) ?></h5>
                                <p class="empleado-correo"><?= htmlspecialchars($empleado['correo']) ?></p>
                            </div>
                            <div class="empleado-estado">
                                <span class="estado-badge estado-<?= $empleado['activo'] ? 'activo' : 'inactivo' ?>">
                                    <?= $empleado['activo'] ? '🟢 Activo' : '🔴 Inactivo' ?>
                                </span>
                            </div>
                        </div>

                        <div class="empleado-detalles">
                            <div class="detalle-item">
                                <span class="detalle-label">ID:</span>
                                <span class="detalle-valor">#<?= $empleado['id'] ?></span>
                            </div>
                            <div class="detalle-item">
                                <span class="detalle-label">Rol:</span>
                                <span class="detalle-valor rol-<?= $empleado['rol'] ?>">
                                    <?= ucfirst($empleado['rol']) ?>
                                </span>
                            </div>
                            <div class="detalle-item">
                                <span class="detalle-label">Registro:</span>
                                <span class="detalle-valor">
                                    <?php
                                    if (isset($empleado['fecha_creacion']) && !empty($empleado['fecha_creacion'])) {
                                        echo date('d/m/Y', strtotime($empleado['fecha_creacion']));
                                    } else {
                                        echo 'No disponible';
                                    }
                                    ?>
                                </span>
                            </div>
                        </div>

                        <div class="empleado-acciones">
                            <button class="btn-accion btn-editar editar-empleado-btn"
                                    data-id="<?= $empleado['id'] ?>"
                                    data-nombre="<?= htmlspecialchars($empleado['nombre']) ?>"
                                    data-correo="<?= htmlspecialchars($empleado['correo']) ?>"
                                    data-rol="<?= $empleado['rol'] ?>"
                                    data-activo="<?= $empleado['activo'] ?>">
                                ✏️ Editar
                            </button>

                            <?php if ($empleado['id'] != $_SESSION['usuario_id']): ?>
                                <button class="btn-accion btn-toggle toggle-estado-btn"
                                        data-id="<?= $empleado['id'] ?>"
                                        data-estado="<?= $empleado['activo'] ?>">
                                    <?= $empleado['activo'] ? '⏸️ Desactivar' : '▶️ Activar' ?>
                                </button>

                                <button class="btn-accion btn-eliminar eliminar-empleado-btn"
                                        data-id="<?= $empleado['id'] ?>"
                                        data-nombre="<?= htmlspecialchars($empleado['nombre']) ?>">
                                    🗑️ Eliminar
                                </button>
                            <?php else: ?>
                                <span class="empleado-actual">👤 Tu cuenta</span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* Estilos para lista de empleados */
.empleados-lista-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.empleados-lista-header {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    padding: 20px;
    border-bottom: 2px solid #ce93d8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.empleados-lista-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.lista-icon {
    font-size: 24px;
}

.empleados-lista-title h4 {
    margin: 0;
    color: #7b1fa2;
    font-weight: 600;
}

.empleados-filtros {
    display: flex;
    gap: 12px;
}

.filtro-rol, .filtro-estado {
    padding: 8px 16px;
    border: 2px solid #ce93d8;
    border-radius: 20px;
    background: white;
    color: #7b1fa2;
    font-weight: 500;
    cursor: pointer;
}

.empleados-lista-content {
    padding: 20px;
}

.empleados-empty {
    text-align: center;
    padding: 40px 20px;
}

.empty-icon {
    font-size: 48px;
    display: block;
    margin-bottom: 16px;
}

.empleados-empty h5 {
    color: #6c757d;
    margin: 0 0 8px 0;
}

.empleados-empty p {
    color: #6c757d;
    margin-bottom: 20px;
}

.btn-agregar-primero {
    background: #7b1fa2;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-agregar-primero:hover {
    background: #6a1b9a;
    transform: translateY(-1px);
}

.empleados-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.empleado-card {
    background: #ffffff;
    border: 2px solid #f3e5f5;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
}

.empleado-card:hover {
    border-color: #9c27b0;
    box-shadow: 0 4px 12px rgba(156, 39, 176, 0.15);
    transform: translateY(-2px);
}

.empleado-header {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 16px;
}

.empleado-avatar {
    font-size: 32px;
    width: 50px;
    height: 50px;
    background: #f3e5f5;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.empleado-info {
    flex: 1;
}

.empleado-nombre {
    margin: 0 0 4px 0;
    color: #212529;
    font-weight: 600;
    font-size: 18px;
}

.empleado-correo {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
}

.empleado-estado {
    align-self: flex-start;
}

.estado-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.estado-activo {
    background: #e8f5e8;
    color: #2e7d32;
}

.estado-inactivo {
    background: #ffebee;
    color: #c62828;
}

.empleado-detalles {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
}

.detalle-item {
    text-align: center;
}

.detalle-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 4px;
}

.detalle-valor {
    display: block;
    font-weight: 600;
    color: #212529;
}

.rol-admin { color: #dc3545; }
.rol-mesero { color: #0d6efd; }
.rol-cocina { color: #6c757d; }
.rol-bebidas { color: #0dcaf0; }

.empleado-acciones {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.btn-accion {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    min-width: 100px;
}

.btn-editar {
    background: #2196f3;
    color: white;
}

.btn-editar:hover {
    background: #1976d2;
}

.btn-toggle {
    background: #ff9800;
    color: white;
}

.btn-toggle:hover {
    background: #f57c00;
}

.btn-eliminar {
    background: #f44336;
    color: white;
}

.btn-eliminar:hover {
    background: #d32f2f;
}

.empleado-actual {
    padding: 8px 16px;
    background: #e8f5e8;
    color: #2e7d32;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    flex: 1;
    text-align: center;
}

@media (max-width: 768px) {
    .empleados-lista-header {
        flex-direction: column;
        text-align: center;
    }

    .empleados-filtros {
        flex-direction: column;
        width: 100%;
    }

    .filtro-rol, .filtro-estado {
        width: 100%;
    }

    .empleados-grid {
        grid-template-columns: 1fr;
    }

    .empleado-header {
        flex-direction: column;
        text-align: center;
    }

    .empleado-detalles {
        grid-template-columns: 1fr;
    }

    .empleado-acciones {
        flex-direction: column;
    }
}
</style>

<!-- Modal Nuevo Empleado -->
<div class="modal fade" id="nuevoEmpleadoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">➕ Nuevo Empleado</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="formNuevoEmpleado">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="nombre" class="form-label">👤 Nombre Completo *</label>
                        <input type="text" class="form-control" id="nombre" name="nombre" required>
                    </div>
                    <div class="mb-3">
                        <label for="correo" class="form-label">📧 Correo Electrónico *</label>
                        <input type="email" class="form-control" id="correo" name="correo" required>
                    </div>
                    <div class="mb-3">
                        <label for="rol" class="form-label">🏷️ Rol *</label>
                        <select class="form-control" id="rol" name="rol" required>
                            <option value="">Seleccionar rol...</option>
                            <option value="admin">👑 Administrador</option>
                            <option value="mesero">🍽️ Mesero</option>
                            <option value="cocina">🔥 Cocina</option>
                            <option value="bebidas">🍹 Bebidas</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">🔒 Contraseña *</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <div class="form-text">La contraseña debe tener al menos 6 caracteres</div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="activo" name="activo" checked>
                            <label class="form-check-label" for="activo">
                                ✅ Empleado activo
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">
                        ➕ Crear Empleado
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Editar Empleado -->
<div class="modal fade" id="editarEmpleadoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">✏️ Editar Empleado</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="formEditarEmpleado">
                <input type="hidden" id="editarId" name="id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editarNombre" class="form-label">👤 Nombre Completo *</label>
                        <input type="text" class="form-control" id="editarNombre" name="nombre" required>
                    </div>
                    <div class="mb-3">
                        <label for="editarCorreo" class="form-label">📧 Correo Electrónico *</label>
                        <input type="email" class="form-control" id="editarCorreo" name="correo" required>
                    </div>
                    <div class="mb-3">
                        <label for="editarRol" class="form-label">🏷️ Rol *</label>
                        <select class="form-control" id="editarRol" name="rol" required>
                            <option value="admin">👑 Administrador</option>
                            <option value="mesero">🍽️ Mesero</option>
                            <option value="cocina">🔥 Cocina</option>
                            <option value="bebidas">🍹 Bebidas</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editarPassword" class="form-label">🔒 Nueva Contraseña</label>
                        <input type="password" class="form-control" id="editarPassword" name="password">
                        <div class="form-text">Dejar vacío para mantener la contraseña actual</div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editarActivo" name="activo">
                            <label class="form-check-label" for="editarActivo">
                                ✅ Empleado activo
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">
                        💾 Guardar Cambios
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// JavaScript adicional
$extra_js = '
<script>
document.addEventListener("DOMContentLoaded", function() {
    // Filtros
    const filtroRol = document.getElementById("filtroRol");
    const filtroEstado = document.getElementById("filtroEstado");

    if (filtroRol) {
        filtroRol.addEventListener("change", aplicarFiltros);
    }
    if (filtroEstado) {
        filtroEstado.addEventListener("change", aplicarFiltros);
    }

    // Crear nuevo empleado
    document.getElementById("formNuevoEmpleado").addEventListener("submit", function(e) {
        e.preventDefault();
        crearEmpleado();
    });

    // Editar empleado
    document.getElementById("formEditarEmpleado").addEventListener("submit", function(e) {
        e.preventDefault();
        editarEmpleado();
    });

    // Botones de editar
    document.querySelectorAll(".editar-empleado-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const id = this.dataset.id;
            const nombre = this.dataset.nombre;
            const correo = this.dataset.correo;
            const rol = this.dataset.rol;
            const activo = this.dataset.activo === "1";

            document.getElementById("editarId").value = id;
            document.getElementById("editarNombre").value = nombre;
            document.getElementById("editarCorreo").value = correo;
            document.getElementById("editarRol").value = rol;
            document.getElementById("editarActivo").checked = activo;

            new bootstrap.Modal(document.getElementById("editarEmpleadoModal")).show();
        });
    });

    // Botones de toggle estado
    document.querySelectorAll(".toggle-estado-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const id = this.dataset.id;
            const estadoActual = this.dataset.estado === "1";
            const nuevoEstado = !estadoActual;
            const accion = nuevoEstado ? "activar" : "desactivar";

            if (confirm(`¿Estás seguro de ${accion} este empleado?`)) {
                toggleEstadoEmpleado(id, nuevoEstado);
            }
        });
    });

    // Botones de eliminar
    document.querySelectorAll(".eliminar-empleado-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const id = this.dataset.id;
            const nombre = this.dataset.nombre;

            if (confirm(`¿Estás seguro de eliminar al empleado "${nombre}"? Esta acción no se puede deshacer.`)) {
                eliminarEmpleado(id);
            }
        });
    });
});

function aplicarFiltros() {
    const filtroRol = document.getElementById("filtroRol").value;
    const filtroEstado = document.getElementById("filtroEstado").value;
    const cards = document.querySelectorAll(".empleado-card");

    cards.forEach(card => {
        const rolCard = card.dataset.rol;
        const estadoCard = card.dataset.estado;

        let mostrar = true;

        if (filtroRol && rolCard !== filtroRol) {
            mostrar = false;
        }

        if (filtroEstado && estadoCard !== filtroEstado) {
            mostrar = false;
        }

        card.style.display = mostrar ? "block" : "none";
    });
}

function crearEmpleado() {
    const formData = new FormData(document.getElementById("formNuevoEmpleado"));

    fetch("/Restaurante/api/crear_empleado.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            nombre: formData.get("nombre"),
            correo: formData.get("correo"),
            rol: formData.get("rol"),
            password: formData.get("password"),
            activo: formData.get("activo") ? 1 : 0
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast("Empleado creado exitosamente", "success");
            setTimeout(() => location.reload(), 1500);
        } else {
            showToast("Error: " + data.message, "error");
        }
    })
    .catch(error => {
        console.error("Error:", error);
        showToast("Error de conexión", "error");
    });
}

function editarEmpleado() {
    const formData = new FormData(document.getElementById("formEditarEmpleado"));

    fetch("/Restaurante/api/editar_empleado.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            id: formData.get("id"),
            nombre: formData.get("nombre"),
            correo: formData.get("correo"),
            rol: formData.get("rol"),
            password: formData.get("password"),
            activo: formData.get("activo") ? 1 : 0
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast("Empleado actualizado exitosamente", "success");
            setTimeout(() => location.reload(), 1500);
        } else {
            showToast("Error: " + data.message, "error");
        }
    })
    .catch(error => {
        console.error("Error:", error);
        showToast("Error de conexión", "error");
    });
}

function toggleEstadoEmpleado(id, nuevoEstado) {
    fetch("/Restaurante/api/toggle_empleado.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            id: id,
            activo: nuevoEstado ? 1 : 0
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(`Empleado ${nuevoEstado ? "activado" : "desactivado"} exitosamente`, "success");
            setTimeout(() => location.reload(), 1500);
        } else {
            showToast("Error: " + data.message, "error");
        }
    })
    .catch(error => {
        console.error("Error:", error);
        showToast("Error de conexión", "error");
    });
}

function eliminarEmpleado(id) {
    fetch("/Restaurante/api/eliminar_empleado.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            id: id
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast("Empleado eliminado exitosamente", "success");
            setTimeout(() => location.reload(), 1500);
        } else {
            showToast("Error: " + data.message, "error");
        }
    })
    .catch(error => {
        console.error("Error:", error);
        showToast("Error de conexión", "error");
    });
}

// Función para mostrar notificaciones toast
function showToast(message, type = "info") {
    // Crear contenedor de toasts si no existe
    let toastContainer = document.getElementById("toast-container");
    if (!toastContainer) {
        toastContainer = document.createElement("div");
        toastContainer.id = "toast-container";
        toastContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            display: flex;
            flex-direction: column;
            gap: 10px;
        `;
        document.body.appendChild(toastContainer);
    }

    // Crear elemento toast
    const toast = document.createElement("div");
    const bgColor = type === "success" ? "#d4edda" : type === "error" ? "#f8d7da" : "#d1ecf1";
    const textColor = type === "success" ? "#155724" : type === "error" ? "#721c24" : "#0c5460";
    const icon = type === "success" ? "✅" : type === "error" ? "❌" : "ℹ️";

    toast.style.cssText = `
        background: ${bgColor};
        color: ${textColor};
        padding: 12px 20px;
        border-radius: 8px;
        border: 1px solid ${type === "success" ? "#c3e6cb" : type === "error" ? "#f5c6cb" : "#bee5eb"};
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        animation: slideInRight 0.3s ease-out;
        font-weight: 500;
    `;

    toast.innerHTML = `${icon} ${message}`;

    // Agregar animación CSS
    if (!document.getElementById("toast-animations")) {
        const style = document.createElement("style");
        style.id = "toast-animations";
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    toastContainer.appendChild(toast);

    // Remover después de 4 segundos
    setTimeout(() => {
        toast.style.animation = "slideOutRight 0.3s ease-in";
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 4000);
}
</script>
';

// Incluir el layout
include '../includes/layout.php';
?>