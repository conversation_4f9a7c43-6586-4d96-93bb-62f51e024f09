<?php
header('Content-Type: application/json');

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Verificar que sea un mesero
require_once '../config/db.php';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'mesero'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo los meseros pueden cerrar mesas']);
    exit();
}

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);
$hash = $input['hash'] ?? null;

if (!$hash) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Hash de mesa inválido']);
    exit();
}

try {
    // Verificar que la mesa existe y pertenece al usuario actual
    $stmt = $pdo->prepare("SELECT * FROM mesas WHERE hash_mesa = ? AND mesero_id = ? AND estado = 'ocupada'");
    $stmt->execute([$hash, $usuario['id']]);
    $mesa = $stmt->fetch();

    if (!$mesa) {
        echo json_encode(['success' => false, 'message' => 'Mesa no encontrada o no tienes permisos para cerrarla']);
        exit();
    }

    // Iniciar transacción
    $pdo->beginTransaction();

    try {
        // Verificar que todas las cuentas estén pagadas antes de cerrar
        $stmt = $pdo->prepare("SELECT COUNT(*) as cuentas_abiertas FROM cuentas WHERE hash_mesa = ? AND estado != 'pagada'");
        $stmt->execute([$hash]);
        $cuentas_result = $stmt->fetch();

        if ($cuentas_result['cuentas_abiertas'] > 0) {
            $pdo->rollBack();
            echo json_encode(['success' => false, 'message' => 'No se puede cerrar la mesa: hay cuentas sin pagar']);
            exit();
        }

        // Marcar todas las cuentas como cerradas (por seguridad)
        $stmt = $pdo->prepare("UPDATE cuentas SET estado = 'pagada' WHERE hash_mesa = ? AND estado != 'pagada'");
        $stmt->execute([$hash]);

        // Finalizar todas las órdenes de la mesa
        $stmt = $pdo->prepare("UPDATE ordenes SET estado = 'finalizada' WHERE hash_mesa = ?");
        $stmt->execute([$hash]);

        // Cambiar estado de la mesa a libre - SIN eliminar el hash para evitar conflictos
        $stmt = $pdo->prepare("UPDATE mesas SET estado = 'libre', mesero_id = NULL, fecha_apertura = NULL WHERE id = ?");
        $stmt->execute([$mesa['id']]);

        // Confirmar transacción
        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => 'Mesa cerrada exitosamente',
            'mesa' => $mesa['numero_mesa']
        ]);

    } catch (Exception $e) {
        // Revertir transacción en caso de error
        $pdo->rollBack();
        throw $e;
    }

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
