/* Estilos personalizados para el Sistema de Restaurante - Diseño Minimalista */

/* Colores del tema minimalista */
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #000000;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --light-gray: #f8fafc;
    --medium-gray: #e2e8f0;
    --dark-gray: #334155;
    --white: #ffffff;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --border-radius: 8px;
    --border-radius-lg: 12px;

    /* Colores personalizados para estados - Actualizados según preferencias del usuario */
    --estado-pendiente: #f97316;     /* Naranja para Pendientes */
    --estado-preparando: #8b5cf6;    /* Morado para Preparando */
    --estado-listo: #1e40af;         /* Azul Oscuro para Listo */
    --estado-servido: #22c55e;       /* Verde Claro para Servido */
}

/* Diseño general minimalista */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--light-gray);
    color: var(--dark-gray);
}

/* Sidebar minimalista */
.app-sidebar {
    background: var(--white) !important;
    border-right: 1px solid var(--medium-gray);
    box-shadow: var(--shadow-sm);
}

.sidebar-brand {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid var(--medium-gray);
}

.sidebar-brand .brand-text {
    color: var(--primary-color) !important;
    font-weight: 600 !important;
    font-size: 1.25rem;
}

.sidebar-brand .brand-image {
    width: 32px;
    height: 32px;
}

/* Header minimalista */
.app-header {
    background: var(--white) !important;
    border-bottom: 1px solid var(--medium-gray);
    box-shadow: var(--shadow-sm);
    padding: 0.75rem 0;
}

/* Menú lateral minimalista */
.nav.sidebar-menu .nav-item .nav-link {
    color: var(--secondary-color);
    padding: 0.75rem 1rem;
    margin: 0.125rem 0.5rem;
    border-radius: var(--border-radius);
    transition: all 0.2s ease;
    font-weight: 500;
}

.nav.sidebar-menu .nav-item .nav-link:hover {
    background-color: var(--light-gray);
    color: var(--primary-color);
}

.nav.sidebar-menu .nav-item .nav-link.active {
    background-color: var(--primary-color);
    color: var(--white);
}

.nav.sidebar-menu .nav-header {
    color: var(--secondary-color);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 1rem 1rem 0.5rem;
    margin-top: 1rem;
}

/* Cards minimalistas */
.card {
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    background: var(--white);
    transition: all 0.2s ease;
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    background: var(--white);
    border-bottom: 1px solid var(--medium-gray);
    padding: 1.25rem;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--dark-gray);
    margin: 0;
}

/* Botones minimalistas */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.625rem 1.25rem;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background-color: #1d4ed8;
    border-color: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Estados de órdenes con nuevos colores */
.estado-pendiente {
    background-color: #fed7aa;  /* Fondo naranja claro */
    color: #ea580c;             /* Texto naranja oscuro */
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.estado-preparando {
    background-color: #e9d5ff;  /* Fondo morado claro */
    color: #7c3aed;             /* Texto morado oscuro */
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.estado-listo {
    background-color: #bfdbfe;  /* Fondo azul claro */
    color: #1e40af;             /* Texto azul oscuro */
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.estado-servido {
    background-color: #bbf7d0;  /* Fondo verde claro */
    color: #16a34a;             /* Texto verde oscuro */
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.estado-finalizada {
    background-color: #bbf7d0;
    color: #16a34a;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

/* Clases Bootstrap personalizadas para estados */
.bg-pendiente {
    background-color: var(--estado-pendiente) !important;
}

.text-pendiente {
    color: var(--estado-pendiente) !important;
}

.bg-preparando {
    background-color: var(--estado-preparando) !important;
}

.text-preparando {
    color: var(--estado-preparando) !important;
}

.bg-listo {
    background-color: var(--estado-listo) !important;
}

.text-listo {
    color: var(--estado-listo) !important;
}

.bg-servido {
    background-color: var(--estado-servido) !important;
}

.text-servido {
    color: var(--estado-servido) !important;
}

/* Iconos de área */
.icono-cocina {
    color: #dc3545;
}

.icono-bebidas {
    color: #007bff;
}

/* Tabla de órdenes */
.tabla-ordenes {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.tabla-ordenes th {
    background-color: var(--restaurant-primary);
    color: white;
    font-weight: 600;
    border: none;
}

.tabla-ordenes td {
    vertical-align: middle;
    border-color: #e9ecef;
}

/* Notificaciones personalizadas */
.notification-item {
    border-left: 4px solid var(--restaurant-primary);
    background-color: #f8f9fa;
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 4px;
}

.notification-item.urgente {
    border-left-color: var(--restaurant-danger);
    background-color: #fff5f5;
}

/* Dashboard widgets */
.widget-stat {
    background: linear-gradient(135deg, var(--restaurant-primary), var(--restaurant-secondary));
    color: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.widget-stat .widget-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.widget-stat .widget-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Formularios */
.form-restaurant .form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 10px 15px;
    transition: border-color 0.3s;
}

.form-restaurant .form-control:focus {
    border-color: var(--restaurant-primary);
    box-shadow: 0 0 0 0.2rem rgba(212, 165, 116, 0.25);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .widget-stat {
        margin-bottom: 15px;
    }
    
    .tabla-ordenes {
        font-size: 0.875rem;
    }
}

/* Animaciones */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Loading spinner personalizado */
.loading-restaurant {
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--restaurant-primary);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== ESTILOS PARA NOTIFICACIONES ===== */

/* Campana de notificaciones */
.navbar-nav .nav-link {
    position: relative;
}

.navbar-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    min-width: 18px;
    height: 18px;
    padding: 0;
    font-size: 0.7rem;
    line-height: 18px;
    text-align: center;
    border-radius: 50%;
    animation: pulse-notification 2s infinite;
}

@keyframes pulse-notification {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.7);
    }
    70% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(0, 0, 0, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    }
}

/* Dropdown de notificaciones */
#notifications-dropdown {
    min-width: 380px;
    max-height: 450px;
    overflow-y: auto;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 0;
}

#notifications-dropdown .dropdown-header {
    background: var(--primary-color);
    color: var(--white);
    font-weight: 600;
    padding: 1rem;
    margin: 0;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

/* Items de notificación */
.notification-item {
    padding: 1rem;
    border-bottom: 1px solid var(--medium-gray);
    transition: all 0.2s ease;
    text-decoration: none;
    color: inherit;
    display: block;
}

.notification-item:hover {
    background-color: var(--light-gray);
    transform: translateX(5px);
    text-decoration: none;
    color: inherit;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.border-start {
    border-left-width: 4px !important;
    border-left-style: solid;
}

.notification-item .d-flex {
    align-items: flex-start;
    gap: 0.75rem;
}

.notification-item strong {
    font-weight: 600;
    color: var(--dark-gray);
    font-size: 0.9rem;
    line-height: 1.3;
}

.notification-item small {
    font-size: 0.8rem;
    line-height: 1.4;
}

.notification-item .text-muted {
    color: var(--secondary-color) !important;
}

/* Iconos de notificación */
.notification-item .bi {
    font-size: 1.1rem;
    margin-top: 2px;
}

/* Estados de notificación por color */
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-primary { color: var(--primary-color) !important; }

/* Scroll personalizado para notificaciones */
#notifications-dropdown::-webkit-scrollbar {
    width: 6px;
}

#notifications-dropdown::-webkit-scrollbar-track {
    background: var(--light-gray);
}

#notifications-dropdown::-webkit-scrollbar-thumb {
    background: var(--medium-gray);
    border-radius: 3px;
}

#notifications-dropdown::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* Responsive para notificaciones */
@media (max-width: 768px) {
    #notifications-dropdown {
        min-width: 320px;
        max-height: 350px;
    }

    .notification-item {
        padding: 0.75rem;
    }

    .notification-item strong {
        font-size: 0.85rem;
    }

    .notification-item small {
        font-size: 0.75rem;
    }
}

/* ===== ESTILOS PARA TABLA DE MESAS ACTIVAS ===== */

/* Tabla de mesas activas */
.mesas-activas-table {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.mesas-activas-table .table {
    margin-bottom: 0;
}

.mesas-activas-table .table th {
    background-color: var(--light-gray);
    color: var(--dark-gray);
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    border-bottom: 2px solid var(--medium-gray);
    padding: 1rem 0.75rem;
}

.mesas-activas-table .table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--medium-gray);
}

.mesas-activas-table .table tbody tr:hover {
    background-color: var(--light-gray);
    transition: background-color 0.2s ease;
}

/* Badges para estados de productos */
.badge-estado {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 9999px;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    margin: 0.125rem;
}

.badge-pendiente {
    background-color: #fed7aa;
    color: #ea580c;
    font-weight: 600;
}

.badge-preparando {
    background-color: #e9d5ff;
    color: #7c3aed;
}

.badge-listo {
    background-color: #bfdbfe;
    color: #1e40af;
}

.badge-servido {
    background-color: #bbf7d0;
    color: #16a34a;
}

/* Mejores estilos para badges con mejor contraste */
.badge.bg-pendiente {
    background-color: #f97316 !important;
    color: white !important;
    font-weight: 600 !important;
}

.badge.bg-preparando {
    background-color: #8b5cf6 !important;
    color: white !important;
    font-weight: 600 !important;
}

.badge.bg-listo {
    background-color: #1e40af !important;
    color: white !important;
    font-weight: 600 !important;
}

.badge.bg-servido {
    background-color: #22c55e !important;
    color: white !important;
    font-weight: 600 !important;
}

/* Sobrescribir estilos problemáticos */
.badge.bg-pendiente.bg-opacity-10 {
    background-color: #7c7b7a !important;
    color: white !important;
    opacity: 1 !important;
}

.badge.bg-preparando.bg-opacity-10 {
    background-color: #8b5cf6 !important;
    color: white !important;
    opacity: 1 !important;
}

.badge.bg-listo.bg-opacity-10 {
    background-color: #1e40af !important;
    color: white !important;
    opacity: 1 !important;
}

.badge.bg-servido.bg-opacity-10 {
    background-color: #22c55e !important;
    color: white !important;
    opacity: 1 !important;
}

/* Estilos adicionales para asegurar legibilidad en todos los contextos */
.text-pendiente {
    color: #000000 !important;
    font-weight: 600 !important;
}

.text-preparando {
    color: #7c3aed !important;
    font-weight: 600 !important;
}

.text-listo {
    color: #1e40af !important;
    font-weight: 600 !important;
}

.text-servido {
    color: #16a34a !important;
    font-weight: 600 !important;
}

/* Estilos específicos para badges en tablas */
.table .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    font-weight: 600;
    letter-spacing: 0.025em;
}

/* Asegurar que los badges naranjas siempre sean legibles */
.badge[class*="pendiente"],
.badge[class*="warning"] {
    background-color: #000000 !important;
    color: white !important;
    border: 1px solid #000000 !important;
}

/* Mejorar contraste para badges pequeños */
.badge-sm {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    font-weight: 700;
}

/* Solucionar problemas específicos de legibilidad */
.bg-pendiente.bg-opacity-20 {
    background-color: rgba(0, 0, 0, 0.2) !important;
    color: #000000 !important;
}

.bg-pendiente.bg-opacity-10 {
    background-color: rgba(0, 0, 0, 0.1) !important;
    color: #000000 !important;
}

.bg-pendiente {
    background-color: #000000 !important;
    color: #ffffff !important;
}

.bg-preparando.bg-opacity-20 {
    background-color: #e9d5ff !important;
    color: #7c3aed !important;
}

.bg-listo.bg-opacity-20 {
    background-color: #bfdbfe !important;
    color: #1e40af !important;
}

.bg-servido.bg-opacity-20 {
    background-color: #bbf7d0 !important;
    color: #16a34a !important;
}

/* Estilos para headers de cards con estados */
.card-header.bg-pendiente.bg-opacity-20 {
    background-color: rgba(0, 0, 0, 0.2) !important;
    border-color: #000000 !important;
}

.card-header.bg-pendiente.bg-opacity-10 {
    background-color: rgba(0, 0, 0, 0.1) !important;
    border-color: #000000 !important;
}

.card-header.bg-pendiente {
    background-color: #000000 !important;
    color: #ffffff !important;
    border-color: #000000 !important;
}

.card-header.bg-preparando.bg-opacity-20 {
    background-color: #e9d5ff !important;
    border-color: #8b5cf6 !important;
}

.card-header.bg-listo.bg-opacity-20 {
    background-color: #bfdbfe !important;
    border-color: #1e40af !important;
}

.card-header.bg-servido.bg-opacity-20 {
    background-color: #bbf7d0 !important;
    border-color: #22c55e !important;
}

/* Iconos en círculos con estados */
.bg-pendiente.bg-opacity-20 i,
.bg-preparando.bg-opacity-20 i,
.bg-listo.bg-opacity-20 i,
.bg-servido.bg-opacity-20 i {
    font-weight: 600 !important;
}

.badge-sin-productos {
    background-color: #f1f5f9;
    color: #64748b;
}

/* Responsive para tabla de mesas */
@media (max-width: 768px) {
    .mesas-activas-table .table th,
    .mesas-activas-table .table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
    }

    .badge-estado {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
}

/* Animación para nuevas filas */
.mesa-row {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== SISTEMA DE NOTIFICACIONES MODERNAS ===== */

/* Contenedor de notificaciones */
#toast-container {
    z-index: 9999 !important;
    max-width: 400px;
    width: 100%;
}

/* Estilos para las notificaciones */
.toast {
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    margin-bottom: 8px;
    min-width: 320px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toast:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
}

/* Colores personalizados para notificaciones */
.toast.bg-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
}

.toast.bg-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
}

.toast.bg-warning {
    background: linear-gradient(135deg, #000000 0%, #333333 100%) !important;
    color: #ffffff !important;
}

.toast.bg-info {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
}

/* Iconos en las notificaciones */
.toast .bi {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* Botón de cerrar personalizado */
.toast .btn-close {
    filter: brightness(0) invert(1);
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.toast .btn-close:hover {
    opacity: 1;
    transform: scale(1.1);
}

/* Animaciones de entrada y salida */
.toast-enter {
    animation: toastSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toast-exit {
    animation: toastSlideOut 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes toastSlideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes toastSlideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Responsive para notificaciones */
@media (max-width: 768px) {
    #toast-container {
        max-width: calc(100vw - 20px);
        left: 10px !important;
        right: 10px !important;
        top: 10px !important;
    }

    .toast {
        min-width: unset;
        width: 100%;
    }
}

/* Efectos especiales para notificaciones importantes */
.toast.toast-important {
    animation: toastPulse 2s infinite;
}

@keyframes toastPulse {
    0%, 100% {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    }
    50% {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    }
}

/* ===== SOBRESCRIBIR COLORES NARANJAS DE BOOTSTRAP ===== */

/* Sobrescribir todas las clases warning de Bootstrap para usar negro */
.bg-warning {
    background-color: #000000 !important;
}

.text-warning {
    color: #000000 !important;
}

.bg-warning.bg-opacity-10 {
    background-color: rgba(0, 0, 0, 0.1) !important;
    color: #000000 !important;
}

.bg-warning.bg-opacity-20 {
    background-color: rgba(0, 0, 0, 0.2) !important;
    color: #000000 !important;
}

.badge.bg-warning {
    background-color: #000000 !important;
    color: #ffffff !important;
}

.alert-warning {
    background-color: rgba(0, 0, 0, 0.1) !important;
    border-color: #000000 !important;
    color: #000000 !important;
}

.btn-warning {
    background-color: #000000 !important;
    border-color: #000000 !important;
    color: #ffffff !important;
}

.btn-warning:hover {
    background-color: #333333 !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

/* Sobrescribir iconos con color warning */
.text-warning i,
i.text-warning {
    color: #000000 !important;
}

/* Sobrescribir cualquier color naranja restante */
.text-orange,
.bg-orange,
[class*="orange"] {
    color: #000000 !important;
    background-color: rgba(0, 0, 0, 0.1) !important;
}

/* Sobrescribir variables CSS de Bootstrap para warning */
:root {
    --bs-warning: #000000 !important;
    --bs-warning-rgb: 0, 0, 0 !important;
    --bs-warning-text-emphasis: #000000 !important;
    --bs-warning-bg-subtle: rgba(0, 0, 0, 0.1) !important;
    --bs-warning-border-subtle: #000000 !important;
}

/* Asegurar que todos los elementos con warning usen negro */
*[class*="warning"] {
    color: #000000 !important;
}

*[class*="warning"][class*="bg"] {
    background-color: #000000 !important;
    color: #ffffff !important;
}

/* Sobrescribir agresivamente cualquier color naranja de AdminLTE */
.card-warning,
.text-bg-warning,
.link-warning,
.callout-warning {
    background-color: #000000 !important;
    color: #ffffff !important;
    border-color: #000000 !important;
}

.link-warning:hover,
.link-warning:focus {
    color: #333333 !important;
}

/* Sobrescribir variables CSS de AdminLTE */
:root {
    --lte-card-variant-bg: #000000 !important;
    --lte-card-variant-bg-rgb: 0, 0, 0 !important;
    --lte-card-variant-color: #ffffff !important;
    --lte-card-variant-color-rgb: 255, 255, 255 !important;
}

/* Forzar todos los elementos warning a usar negro */
[class*="warning"],
.warning,
*[data-color="warning"],
*[data-type="warning"] {
    color: #000000 !important;
}

[class*="warning"][class*="bg"],
.warning[class*="bg"],
*[data-color="warning"][class*="bg"],
*[data-type="warning"][class*="bg"] {
    background-color: #000000 !important;
    color: #ffffff !important;
}

/* Sobrescribir específicamente todas las clases pendiente */
[class*="pendiente"] {
    color: #000000 !important;
}

[class*="pendiente"][class*="bg"] {
    background-color: #000000 !important;
    color: #ffffff !important;
}

.badge[class*="pendiente"] {
    background-color: #000000 !important;
    color: #ffffff !important;
}

.text-pendiente,
.pendiente {
    color: #000000 !important;
}

.bg-pendiente {
    background-color: #000000 !important;
    color: #ffffff !important;
}

/* Sobrescribir todas las variantes de bg-pendiente */
.bg-pendiente.bg-opacity-5 {
    background-color: rgba(0, 0, 0, 0.05) !important;
    color: #000000 !important;
}

.bg-pendiente.bg-opacity-10 {
    background-color: rgba(0, 0, 0, 0.1) !important;
    color: #000000 !important;
}

.bg-pendiente.bg-opacity-20 {
    background-color: rgba(0, 0, 0, 0.2) !important;
    color: #000000 !important;
}

.bg-pendiente.bg-opacity-25 {
    background-color: rgba(0, 0, 0, 0.25) !important;
    color: #000000 !important;
}

.bg-pendiente.bg-opacity-50 {
    background-color: rgba(0, 0, 0, 0.5) !important;
    color: #ffffff !important;
}

.bg-pendiente.bg-opacity-75 {
    background-color: rgba(0, 0, 0, 0.75) !important;
    color: #ffffff !important;
}

/* Reglas específicas para headers de cards con estado pendiente */
.card-header.bg-pendiente,
.card-header[class*="pendiente"] {
    background-color: rgba(0, 0, 0, 0.2) !important;
    color: #000000 !important;
    border-color: #000000 !important;
}

.card-header.bg-pendiente h5,
.card-header[class*="pendiente"] h5,
.card-header.bg-pendiente .text-pendiente,
.card-header[class*="pendiente"] .text-pendiente {
    color: #000000 !important;
}

/* Asegurar que los badges con estado pendiente sean legibles */
.badge.bg-pendiente.bg-opacity-10,
.badge[class*="pendiente"][class*="opacity"] {
    background-color: #000000 !important;
    color: #ffffff !important;
    opacity: 1 !important;
}

/* Forzar color negro en todos los textos pendiente */
h5.text-pendiente,
.text-pendiente,
span.text-pendiente,
div.text-pendiente {
    color: #000000 !important;
    font-weight: 600 !important;
}

/* REGLA FINAL: Sobrescribir CUALQUIER color naranja restante */
* {
    /* Sobrescribir cualquier variable CSS que pueda contener naranja */
    --estado-pendiente: #000000 !important;
    --warning-color: #000000 !important;
    --bs-warning: #000000 !important;
    --bs-warning-rgb: 0, 0, 0 !important;
}

/* Sobrescribir colores naranjas específicos que puedan aparecer */
[style*="#f59e0b"],
[style*="#fed7aa"],
[style*="#ea580c"],
[style*="#f97316"],
[style*="orange"],
[style*="rgb(245, 158, 11)"],
[style*="rgba(245, 158, 11"] {
    color: #000000 !important;
    background-color: rgba(0, 0, 0, 0.1) !important;
}

/* Asegurar que TODOS los elementos con pendiente usen negro */
*[class*="pendiente"],
*[data-estado="pendiente"],
*[data-type="pendiente"] {
    color: #000000 !important;
}

*[class*="pendiente"][class*="bg"],
*[data-estado="pendiente"][class*="bg"],
*[data-type="pendiente"][class*="bg"] {
    background-color: #000000 !important;
    color: #ffffff !important;
}
