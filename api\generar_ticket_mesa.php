<?php
header('Content-Type: application/json');

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

require_once '../config/db.php';

// Obtener hash de mesa
$hash_mesa = $_GET['hash_mesa'] ?? '';

if (empty($hash_mesa)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Hash de mesa requerido']);
    exit();
}

try {
    // Obtener información de la configuración del restaurante
    $stmt = $pdo->query("SELECT * FROM configuracion WHERE id = 1");
    $config = $stmt->fetch();
    
    if (!$config) {
        echo json_encode(['success' => false, 'message' => 'Configuración del restaurante no encontrada']);
        exit();
    }

    // Obtener información de la mesa
    $stmt = $pdo->prepare("
        SELECT m.numero_mesa, m.mesero_id, u.nombre as mesero_nombre
        FROM mesas m
        LEFT JOIN usuarios u ON m.mesero_id = u.id
        WHERE m.hash_mesa = ?
    ");
    $stmt->execute([$hash_mesa]);
    $mesa = $stmt->fetch();

    if (!$mesa) {
        echo json_encode(['success' => false, 'message' => 'Mesa no encontrada']);
        exit();
    }

    // Obtener todas las cuentas pagadas de la mesa
    $stmt = $pdo->prepare("
        SELECT c.id, c.nombre_cliente, c.apellido_cliente, c.total, c.fecha_creacion,
               c.subtotal, c.propina_porcentaje, c.propina_monto, 
               c.descuento_tipo, c.descuento_valor, c.descuento_monto, c.total_final
        FROM cuentas c
        WHERE c.hash_mesa = ? AND c.estado = 'pagada'
        ORDER BY c.fecha_creacion ASC
    ");
    $stmt->execute([$hash_mesa]);
    $cuentas = $stmt->fetchAll();

    if (empty($cuentas)) {
        echo json_encode(['success' => false, 'message' => 'No hay cuentas pagadas en esta mesa']);
        exit();
    }

    // Obtener productos de cada cuenta
    $cuentas_con_productos = [];
    foreach ($cuentas as $cuenta) {
        $stmt = $pdo->prepare("
            SELECT p.nombre, d.cantidad, d.precio_unitario, d.subtotal, d.notas
            FROM detalle_orden d
            INNER JOIN ordenes o ON d.orden_id = o.id
            INNER JOIN productos p ON d.producto_id = p.id
            WHERE o.cuenta_id = ?
            ORDER BY p.nombre ASC
        ");
        $stmt->execute([$cuenta['id']]);
        $productos = $stmt->fetchAll();

        $cuenta['productos'] = $productos;
        $cuentas_con_productos[] = $cuenta;
    }

    // Preparar datos del ticket
    $ticket_data = [
        'restaurante' => [
            'nombre' => $config['nombre_restaurante'],
            'direccion' => $config['direccion'],
            'telefono' => $config['telefono'],
            'logo' => null // Se puede agregar después si se implementa
        ],
        'mesa' => [
            'numero' => $mesa['numero_mesa'],
            'hash' => $hash_mesa,
            'mesero_nombre' => $mesa['mesero_nombre']
        ],
        'cuentas' => $cuentas_con_productos,
        'totales' => [
            'moneda' => $config['moneda'] ?? 'Q',
            'total_cuentas' => count($cuentas_con_productos),
            'total_general' => array_sum(array_column($cuentas_con_productos, 'total'))
        ],
        'fecha_generacion' => date('Y-m-d H:i:s')
    ];

    echo json_encode([
        'success' => true,
        'ticket' => $ticket_data
    ]);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
