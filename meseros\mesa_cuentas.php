<?php
// Configurar variables para el layout
$page_title = 'Cuentas de Mesa';
$breadcrumbs = [
    ['title' => 'Inicio', 'url' => '/Restaurante/'],
    ['title' => 'Meseros', 'url' => '/Restaurante/meseros/'],
    ['title' => 'Cuentas de Mesa']
];

// Iniciar el buffer de contenido
ob_start();

// Obtener hash de la mesa
$hash = $_GET['hash'] ?? '';

if (empty($hash)) {
    echo '<div class="alert alert-danger">Hash de mesa no proporcionado.</div>';
    $content = ob_get_clean();
    include '../includes/layout.php';
    exit();
}

// Conectar a la base de datos restaurante
session_start();
require_once '../config/db.php';

try {
    // Obtener información de la mesa
    $stmt = $pdo->prepare("SELECT * FROM mesas WHERE hash_mesa = ? AND estado = 'ocupada'");
    $stmt->execute([$hash]);
    $mesa = $stmt->fetch();

    // Debug: Información de la mesa
    echo "<!-- DEBUG: Hash recibido: " . $hash . " -->";
    echo "<!-- DEBUG: Mesa encontrada: " . ($mesa ? "Sí (ID: " . $mesa['id'] . ", Número: " . $mesa['numero_mesa'] . ")" : "No") . " -->";

    if (!$mesa) {
        echo '<div class="alert alert-danger">Mesa no encontrada o ya cerrada.</div>';
        $content = ob_get_clean();
        include '../includes/layout.php';
        exit();
    }

    // Obtener cuentas de la mesa (consulta simplificada)
    $stmt = $pdo->prepare("
        SELECT c.*, u.nombre as mesero_nombre
        FROM cuentas c
        LEFT JOIN usuarios u ON c.mesero_id = u.id
        WHERE c.hash_mesa = ? AND c.estado != 'pagada'
        ORDER BY c.fecha_creacion ASC
    ");
    $stmt->execute([$hash]);
    $cuentas = $stmt->fetchAll();

    // Para cada cuenta, obtener estadísticas detalladas
    foreach ($cuentas as $key => $cuenta) {
        // Obtener productos de la cuenta con información más detallada
        $stmt = $pdo->prepare("
            SELECT d.*, p.nombre as producto_nombre, p.area as producto_area,
                   o.fecha_hora as orden_fecha, o.id as orden_id
            FROM detalle_orden d
            INNER JOIN ordenes o ON d.orden_id = o.id
            INNER JOIN productos p ON d.producto_id = p.id
            WHERE o.cuenta_id = ?
            ORDER BY o.fecha_hora DESC, d.id DESC
        ");
        $stmt->execute([$cuenta['id']]);
        $cuentas[$key]['productos'] = $stmt->fetchAll();

        // Calcular estadísticas
        $cuentas[$key]['productos_pendientes'] = 0;
        $cuentas[$key]['productos_preparando'] = 0;
        $cuentas[$key]['productos_listos'] = 0;
        $cuentas[$key]['productos_servidos'] = 0;
        $cuentas[$key]['total_real'] = 0;

        foreach ($cuentas[$key]['productos'] as $producto) {
            $cuentas[$key]['total_real'] += $producto['subtotal'];

            switch ($producto['estado']) {
                case 'pendiente': $cuentas[$key]['productos_pendientes']++; break;
                case 'preparando': $cuentas[$key]['productos_preparando']++; break;
                case 'listo': $cuentas[$key]['productos_listos']++; break;
                case 'servido': $cuentas[$key]['productos_servidos']++; break;
            }
        }

        // Actualizar total si es diferente
        if (abs($cuentas[$key]['total_real'] - $cuentas[$key]['total']) > 0.01) { // Usar comparación con tolerancia para decimales
            $stmt = $pdo->prepare("UPDATE cuentas SET total = ? WHERE id = ?");
            $stmt->execute([$cuentas[$key]['total_real'], $cuentas[$key]['id']]);
            $cuentas[$key]['total'] = $cuentas[$key]['total_real'];
        }

    }

    // Obtener nombre del encargado
    $stmt = $pdo->prepare("SELECT nombre FROM usuarios WHERE id = ?");
    $stmt->execute([$mesa['mesero_id']]);
    $encargado = $stmt->fetch();

} catch (PDOException $e) {
    $cuentas = [];
    $encargado = ['nombre' => 'Desconocido'];
    echo '<div class="alert alert-danger">Error de base de datos: ' . $e->getMessage() . '</div>';
}

// Asegurar que las variables estén definidas
if (!isset($cuentas)) {
    $cuentas = [];
}
if (!isset($encargado)) {
    $encargado = ['nombre' => 'Desconocido'];
}
?>

<!-- Información de la Mesa -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <div class="bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                                <i class="bi bi-table text-primary fs-3"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">Mesa #<?= $mesa['numero_mesa'] ?></h4>
                                <p class="text-muted mb-0">
                                    <i class="bi bi-person me-1"></i>
                                    Encargado: <?= htmlspecialchars($encargado['nombre'] ?? 'Desconocido') ?>
                                </p>
                                <small class="text-muted">
                                    <i class="bi bi-clock me-1"></i>
                                    Abierta: <?= date('d/m/Y H:i', strtotime($mesa['fecha_apertura'])) ?>
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <div class="d-flex gap-2 justify-content-md-end flex-wrap">
                            <button class="btn btn-success rounded-pill" data-bs-toggle="modal" data-bs-target="#modalNuevaCuenta">
                                <i class="bi bi-person-plus me-2"></i>
                                Nueva Cuenta
                            </button>
                            <?php if (isset($cuentas) && !empty($cuentas)): ?>
                                <button class="btn btn-dark rounded-pill" data-bs-toggle="modal" data-bs-target="#modalPagarMesa" style="background-color: #1a365d; color: black; border-color: #1a365d;">
                                    <i class="bi bi-credit-card me-2"></i>
                                    Pagar Mesa Completa
                                </button>
                            <?php endif; ?>
                            <a href="/Restaurante/meseros/tomar_orden.php" class="btn btn-outline-secondary rounded-pill">
                                <i class="bi bi-arrow-left me-2"></i>
                                Volver a Tomar Orden
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cuentas Activas - Diseño Personalizado -->
<div class="cuentas-activas-container">
    <div class="cuentas-header">
        <div class="cuentas-title">
            <span class="cuentas-icon">💳</span>
            <h4>Cuentas Activas (<?= isset($cuentas) ? count($cuentas) : 0 ?>)</h4>
        </div>
        <button class="btn-actualizar" onclick="location.reload()">
            🔄 Actualizar
        </button>
    </div>

    <div class="cuentas-content">
        <?php if (!isset($cuentas) || empty($cuentas)): ?>
            <div class="cuentas-empty">
                <span class="empty-icon">📄</span>
                <h5>No hay cuentas activas</h5>
                <p>Crea la primera cuenta para esta mesa</p>
                <button class="btn-crear-primera" data-bs-toggle="modal" data-bs-target="#modalNuevaCuenta">
                    👤 Crear Primera Cuenta
                </button>
            </div>
        <?php else: ?>
            <div class="cuentas-grid">
                <?php foreach ($cuentas as $index => $cuenta): ?>
                    <div class="cuenta-card">
                        <div class="cuenta-header">
                            <div class="cliente-info">
                                <span class="cliente-nombre">
                                    👤 <?= htmlspecialchars($cuenta['nombre_cliente']) ?>
                                    <?php if (!empty($cuenta['apellido_cliente'])): ?>
                                        <?= htmlspecialchars($cuenta['apellido_cliente']) ?>
                                    <?php endif; ?>

                                </span>
                                <span class="estado-badge estado-<?= $cuenta['estado'] ?>">
                                    <?= $cuenta['estado'] === 'abierta' ? '🟢 Abierta' : '🟡 ' . ucfirst($cuenta['estado']) ?>
                                </span>
                            </div>
                        </div>

                        <div class="cuenta-estadisticas">
                            <div class="stat-item">
                                <span class="stat-numero"><?= count($cuenta['productos']) ?></span>
                                <span class="stat-label">📦 Productos</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-numero">Q<?= number_format($cuenta['total'], 0) ?></span>
                                <span class="stat-label">💰 Total</span>
                            </div>
                        </div>

                        <?php if (!empty($cuenta['productos'])): ?>
                            <div class="cuenta-productos">
                                <div class="productos-titulo">Estado de productos:</div>
                                <div class="productos-badges">
                                    <?php if ($cuenta['productos_pendientes'] > 0): ?>
                                        <span class="producto-badge pendientes">
                                            ⏳ <?= $cuenta['productos_pendientes'] ?> pendientes
                                        </span>
                                    <?php endif; ?>
                                    <?php if ($cuenta['productos_preparando'] > 0): ?>
                                        <span class="producto-badge preparando">
                                            🔄 <?= $cuenta['productos_preparando'] ?> preparando
                                        </span>
                                    <?php endif; ?>
                                    <?php if ($cuenta['productos_listos'] > 0): ?>
                                        <span class="producto-badge listos">
                                            ✅ <?= $cuenta['productos_listos'] ?> listos
                                        </span>
                                    <?php endif; ?>
                                    <?php if ($cuenta['productos_servidos'] > 0): ?>
                                        <span class="producto-badge servidos">
                                            🍽️ <?= $cuenta['productos_servidos'] ?> servidos
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="cuenta-fecha">
                            🕐 Creada: <?= date('d/m/Y H:i', strtotime($cuenta['fecha_creacion'])) ?>
                        </div>

                        <div class="cuenta-acciones">
                            <button class="btn-accion btn-agregar agregar-orden-btn"
                                    data-cuenta-id="<?= $cuenta['id'] ?>"
                                    data-cuenta-nombre="<?= htmlspecialchars($cuenta['nombre_cliente']) ?>">
                                ➕ Agregar
                            </button>
                            <button class="btn-accion btn-ver ver-detalle-btn"
                                    data-cuenta-id="<?= $cuenta['id'] ?>">
                                👁️ Ver
                            </button>
                            <button class="btn-accion btn-ticket generar-ticket-btn"
                                    data-cuenta-id="<?= $cuenta['id'] ?>">
                                🧾 Ticket
                            </button>
                            <?php if ($cuenta['estado'] === 'abierta'): ?>
                                <button class="btn-accion btn-ajustes ajustes-cuenta-btn"
                                        data-cuenta-id="<?= $cuenta['id'] ?>"
                                        data-cuenta-nombre="<?= htmlspecialchars($cuenta['nombre_cliente']) ?>">
                                    ⚙️ Propina
                                </button>
                                <button class="btn-accion btn-pagar cerrar-cuenta-btn"
                                        data-cuenta-id="<?= $cuenta['id'] ?>"
                                        data-cuenta-nombre="<?= htmlspecialchars($cuenta['nombre_cliente']) ?>">
                                    💳 Pagar
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* Estilos personalizados para cuentas activas */
.cuentas-activas-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.cuentas-header {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    padding: 20px;
    border-bottom: 2px solid #a5d6a7;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cuentas-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.cuentas-icon {
    font-size: 24px;
}

.cuentas-title h4 {
    margin: 0;
    color: #2e7d32;
    font-weight: 600;
}

.btn-actualizar {
    background: #4caf50;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-actualizar:hover {
    background: #45a049;
    transform: translateY(-1px);
}

.cuentas-content {
    padding: 20px;
}

.cuentas-empty {
    text-align: center;
    padding: 40px 20px;
}

.empty-icon {
    font-size: 48px;
    display: block;
    margin-bottom: 16px;
}

.cuentas-empty h5 {
    color: #6c757d;
    margin: 0 0 8px 0;
}

.cuentas-empty p {
    color: #6c757d;
    margin-bottom: 20px;
}

.btn-crear-primera {
    background: #4caf50;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-crear-primera:hover {
    background: #45a049;
    transform: translateY(-1px);
}

.cuentas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 20px;
}

.cuenta-card {
    background: #ffffff;
    border: 2px solid #e8f5e8;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
}

.cuenta-card:hover {
    border-color: #4caf50;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.15);
    transform: translateY(-2px);
}

.cuenta-header {
    margin-bottom: 16px;
}

.cliente-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

.cliente-nombre {
    font-weight: 700;
    color: #2e7d32;
    font-size: 16px;
}

.estado-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.estado-abierta {
    background: #e8f5e8;
    color: #2e7d32;
}

.cuenta-estadisticas {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-item {
    text-align: center;
}

.stat-numero {
    display: block;
    font-size: 20px;
    font-weight: 700;
    color: #2e7d32;
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
}

.cuenta-productos {
    margin-bottom: 16px;
}

.productos-titulo {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 8px;
}

.productos-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.producto-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.producto-badge.pendientes {
    background: #fff3cd;
    color: #856404;
}

.producto-badge.preparando {
    background: #d1ecf1;
    color: #0c5460;
}

.producto-badge.listos {
    background: #d4edda;
    color: #155724;
}

.producto-badge.servidos {
    background: #e2e3e5;
    color: #383d41;
}

.cuenta-fecha {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 16px;
}

.cuenta-acciones {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 8px;
}

.btn-accion {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-agregar {
    background: #4caf50;
    color: white;
}

.btn-agregar:hover {
    background: #45a049;
}

.btn-ver {
    background: #2196f3;
    color: white;
}

.btn-ver:hover {
    background: #1976d2;
}

.btn-ticket {
    background: #ff9800;
    color: white;
}

.btn-ticket:hover {
    background: #f57c00;
}

.btn-pagar {
    background: #8bc34a;
    color: white;
}

.btn-pagar:hover {
    background: #7cb342;
}

.btn-ajustes {
    background: #ff9800;
    color: white;
}

.btn-ajustes:hover {
    background: #f57c00;
}

@media (max-width: 768px) {
    .cuentas-grid {
        grid-template-columns: 1fr;
    }

    .cuentas-header {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .cliente-info {
        flex-direction: column;
        text-align: center;
    }

    .cuenta-acciones {
        grid-template-columns: 1fr 1fr;
    }
}
</style>

<!-- Modal para Nueva Cuenta -->
<div class="modal fade" id="modalNuevaCuenta" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Nueva Cuenta</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="formNuevaCuenta">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="nombre_cliente" class="form-label">Nombre del Cliente *</label>
                        <input type="text" class="form-control" id="nombre_cliente" name="nombre_cliente" required>
                    </div>
                    <div class="mb-3">
                        <label for="apellido_cliente" class="form-label">Apellido del Cliente</label>
                        <input type="text" class="form-control" id="apellido_cliente" name="apellido_cliente">
                    </div>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        Se creará una cuenta separada para este cliente en la Mesa #<?= $mesa['numero_mesa'] ?>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-person-plus me-2"></i>
                        Crear Cuenta
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para Pagar Mesa Completa -->
<div class="modal fade" id="modalPagarMesa" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-credit-card me-2"></i>
                    Pagar Mesa Completa
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>¿Estás seguro de pagar toda la mesa?</strong>
                </div>

                <p>Esta acción realizará lo siguiente:</p>
                <ul>
                    <li>✅ Marcará todas las cuentas como <strong>pagadas</strong></li>
                    <li>✅ Finalizará todas las órdenes pendientes</li>
                    <li>✅ Calculará el total general de la mesa</li>
                    <li>⚠️ Esta acción <strong>no se puede deshacer</strong></li>
                </ul>

                <?php if (isset($cuentas) && !empty($cuentas)): ?>
                    <div class="card bg-light">
                        <div class="card-header">
                            <h6 class="mb-0">Resumen de Cuentas</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            $total_mesa = 0;
                            $cuentas_abiertas = 0;
                            foreach ($cuentas as $cuenta):
                                if ($cuenta['estado'] === 'abierta') {
                                    $cuentas_abiertas++;
                                    $total_mesa += $cuenta['total'];
                                }
                            ?>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>
                                        <i class="bi bi-person-circle me-1"></i>
                                        <?= htmlspecialchars($cuenta['nombre_cliente']) ?>
                                        <?php if (!empty($cuenta['apellido_cliente'])): ?>
                                            <?= htmlspecialchars($cuenta['apellido_cliente']) ?>
                                        <?php endif; ?>
                                        <span class="badge bg-<?= $cuenta['estado'] === 'abierta' ? 'success' : 'secondary' ?> ms-2">
                                            <?= ucfirst($cuenta['estado']) ?>
                                        </span>
                                    </span>
                                    <span class="fw-bold">Q<?= number_format($cuenta['total'], 0) ?></span>
                                </div>
                            <?php endforeach; ?>

                            <hr>
                            <div class="d-flex justify-content-between align-items-center">
                                <strong>Total Mesa:</strong>
                                <strong class="text-primary fs-5">Q<?= number_format($total_mesa ?? 0, 0) ?></strong>
                            </div>

                            <?php if (($cuentas_abiertas ?? 0) === 0): ?>
                                <div class="alert alert-warning mt-3 mb-0">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    No hay cuentas abiertas para pagar.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <?php if (($cuentas_abiertas ?? 0) > 0): ?>
                    <button type="button" class="btn" id="confirmarPagarMesa" style="background-color: #1a365d; color: black; border-color: #1a365d;">
                        <i class="bi bi-credit-card me-2"></i>
                        Pagar Q<?= number_format($total_mesa ?? 0, 0) ?>
                    </button>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// JavaScript adicional
$extra_js = '
<script>
const hashMesa = "' . $hash . '";

document.addEventListener("DOMContentLoaded", function() {
    // Crear nueva cuenta
    document.getElementById("formNuevaCuenta").addEventListener("submit", function(e) {
        e.preventDefault();
        crearNuevaCuenta();
    });

    // Agregar orden a cuenta
    document.querySelectorAll(".agregar-orden-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const cuentaId = this.dataset.cuentaId;
            const cuentaNombre = this.dataset.cuentaNombre;
            agregarOrdenACuenta(cuentaId, cuentaNombre);
        });
    });

    // Ver detalle de cuenta
    document.querySelectorAll(".ver-detalle-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const cuentaId = this.dataset.cuentaId;
            verDetalleCuenta(cuentaId);
        });
    });

    // Generar ticket
    document.querySelectorAll(".generar-ticket-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const cuentaId = this.dataset.cuentaId;
            generarTicket(cuentaId);
        });
    });

    // Ajustes de cuenta (propina y descuentos)
    document.querySelectorAll(".ajustes-cuenta-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const cuentaId = this.dataset.cuentaId;
            const cuentaNombre = this.dataset.cuentaNombre;
            abrirAjustesCuenta(cuentaId, cuentaNombre);
        });
    });

    // Cerrar/Pagar cuenta
    document.querySelectorAll(".cerrar-cuenta-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const cuentaId = this.dataset.cuentaId;
            const cuentaNombre = this.dataset.cuentaNombre;
            // Procesar pago directamente sin confirmación
            pagarCuenta(cuentaId);
        });
    });

    // Pagar mesa completa
    const confirmarPagarMesaBtn = document.getElementById("confirmarPagarMesa");
    if (confirmarPagarMesaBtn) {
        confirmarPagarMesaBtn.addEventListener("click", function() {
            pagarMesaCompleta();
        });
    }
});

function crearNuevaCuenta() {
    const formData = new FormData(document.getElementById("formNuevaCuenta"));

    fetch("/Restaurante/api/crear_cuenta.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            hash_mesa: hashMesa,
            nombre_cliente: formData.get("nombre_cliente"),
            apellido_cliente: formData.get("apellido_cliente")
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast("Cuenta creada exitosamente", "success");
            setTimeout(() => location.reload(), 1500);
        } else {
            showToast("Error: " + data.message, "error");
        }
    })
    .catch(error => {
        console.error("Error:", error);
        showToast("Error de conexión", "error");
    });
}

function agregarOrdenACuenta(cuentaId, cuentaNombre) {
    // Redirigir a tomar orden con el ID de cuenta
    window.location.href = `/Restaurante/meseros/tomar_orden.php?hash=${hashMesa}&cuenta_id=${cuentaId}&cliente=${encodeURIComponent(cuentaNombre)}`;
}

function verDetalleCuenta(cuentaId) {
    // Redirigir a detalle de cuenta
    window.location.href = `/Restaurante/meseros/cuenta_detalle.php?cuenta_id=${cuentaId}`;
}

function generarTicket(cuentaId) {
    // Abrir ticket en nueva ventana
    const ticketUrl = "/Restaurante/imprimir_ticket.php?cuenta_id=" + cuentaId;
    const ticketWindow = window.open(ticketUrl, "ticket", "width=400,height=600,scrollbars=yes,resizable=yes");

    if (!ticketWindow) {
        showToast("Por favor, permita las ventanas emergentes para generar el ticket", "warning");
    }
}

function abrirAjustesCuenta(cuentaId, cuentaNombre) {
    // Abrir página de ajustes en nueva ventana
    const ajustesUrl = "/Restaurante/meseros/ajustes_cuenta.php?cuenta_id=" + cuentaId;
    const ajustesWindow = window.open(ajustesUrl, "ajustes", "width=800,height=700,scrollbars=yes,resizable=yes");

    if (!ajustesWindow) {
        showToast("Por favor, permita las ventanas emergentes para abrir los ajustes", "warning");
    } else {
        showToast(`Abriendo ajustes para ${cuentaNombre}`, "info");
    }
}

function pagarCuenta(cuentaId) {
    fetch("/Restaurante/api/cerrar_cuenta.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            cuenta_id: cuentaId,
            accion: "pagar"
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Generar ticket automáticamente después del pago
            generarTicketAutomatico(cuentaId);

            showToast("Cuenta pagada exitosamente", "success");
            setTimeout(() => location.reload(), 3000);
        } else {
            showToast("Error: " + data.message, "error");
        }
    })
    .catch(error => {
        console.error("Error:", error);
        showToast("Error de conexión", "error");
    });
}

function pagarMesaCompleta() {
    fetch("/Restaurante/api/pagar_mesa_completa.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            hash_mesa: hashMesa
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Generar ticket consolidado de la mesa
            generarTicketMesaCompleta(hashMesa);

            showToast(`Mesa pagada exitosamente. Total: Q${data.total_formateado}. Cuentas procesadas: ${data.cuentas_procesadas}`, "success");
            setTimeout(() => location.reload(), 3000);
        } else {
            showToast("Error: " + data.message, "error");
        }
    })
    .catch(error => {
        console.error("Error:", error);
        showToast("Error de conexión", "error");
    });
}

// Función para generar ticket automáticamente después del pago de una cuenta
function generarTicketAutomatico(cuentaId) {
    // Abrir ticket en nueva ventana
    const ticketUrl = "/Restaurante/imprimir_ticket.php?cuenta_id=" + cuentaId + "&auto_print=1";
    const ticketWindow = window.open(ticketUrl, "ticket", "width=400,height=600,scrollbars=yes,resizable=yes");

    if (!ticketWindow) {
        showToast("Por favor, permita las ventanas emergentes para generar el ticket", "warning");
    } else {
        showToast("Generando ticket para imprimir...", "info");
    }
}

// Función para generar ticket consolidado de mesa completa
function generarTicketMesaCompleta(hashMesa) {
    // Abrir ticket consolidado en nueva ventana
    const ticketUrl = "/Restaurante/imprimir_ticket_mesa.php?hash_mesa=" + hashMesa + "&auto_print=1";
    const ticketWindow = window.open(ticketUrl, "ticket_mesa", "width=400,height=700,scrollbars=yes,resizable=yes");

    if (!ticketWindow) {
        showToast("Por favor, permita las ventanas emergentes para generar el ticket", "warning");
    } else {
        showToast("Generando ticket consolidado de la mesa...", "info");
    }
}
</script>
';

// Incluir el layout
include '../includes/layout.php';
?>
