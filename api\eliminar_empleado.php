<?php
header('Content-Type: application/json');
session_start();

// Verificar que el usuario esté logueado y sea admin
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

require_once '../config/db.php';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden eliminar empleados']);
    exit();
}

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);
$id = $input['id'] ?? null;

// Validaciones
if (!$id) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID de empleado requerido']);
    exit();
}

// No permitir eliminar al propio usuario
if ($id == $_SESSION['usuario_id']) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'No puedes eliminar tu propia cuenta']);
    exit();
}

try {
    // Verificar que el empleado existe
    $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ?");
    $stmt->execute([$id]);
    $empleado = $stmt->fetch();
    
    if (!$empleado) {
        echo json_encode(['success' => false, 'message' => 'Empleado no encontrado']);
        exit();
    }
    
    // Iniciar transacción
    $pdo->beginTransaction();
    
    try {
        // Verificar si el empleado tiene órdenes asociadas
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM ordenes WHERE mesero_id = ?");
        $stmt->execute([$id]);
        $ordenes_count = $stmt->fetch()['total'];
        
        if ($ordenes_count > 0) {
            // Si tiene órdenes, solo desactivar en lugar de eliminar
            $stmt = $pdo->prepare("UPDATE usuarios SET activo = 0 WHERE id = ?");
            $stmt->execute([$id]);
            
            $pdo->commit();
            
            echo json_encode([
                'success' => true, 
                'message' => 'Empleado desactivado (tenía órdenes asociadas)',
                'empleado_id' => $id,
                'nombre' => $empleado['nombre'],
                'accion' => 'desactivado'
            ]);
        } else {
            // Si no tiene órdenes, eliminar completamente
            $stmt = $pdo->prepare("DELETE FROM usuarios WHERE id = ?");
            $stmt->execute([$id]);
            
            $pdo->commit();
            
            echo json_encode([
                'success' => true, 
                'message' => 'Empleado eliminado exitosamente',
                'empleado_id' => $id,
                'nombre' => $empleado['nombre'],
                'accion' => 'eliminado'
            ]);
        }
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
