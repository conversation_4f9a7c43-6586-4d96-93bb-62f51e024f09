<?php
// Configurar variables para el layout
$page_title = 'Reportes y Estadísticas';
$breadcrumbs = [
    ['title' => 'Inicio', 'url' => url('')],
    ['title' => 'Administración', 'url' => url('admin/')],
    ['title' => 'Reportes']
];

// Iniciar el buffer de contenido
ob_start();

// Conectar a la base de datos
require_once '../config/session_config.php';\nsession_start();
require_once '../config/db.php';

// Verificar que sea administrador
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    echo '<div class="alert alert-danger">Acceso denegado. Solo administradores pueden acceder.</div>';
    $content = ob_get_clean();
    include '../includes/layout.php';
    exit();
}

// Obtener estadísticas generales
try {
    // Ventas del día
    $stmt = $pdo->query("
        SELECT COUNT(*) as ordenes_hoy, COALESCE(SUM(total), 0) as ventas_hoy
        FROM ordenes
        WHERE DATE(fecha_hora) = CURDATE() AND estado = 'finalizada'
    ");
    $ventas_hoy = $stmt->fetch();

    // Ventas del mes
    $stmt = $pdo->query("
        SELECT COUNT(*) as ordenes_mes, COALESCE(SUM(total), 0) as ventas_mes
        FROM ordenes
        WHERE MONTH(fecha_hora) = MONTH(CURDATE()) AND YEAR(fecha_hora) = YEAR(CURDATE()) AND estado = 'finalizada'
    ");
    $ventas_mes = $stmt->fetch();

} catch (PDOException $e) {
    $ventas_hoy = ['ordenes_hoy' => 0, 'ventas_hoy' => 0];
    $ventas_mes = ['ordenes_mes' => 0, 'ventas_mes' => 0];
}
?>

<!-- Estadísticas Principales -->
<div class="row g-4 mb-4">
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-currency-dollar text-success fs-4"></i>
                </div>
                <h3 class="text-success mb-1">$<?= number_format($ventas_hoy['ventas_hoy'], 0) ?></h3>
                <p class="text-muted mb-0 small">Ventas Hoy</p>
                <small class="text-muted"><?= $ventas_hoy['ordenes_hoy'] ?> órdenes</small>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-calendar-month text-primary fs-4"></i>
                </div>
                <h3 class="text-primary mb-1">$<?= number_format($ventas_mes['ventas_mes'], 0) ?></h3>
                <p class="text-muted mb-0 small">Ventas del Mes</p>
                <small class="text-muted"><?= $ventas_mes['ordenes_mes'] ?> órdenes</small>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-graph-up text-info fs-4"></i>
                </div>
                <h3 class="text-info mb-1">
                    $<?= $ventas_hoy['ordenes_hoy'] > 0 ? number_format($ventas_hoy['ventas_hoy'] / $ventas_hoy['ordenes_hoy'], 0) : 0 ?>
                </h3>
                <p class="text-muted mb-0 small">Promedio por Orden</p>
                <small class="text-muted">Hoy</small>
            </div>
        </div>
    </div>
</div>

<!-- Filtros de reportes -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="bi bi-funnel me-2"></i>
                    Filtros de Reportes
                </h5>
            </div>
            <div class="card-body p-4">
                <form id="filtros-form">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Fecha Inicio</label>
                            <input type="date" class="form-control" id="fecha_inicio" value="<?= date('Y-m-d', strtotime('-7 days')) ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Fecha Fin</label>
                            <input type="date" class="form-control" id="fecha_fin" value="<?= date('Y-m-d') ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Mesero</label>
                            <select class="form-select" id="mesero_filtro">
                                <option value="">Todos los meseros</option>
                                <?php
                                $stmt = $pdo->query("SELECT id, nombre FROM usuarios WHERE rol = 'mesero' AND activo = 1 ORDER BY nombre");
                                $meseros = $stmt->fetchAll();
                                foreach ($meseros as $mesero):
                                ?>
                                    <option value="<?= $mesero['id'] ?>"><?= htmlspecialchars($mesero['nombre']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-primary" onclick="actualizarReportes()">
                                    <i class="bi bi-search me-2"></i>
                                    Generar Reportes
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Reportes principales -->
<div class="row g-4">
    <!-- Reporte de ventas por día -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-graph-up me-2"></i>
                        Ventas por Día
                    </h5>
                    <button class="btn btn-outline-primary btn-sm" onclick="exportarVentas()">
                        <i class="bi bi-download me-1"></i>
                        Exportar
                    </button>
                </div>
            </div>
            <div class="card-body p-4">
                <canvas id="ventasChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <!-- Top productos -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="bi bi-trophy me-2"></i>
                    Productos Más Vendidos
                </h5>
            </div>
            <div class="card-body p-0" style="max-height: 400px; overflow-y: auto;">
                <div id="top-productos-container">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reportes adicionales -->
<div class="row g-4 mt-2">
    <!-- Rendimiento de meseros -->
    <div class="col-lg-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="bi bi-people me-2"></i>
                    Rendimiento de Meseros
                </h5>
            </div>
            <div class="card-body p-0">
                <div id="meseros-rendimiento-container">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Análisis por categorías -->
    <div class="col-lg-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="bi bi-pie-chart me-2"></i>
                    Ventas por Área
                </h5>
            </div>
            <div class="card-body p-4">
                <canvas id="categoriasChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Reporte de eficiencia -->
<div class="row g-4 mt-2">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="bi bi-speedometer me-2"></i>
                    Análisis de Eficiencia
                </h5>
            </div>
            <div class="card-body p-4">
                <div id="eficiencia-container">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// CSS adicional
$extra_css = '
<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.spinner-border {
    width: 2rem;
    height: 2rem;
}

.list-group-item {
    transition: background-color 0.2s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.progress {
    height: 8px;
}

.badge-ranking {
    font-size: 0.7rem;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}
</style>
';

// JavaScript adicional
$extra_js = '
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let ventasChart = null;
let categoriasChart = null;

document.addEventListener("DOMContentLoaded", function() {
    // Cargar reportes iniciales
    actualizarReportes();
});

function actualizarReportes() {
    const fechaInicio = document.getElementById("fecha_inicio").value;
    const fechaFin = document.getElementById("fecha_fin").value;
    const meseroId = document.getElementById("mesero_filtro").value;

    // Mostrar spinners
    mostrarSpinners();

    // Cargar datos de ventas
    cargarVentasPorDia(fechaInicio, fechaFin, meseroId);

    // Cargar top productos
    cargarTopProductos(fechaInicio, fechaFin, meseroId);

    // Cargar rendimiento de meseros
    cargarRendimientoMeseros(fechaInicio, fechaFin);

    // Cargar ventas por categorías
    cargarVentasCategorias(fechaInicio, fechaFin, meseroId);

    // Cargar análisis de eficiencia
    cargarAnalisisEficiencia(fechaInicio, fechaFin);
}

function mostrarSpinners() {
    document.getElementById("top-productos-container").innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Cargando...</span>
            </div>
        </div>
    `;

    document.getElementById("meseros-rendimiento-container").innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Cargando...</span>
            </div>
        </div>
    `;

    document.getElementById("eficiencia-container").innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Cargando...</span>
            </div>
        </div>
    `;
}

function cargarVentasPorDia(fechaInicio, fechaFin, meseroId) {
    const params = new URLSearchParams({
        fecha_inicio: fechaInicio,
        fecha_fin: fechaFin,
        mesero_id: meseroId || ""
    });

    fetch(`/Restaurante/api/reportes_ventas.php?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                actualizarGraficoVentas(data.ventas);
            }
        })
        .catch(error => console.error("Error:", error));
}

function cargarTopProductos(fechaInicio, fechaFin, meseroId) {
    const params = new URLSearchParams({
        fecha_inicio: fechaInicio,
        fecha_fin: fechaFin,
        mesero_id: meseroId || ""
    });

    fetch(`/Restaurante/api/reportes_productos.php?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                mostrarTopProductos(data.productos);
            }
        })
        .catch(error => console.error("Error:", error));
}

function cargarRendimientoMeseros(fechaInicio, fechaFin) {
    const params = new URLSearchParams({
        fecha_inicio: fechaInicio,
        fecha_fin: fechaFin
    });

    fetch(`/Restaurante/api/reportes_meseros.php?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                mostrarRendimientoMeseros(data.meseros);
            }
        })
        .catch(error => console.error("Error:", error));
}

function cargarVentasCategorias(fechaInicio, fechaFin, meseroId) {
    const params = new URLSearchParams({
        fecha_inicio: fechaInicio,
        fecha_fin: fechaFin,
        mesero_id: meseroId || ""
    });

    fetch(`/Restaurante/api/reportes_categorias.php?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                actualizarGraficoCategorias(data.categorias);
            }
        })
        .catch(error => console.error("Error:", error));
}

function cargarAnalisisEficiencia(fechaInicio, fechaFin) {
    const params = new URLSearchParams({
        fecha_inicio: fechaInicio,
        fecha_fin: fechaFin
    });

    fetch(`/Restaurante/api/reportes_eficiencia.php?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                mostrarAnalisisEficiencia(data.eficiencia);
            }
        })
        .catch(error => console.error("Error:", error));
}

function actualizarGraficoVentas(ventas) {
    const ctx = document.getElementById("ventasChart").getContext("2d");

    if (ventasChart) {
        ventasChart.destroy();
    }

    const labels = ventas.map(item => {
        const fecha = new Date(item.fecha);
        return fecha.toLocaleDateString("es-ES", {
            weekday: "short",
            day: "numeric",
            month: "short"
        });
    });

    const data = ventas.map(item => parseFloat(item.total));

    ventasChart = new Chart(ctx, {
        type: "line",
        data: {
            labels: labels,
            datasets: [{
                label: "Ventas ($)",
                data: data,
                borderColor: "#0d6efd",
                backgroundColor: "rgba(13, 110, 253, 0.1)",
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: "#0d6efd",
                pointBorderColor: "#ffffff",
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return "$" + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}

function mostrarTopProductos(productos) {
    let html = "";

    if (productos.length === 0) {
        html = `
            <div class="text-center py-5">
                <i class="bi bi-info-circle text-muted fs-1 mb-3 d-block"></i>
                <h6 class="text-muted">No hay datos disponibles</h6>
            </div>
        `;
    } else {
        html = `<div class="list-group list-group-flush">`;
        productos.forEach((producto, index) => {
            const rankingColors = ["primary", "success", "dark", "info", "secondary"];
            const color = rankingColors[index] || "secondary";

            html += `
                <div class="list-group-item border-0 py-3">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <span class="badge bg-${color} badge-ranking">${index + 1}</span>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">${producto.nombre}</h6>
                            <small class="text-muted">
                                ${producto.cantidad_vendida} vendidos -
                                $${parseInt(producto.ingresos_totales).toLocaleString()}
                            </small>
                        </div>
                    </div>
                </div>
            `;
        });
        html += `</div>`;
    }

    document.getElementById("top-productos-container").innerHTML = html;
}

function mostrarRendimientoMeseros(meseros) {
    let html = "";

    if (meseros.length === 0) {
        html = `
            <div class="text-center py-5">
                <i class="bi bi-info-circle text-muted fs-1 mb-3 d-block"></i>
                <h6 class="text-muted">No hay datos disponibles</h6>
            </div>
        `;
    } else {
        html = `<div class="list-group list-group-flush">`;
        meseros.forEach(mesero => {
            const eficiencia = Math.min(100, (mesero.ventas_totales / 100000) * 100); // Base 100k

            html += `
                <div class="list-group-item border-0 py-3">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div>
                            <h6 class="mb-1">${mesero.nombre}</h6>
                            <small class="text-muted">
                                ${mesero.total_ordenes} órdenes -
                                $${parseInt(mesero.ventas_totales).toLocaleString()}
                            </small>
                        </div>
                        <span class="badge bg-primary rounded-pill">${Math.round(eficiencia)}%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: ${eficiencia}%"></div>
                    </div>
                </div>
            `;
        });
        html += `</div>`;
    }

    document.getElementById("meseros-rendimiento-container").innerHTML = html;
}

function actualizarGraficoCategorias(categorias) {
    const ctx = document.getElementById("categoriasChart").getContext("2d");

    if (categoriasChart) {
        categoriasChart.destroy();
    }

    const labels = categorias.map(cat => cat.area === "cocina" ? "Cocina" : "Bebidas");
    const data = categorias.map(cat => parseFloat(cat.total_ventas));
    const colors = ["#dc3545", "#0dcaf0"]; // Rojo para cocina, azul para bebidas

    categoriasChart = new Chart(ctx, {
        type: "doughnut",
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors,
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: "bottom"
                }
            }
        }
    });
}

function mostrarAnalisisEficiencia(eficiencia) {
    const html = `
        <div class="row g-4">
            <div class="col-md-3">
                <div class="text-center">
                    <h3 class="text-primary mb-1">${eficiencia.tiempo_promedio_cocina || 0} min</h3>
                    <p class="text-muted mb-0 small">Tiempo Promedio Cocina</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <h3 class="text-info mb-1">${eficiencia.tiempo_promedio_bebidas || 0} min</h3>
                    <p class="text-muted mb-0 small">Tiempo Promedio Bebidas</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <h3 class="text-success mb-1">${eficiencia.ordenes_completadas || 0}</h3>
                    <p class="text-muted mb-0 small">Órdenes Completadas</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <h3 class="text-dark mb-1">${Math.round(eficiencia.eficiencia_general || 0)}%</h3>
                    <p class="text-muted mb-0 small">Eficiencia General</p>
                </div>
            </div>
        </div>
    `;

    document.getElementById("eficiencia-container").innerHTML = html;
}

function exportarVentas() {
    const fechaInicio = document.getElementById("fecha_inicio").value;
    const fechaFin = document.getElementById("fecha_fin").value;
    const meseroId = document.getElementById("mesero_filtro").value;

    const params = new URLSearchParams({
        fecha_inicio: fechaInicio,
        fecha_fin: fechaFin,
        mesero_id: meseroId || "",
        export: "csv"
    });

    window.open(`/Restaurante/api/reportes_ventas.php?${params}`, "_blank");
}
</script>
';

// Incluir el layout
include '../includes/layout.php';
?>