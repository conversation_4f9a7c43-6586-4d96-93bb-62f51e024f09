<?php
// Iniciar sesión si no está iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once '../config/db.php';

// Verificar autenticación
if (!isset($_SESSION['usuario_id'])) {
    header('Location: /Restaurante/auth/login.php');
    exit();
}

// Obtener datos del usuario actual y verificar que sea administrador
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario_actual = $stmt->fetch();

if (!$usuario_actual) {
    // Si no es admin, redirigir al dashboard principal
    header('Location: /Restaurante/index.php');
    exit();
}

// Variables para el layout
$page_title = 'Registro General de Órdenes';
$breadcrumbs = [
    ['title' => 'Inicio', 'url' => '/Restaurante/'],
    ['title' => 'Administración', 'url' => '/Restaurante/admin/'],
    ['title' => 'Registro de Órdenes']
];

// Iniciar captura de contenido
ob_start();

try {
    // Obtener filtros
    $fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-m-d');
    $fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-d');
    $estado_filtro = $_GET['estado'] ?? '';
    $mesa_filtro = $_GET['mesa'] ?? '';
    $mesero_filtro = $_GET['mesero'] ?? '';
    
    // Construir consulta con filtros
    $where_conditions = ["DATE(o.fecha_hora) BETWEEN ? AND ?"];
    $params = [$fecha_inicio, $fecha_fin];
    
    if (!empty($estado_filtro)) {
        $where_conditions[] = "o.estado = ?";
        $params[] = $estado_filtro;
    }
    
    if (!empty($mesa_filtro)) {
        $where_conditions[] = "m.numero_mesa = ?";
        $params[] = $mesa_filtro;
    }
    
    if (!empty($mesero_filtro)) {
        $where_conditions[] = "u.id = ?";
        $params[] = $mesero_filtro;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Obtener órdenes con información completa
    $stmt = $pdo->prepare("
        SELECT o.id, o.cuenta_id, o.total, o.estado, o.fecha_hora,
               m.numero_mesa,
               u.nombre as mesero_nombre,
               c.nombre_cliente, c.apellido_cliente,
               COUNT(d.id) as total_productos,
               COALESCE(SUM(d.cantidad), 0) as total_items
        FROM ordenes o
        INNER JOIN cuentas c ON o.cuenta_id = c.id
        INNER JOIN mesas m ON c.hash_mesa = m.hash_mesa
        INNER JOIN usuarios u ON c.mesero_id = u.id
        LEFT JOIN detalle_orden d ON o.id = d.orden_id
        WHERE $where_clause
        GROUP BY o.id, o.fecha_hora, o.total, o.estado, o.cuenta_id,
                 m.numero_mesa, u.nombre, c.nombre_cliente, c.apellido_cliente
        ORDER BY o.fecha_hora DESC
    ");
    $stmt->execute($params);
    $ordenes = $stmt->fetchAll();
    
    // Obtener estadísticas generales
    $stmt = $pdo->prepare("
        SELECT
            COUNT(*) as total_ordenes,
            COALESCE(SUM(o.total), 0) as total_ventas,
            COUNT(CASE WHEN o.estado = 'pendiente' THEN 1 END) as pendientes,
            COUNT(CASE WHEN o.estado = 'preparando' THEN 1 END) as preparando,
            COUNT(CASE WHEN o.estado = 'listo' THEN 1 END) as listos,
            COUNT(CASE WHEN o.estado = 'servido' THEN 1 END) as servidos
        FROM ordenes o
        INNER JOIN cuentas c ON o.cuenta_id = c.id
        WHERE $where_clause
    ");
    $stmt->execute($params);
    $estadisticas = $stmt->fetch();
    
    // Obtener lista de meseros para filtro
    $stmt = $pdo->query("SELECT id, nombre FROM usuarios WHERE rol = 'mesero' AND activo = 1 ORDER BY nombre");
    $meseros = $stmt->fetchAll();
    
    // Obtener lista de mesas para filtro
    $stmt = $pdo->query("SELECT DISTINCT numero_mesa FROM mesas ORDER BY numero_mesa");
    $mesas = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $ordenes = [];
    $estadisticas = [
        'total_ordenes' => 0,
        'total_ventas' => 0,
        'pendientes' => 0,
        'preparando' => 0,
        'listos' => 0,
        'servidos' => 0
    ];
    $meseros = [];
    $mesas = [];
    echo '<div class="alert alert-danger">Error de base de datos: ' . $e->getMessage() . '</div>';
}
?>

<!-- Estadísticas Generales -->
<div class="ordenes-estadisticas-container mb-4">
    <div class="ordenes-estadisticas-header">
        <div class="ordenes-estadisticas-title">
            <span class="ordenes-icon">📊</span>
            <h4>Registro General de Órdenes</h4>
        </div>
        <div class="fecha-rango">
            📅 <?= date('d/m/Y', strtotime($fecha_inicio)) ?> - <?= date('d/m/Y', strtotime($fecha_fin)) ?>
        </div>
    </div>
    
    <div class="ordenes-estadisticas-grid">
        <div class="stat-card stat-total">
            <div class="stat-icon">📋</div>
            <div class="stat-info">
                <span class="stat-numero"><?= $estadisticas['total_ordenes'] ?></span>
                <span class="stat-label">Total Órdenes</span>
            </div>
        </div>
        
        <div class="stat-card stat-ventas">
            <div class="stat-icon">💰</div>
            <div class="stat-info">
                <span class="stat-numero">Q<?= number_format($estadisticas['total_ventas'], 0) ?></span>
                <span class="stat-label">Total Ventas</span>
            </div>
        </div>
        
        <div class="stat-card stat-pendientes">
            <div class="stat-icon">⏳</div>
            <div class="stat-info">
                <span class="stat-numero"><?= $estadisticas['pendientes'] ?></span>
                <span class="stat-label">Pendientes</span>
            </div>
        </div>
        
        <div class="stat-card stat-preparando">
            <div class="stat-icon">🔄</div>
            <div class="stat-info">
                <span class="stat-numero"><?= $estadisticas['preparando'] ?></span>
                <span class="stat-label">Preparando</span>
            </div>
        </div>
        
        <div class="stat-card stat-listos">
            <div class="stat-icon">✅</div>
            <div class="stat-info">
                <span class="stat-numero"><?= $estadisticas['listos'] ?></span>
                <span class="stat-label">Listos</span>
            </div>
        </div>
        
        <div class="stat-card stat-servidos">
            <div class="stat-icon">🍽️</div>
            <div class="stat-info">
                <span class="stat-numero"><?= $estadisticas['servidos'] ?></span>
                <span class="stat-label">Servidos</span>
            </div>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="ordenes-filtros-container mb-4">
    <div class="ordenes-filtros-header">
        <div class="filtros-title">
            <span class="filtros-icon">🔍</span>
            <h5>Filtros de Búsqueda</h5>
        </div>
    </div>
    
    <div class="ordenes-filtros-content">
        <form method="GET" class="filtros-form">
            <div class="filtros-grid">
                <div class="filtro-item">
                    <label for="fecha_inicio">📅 Fecha Inicio:</label>
                    <input type="date" id="fecha_inicio" name="fecha_inicio" value="<?= $fecha_inicio ?>" class="filtro-input">
                </div>
                
                <div class="filtro-item">
                    <label for="fecha_fin">📅 Fecha Fin:</label>
                    <input type="date" id="fecha_fin" name="fecha_fin" value="<?= $fecha_fin ?>" class="filtro-input">
                </div>
                
                <div class="filtro-item">
                    <label for="estado">🏷️ Estado:</label>
                    <select id="estado" name="estado" class="filtro-select">
                        <option value="">Todos los estados</option>
                        <option value="pendiente" <?= $estado_filtro === 'pendiente' ? 'selected' : '' ?>>⏳ Pendiente</option>
                        <option value="preparando" <?= $estado_filtro === 'preparando' ? 'selected' : '' ?>>🔄 Preparando</option>
                        <option value="listo" <?= $estado_filtro === 'listo' ? 'selected' : '' ?>>✅ Listo</option>
                        <option value="servido" <?= $estado_filtro === 'servido' ? 'selected' : '' ?>>🍽️ Servido</option>
                    </select>
                </div>
                
                <div class="filtro-item">
                    <label for="mesa">🪑 Mesa:</label>
                    <select id="mesa" name="mesa" class="filtro-select">
                        <option value="">Todas las mesas</option>
                        <?php foreach ($mesas as $mesa): ?>
                            <option value="<?= $mesa['numero_mesa'] ?>" <?= $mesa_filtro == $mesa['numero_mesa'] ? 'selected' : '' ?>>
                                Mesa #<?= $mesa['numero_mesa'] ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filtro-item">
                    <label for="mesero">👤 Mesero:</label>
                    <select id="mesero" name="mesero" class="filtro-select">
                        <option value="">Todos los meseros</option>
                        <?php foreach ($meseros as $mesero): ?>
                            <option value="<?= $mesero['id'] ?>" <?= $mesero_filtro == $mesero['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($mesero['nombre']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filtro-item filtro-acciones">
                    <button type="submit" class="btn-filtrar">🔍 Filtrar</button>
                    <a href="?" class="btn-limpiar">🗑️ Limpiar</a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Lista de Órdenes -->
<div class="ordenes-lista-container">
    <div class="ordenes-lista-header">
        <div class="ordenes-lista-title">
            <span class="lista-icon">📋</span>
            <h4>Órdenes Registradas (<?= count($ordenes) ?>)</h4>
        </div>
        <div class="ordenes-acciones">
            <button class="btn-exportar" onclick="exportarOrdenes()">
                📊 Exportar Excel
            </button>
            <button class="btn-actualizar" onclick="location.reload()">
                🔄 Actualizar
            </button>
        </div>
    </div>

    <div class="ordenes-lista-content">
        <?php if (empty($ordenes)): ?>
            <div class="ordenes-empty">
                <span class="empty-icon">📋</span>
                <h5>No se encontraron órdenes</h5>
                <p>No hay órdenes que coincidan con los filtros seleccionados</p>
            </div>
        <?php else: ?>
            <div class="ordenes-grid">
                <?php foreach ($ordenes as $orden): ?>
                    <div class="orden-card">
                        <div class="orden-header">
                            <div class="orden-info">
                                <span class="orden-id">#<?= $orden['id'] ?></span>
                                <span class="orden-fecha">
                                    🕐 <?= date('d/m/Y H:i', strtotime($orden['fecha_hora'])) ?>
                                </span>
                            </div>
                            <div class="orden-estado">
                                <span class="estado-badge estado-<?= $orden['estado'] ?>">
                                    <?php
                                    $estados_iconos = [
                                        'pendiente' => '⏳ Pendiente',
                                        'preparando' => '🔄 Preparando',
                                        'listo' => '✅ Listo',
                                        'servido' => '🍽️ Servido'
                                    ];
                                    echo $estados_iconos[$orden['estado']] ?? $orden['estado'];
                                    ?>
                                </span>
                            </div>
                        </div>

                        <div class="orden-detalles">
                            <div class="detalle-item">
                                <span class="detalle-label">🪑 Mesa:</span>
                                <span class="detalle-valor">#<?= $orden['numero_mesa'] ?></span>
                            </div>
                            <div class="detalle-item">
                                <span class="detalle-label">👤 Cliente:</span>
                                <span class="detalle-valor">
                                    <?= htmlspecialchars($orden['nombre_cliente']) ?>
                                    <?php if (!empty($orden['apellido_cliente'])): ?>
                                        <?= htmlspecialchars($orden['apellido_cliente']) ?>
                                    <?php endif; ?>
                                </span>
                            </div>
                            <div class="detalle-item">
                                <span class="detalle-label">🍽️ Mesero:</span>
                                <span class="detalle-valor"><?= htmlspecialchars($orden['mesero_nombre']) ?></span>
                            </div>
                        </div>

                        <div class="orden-estadisticas">
                            <div class="stat-item">
                                <span class="stat-numero"><?= $orden['total_productos'] ?></span>
                                <span class="stat-label">📦 Productos</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-numero"><?= $orden['total_items'] ?></span>
                                <span class="stat-label">🔢 Items</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-numero">Q<?= number_format($orden['total'], 0) ?></span>
                                <span class="stat-label">💰 Total</span>
                            </div>
                        </div>

                        <div class="orden-acciones">
                            <button class="btn-accion btn-ver ver-orden-btn"
                                    data-orden-id="<?= $orden['id'] ?>">
                                👁️ Ver Detalle
                            </button>
                            <button class="btn-accion btn-ticket generar-ticket-btn"
                                    data-cuenta-id="<?= $orden['cuenta_id'] ?>">
                                🧾 Ticket
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* Estilos para estadísticas de órdenes */
.ordenes-estadisticas-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.ordenes-estadisticas-header {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    padding: 20px;
    border-bottom: 2px solid #90caf9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ordenes-estadisticas-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.ordenes-icon {
    font-size: 24px;
}

.ordenes-estadisticas-title h4 {
    margin: 0;
    color: #1565c0;
    font-weight: 600;
}

.fecha-rango {
    background: #ffffff;
    color: #1565c0;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 500;
    border: 2px solid #90caf9;
}

.ordenes-estadisticas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 0;
}

.stat-card {
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    border-right: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.stat-card:last-child {
    border-right: none;
}

.stat-card:hover {
    background: #f8f9fa;
}

.stat-icon {
    font-size: 32px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: #e3f2fd;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-numero {
    font-size: 24px;
    font-weight: 700;
    color: #212529;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
    margin-top: 4px;
}

/* Estilos para filtros */
.ordenes-filtros-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.ordenes-filtros-header {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    padding: 16px 20px;
    border-bottom: 2px solid #ce93d8;
}

.filtros-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.filtros-icon {
    font-size: 20px;
}

.filtros-title h5 {
    margin: 0;
    color: #7b1fa2;
    font-weight: 600;
}

.ordenes-filtros-content {
    padding: 20px;
}

.filtros-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    align-items: end;
}

.filtro-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filtro-item label {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.filtro-input, .filtro-select {
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.filtro-input:focus, .filtro-select:focus {
    outline: none;
    border-color: #7b1fa2;
    box-shadow: 0 0 0 3px rgba(123, 31, 162, 0.1);
}

.filtro-acciones {
    display: flex;
    gap: 8px;
}

.btn-filtrar, .btn-limpiar {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    text-align: center;
    font-size: 14px;
}

.btn-filtrar {
    background: #7b1fa2;
    color: white;
}

.btn-filtrar:hover {
    background: #6a1b9a;
}

.btn-limpiar {
    background: #6c757d;
    color: white;
}

.btn-limpiar:hover {
    background: #5a6268;
}

/* Estilos para lista de órdenes */
.ordenes-lista-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.ordenes-lista-header {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    padding: 20px;
    border-bottom: 2px solid #a5d6a7;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ordenes-lista-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.lista-icon {
    font-size: 24px;
}

.ordenes-lista-title h4 {
    margin: 0;
    color: #2e7d32;
    font-weight: 600;
}

.ordenes-acciones {
    display: flex;
    gap: 12px;
}

.btn-exportar, .btn-actualizar {
    background: #4caf50;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-exportar:hover, .btn-actualizar:hover {
    background: #45a049;
    transform: translateY(-1px);
}

.ordenes-lista-content {
    padding: 20px;
}

.ordenes-empty {
    text-align: center;
    padding: 40px 20px;
}

.empty-icon {
    font-size: 48px;
    display: block;
    margin-bottom: 16px;
}

.ordenes-empty h5 {
    color: #6c757d;
    margin: 0 0 8px 0;
}

.ordenes-empty p {
    color: #6c757d;
    margin-bottom: 20px;
}

.ordenes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.orden-card {
    background: #ffffff;
    border: 2px solid #e8f5e8;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
}

.orden-card:hover {
    border-color: #4caf50;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.15);
    transform: translateY(-2px);
}

.orden-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.orden-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.orden-id {
    font-weight: 700;
    color: #2e7d32;
    font-size: 16px;
}

.orden-fecha {
    color: #6c757d;
    font-size: 14px;
}

.orden-estado {
    align-self: flex-start;
}

.estado-badge {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.estado-pendiente {
    background: #fff3cd;
    color: #856404;
}

.estado-preparando {
    background: #d1ecf1;
    color: #0c5460;
}

.estado-listo {
    background: #d4edda;
    color: #155724;
}

.estado-servido {
    background: #e2e3e5;
    color: #383d41;
}

.orden-detalles {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
    margin-bottom: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
}

.detalle-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detalle-label {
    font-size: 14px;
    color: #6c757d;
}

.detalle-valor {
    font-weight: 600;
    color: #212529;
}

.orden-estadisticas {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
}

.orden-estadisticas .stat-item {
    text-align: center;
}

.orden-estadisticas .stat-numero {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: #2e7d32;
}

.orden-estadisticas .stat-label {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
}

.orden-acciones {
    display: flex;
    gap: 8px;
}

.btn-accion {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    text-align: center;
}

.btn-ver {
    background: #2196f3;
    color: white;
}

.btn-ver:hover {
    background: #1976d2;
}

.btn-ticket {
    background: #ff9800;
    color: white;
}

.btn-ticket:hover {
    background: #f57c00;
}

@media (max-width: 768px) {
    .ordenes-estadisticas-header,
    .ordenes-filtros-header,
    .ordenes-lista-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }

    .ordenes-estadisticas-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .filtros-grid {
        grid-template-columns: 1fr;
    }

    .ordenes-grid {
        grid-template-columns: 1fr;
    }

    .orden-header {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .orden-estadisticas {
        grid-template-columns: 1fr;
    }

    .orden-acciones {
        flex-direction: column;
    }
}
</style>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// JavaScript adicional
$extra_js = '
<script>
document.addEventListener("DOMContentLoaded", function() {
    // Ver detalle de orden
    document.querySelectorAll(".ver-orden-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const ordenId = this.dataset.ordenId;
            verDetalleOrden(ordenId);
        });
    });

    // Generar ticket
    document.querySelectorAll(".generar-ticket-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const cuentaId = this.dataset.cuentaId;
            generarTicket(cuentaId);
        });
    });
});

function verDetalleOrden(ordenId) {
    // Redirigir a la página de detalle de orden
    window.location.href = `/Restaurante/meseros/orden_detalle.php?id=${ordenId}`;
}

function generarTicket(cuentaId) {
    // Abrir ticket en nueva ventana
    const ticketUrl = "/Restaurante/imprimir_ticket.php?cuenta_id=" + cuentaId;
    const ticketWindow = window.open(ticketUrl, "ticket", "width=400,height=600,scrollbars=yes,resizable=yes");

    if (!ticketWindow) {
        showToast("Por favor, permita las ventanas emergentes para generar el ticket", "warning");
    }
}

function exportarOrdenes() {
    // Obtener parámetros actuales de filtro
    const urlParams = new URLSearchParams(window.location.search);
    const exportUrl = "/Restaurante/api/exportar_ordenes.php?" + urlParams.toString();

    // Crear enlace temporal para descarga
    const link = document.createElement("a");
    link.href = exportUrl;
    link.download = "ordenes_" + new Date().toISOString().split("T")[0] + ".xlsx";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showToast("Exportando órdenes...", "info");
}

// Función para mostrar notificaciones toast
function showToast(message, type = "info") {
    // Crear contenedor de toasts si no existe
    let toastContainer = document.getElementById("toast-container");
    if (!toastContainer) {
        toastContainer = document.createElement("div");
        toastContainer.id = "toast-container";
        toastContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            display: flex;
            flex-direction: column;
            gap: 10px;
        `;
        document.body.appendChild(toastContainer);
    }

    // Crear elemento toast
    const toast = document.createElement("div");
    const bgColor = type === "success" ? "#d4edda" : type === "error" ? "#f8d7da" : type === "warning" ? "#fff3cd" : "#d1ecf1";
    const textColor = type === "success" ? "#155724" : type === "error" ? "#721c24" : type === "warning" ? "#856404" : "#0c5460";
    const icon = type === "success" ? "✅" : type === "error" ? "❌" : type === "warning" ? "⚠️" : "ℹ️";

    toast.style.cssText = `
        background: ${bgColor};
        color: ${textColor};
        padding: 12px 20px;
        border-radius: 8px;
        border: 1px solid ${type === "success" ? "#c3e6cb" : type === "error" ? "#f5c6cb" : type === "warning" ? "#ffeaa7" : "#bee5eb"};
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        animation: slideInRight 0.3s ease-out;
        font-weight: 500;
    `;

    toast.innerHTML = `${icon} ${message}`;

    // Agregar animación CSS
    if (!document.getElementById("toast-animations")) {
        const style = document.createElement("style");
        style.id = "toast-animations";
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    toastContainer.appendChild(toast);

    // Remover después de 4 segundos
    setTimeout(() => {
        toast.style.animation = "slideOutRight 0.3s ease-in";
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 4000);
}
</script>
';

// Incluir el layout
include '../includes/layout.php';
?>
