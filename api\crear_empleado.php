<?php
header('Content-Type: application/json');
session_start();

// Verificar que el usuario esté logueado y sea admin
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

require_once '../config/db.php';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden crear empleados']);
    exit();
}

// Obtener datos del JSON
$input = json_decode(file_get_contents('php://input'), true);

$nombre = $input['nombre'] ?? '';
$correo = $input['correo'] ?? '';
$rol = $input['rol'] ?? '';
$password = $input['password'] ?? '1234';
$activo = $input['activo'] ?? 1;

// Validaciones
if (empty($nombre) || empty($correo) || empty($rol)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Todos los campos son obligatorios']);
    exit();
}

$roles_validos = ['admin', 'mesero', 'cocina', 'bebidas'];
if (!in_array($rol, $roles_validos)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Rol inválido']);
    exit();
}

if (!filter_var($correo, FILTER_VALIDATE_EMAIL)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Correo electrónico inválido']);
    exit();
}

try {
    // Verificar que el correo no exista
    $stmt = $pdo->prepare("SELECT id FROM usuarios WHERE correo = ?");
    $stmt->execute([$correo]);
    if ($stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'El correo electrónico ya está registrado']);
        exit();
    }
    
    // Hashear la contraseña con MD5
    $password_hash = md5($password);
    
    // Insertar el nuevo empleado
    $stmt = $pdo->prepare("
        INSERT INTO usuarios (nombre, correo, password, rol, activo)
        VALUES (?, ?, ?, ?, ?)
    ");

    $stmt->execute([$nombre, $correo, $password_hash, $rol, $activo]);
    $nuevo_id = $pdo->lastInsertId();
    
    echo json_encode([
        'success' => true, 
        'message' => 'Empleado creado exitosamente',
        'empleado_id' => $nuevo_id,
        'nombre' => $nombre,
        'rol' => $rol
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
