<?php
header('Content-Type: application/json');

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();

// Verificar que el usuario esté logueado y sea admin
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

require_once '../config/db.php';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden editar empleados']);
    exit();
}

// Obtener datos del JSON
$input = json_decode(file_get_contents('php://input'), true);

$id = $input['id'] ?? '';
$nombre = $input['nombre'] ?? '';
$correo = $input['correo'] ?? '';
$rol = $input['rol'] ?? '';
$password = $input['password'] ?? '';
$activo = $input['activo'] ?? 1;

// Validaciones
if (empty($id) || empty($nombre) || empty($correo) || empty($rol)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Todos los campos son obligatorios']);
    exit();
}

$roles_validos = ['admin', 'mesero', 'cocina', 'bebidas'];
if (!in_array($rol, $roles_validos)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Rol inválido']);
    exit();
}

if (!filter_var($correo, FILTER_VALIDATE_EMAIL)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Correo electrónico inválido']);
    exit();
}

try {
    // Verificar que el empleado existe
    $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ?");
    $stmt->execute([$id]);
    $empleado = $stmt->fetch();
    
    if (!$empleado) {
        echo json_encode(['success' => false, 'message' => 'Empleado no encontrado']);
        exit();
    }
    
    // Verificar que el correo no esté en uso por otro usuario
    $stmt = $pdo->prepare("SELECT id FROM usuarios WHERE correo = ? AND id != ?");
    $stmt->execute([$correo, $id]);
    if ($stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'El correo electrónico ya está en uso por otro empleado']);
        exit();
    }
    
    // Preparar la consulta de actualización
    if (!empty($password)) {
        // Actualizar con nueva contraseña
        $password_hash = md5($password);
        $stmt = $pdo->prepare("
            UPDATE usuarios
            SET nombre = ?, correo = ?, password = ?, rol = ?, activo = ?
            WHERE id = ?
        ");
        $stmt->execute([$nombre, $correo, $password_hash, $rol, $activo, $id]);
    } else {
        // Actualizar sin cambiar contraseña
        $stmt = $pdo->prepare("
            UPDATE usuarios
            SET nombre = ?, correo = ?, rol = ?, activo = ?
            WHERE id = ?
        ");
        $stmt->execute([$nombre, $correo, $rol, $activo, $id]);
    }
    
    echo json_encode([
        'success' => true, 
        'message' => 'Empleado actualizado exitosamente',
        'empleado_id' => $id,
        'nombre' => $nombre,
        'rol' => $rol
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
