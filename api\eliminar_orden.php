<?php
header('Content-Type: application/json');
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Verificar que sea un mesero o administrador
require_once '../config/db.php';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol IN ('mesero', 'admin')");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo meseros y administradores pueden eliminar órdenes']);
    exit();
}

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);
$orden_id = $input['orden_id'] ?? null;

if (!$orden_id || !is_numeric($orden_id)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID de orden inválido']);
    exit();
}

try {
    // Verificar que la orden existe y pertenece al mesero (si no es admin)
    if ($usuario['rol'] === 'mesero') {
        $stmt = $pdo->prepare("SELECT * FROM ordenes WHERE id = ? AND mesero_id = ?");
        $stmt->execute([$orden_id, $usuario['id']]);
    } else {
        $stmt = $pdo->prepare("SELECT * FROM ordenes WHERE id = ?");
        $stmt->execute([$orden_id]);
    }
    
    $orden = $stmt->fetch();
    
    if (!$orden) {
        echo json_encode(['success' => false, 'message' => 'Orden no encontrada o no tienes permisos']);
        exit();
    }
    
    if ($orden['estado'] === 'finalizada') {
        echo json_encode(['success' => false, 'message' => 'No se puede eliminar una orden finalizada']);
        exit();
    }
    
    // Iniciar transacción
    $pdo->beginTransaction();
    
    try {
        // Eliminar detalles de la orden
        $stmt = $pdo->prepare("DELETE FROM detalle_orden WHERE orden_id = ?");
        $stmt->execute([$orden_id]);
        
        // Eliminar la orden
        $stmt = $pdo->prepare("DELETE FROM ordenes WHERE id = ?");
        $stmt->execute([$orden_id]);
        
        // Confirmar transacción
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Orden eliminada exitosamente',
            'orden_id' => $orden_id
        ]);
        
    } catch (Exception $e) {
        // Revertir transacción en caso de error
        $pdo->rollBack();
        throw $e;
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
