<?php
header('Content-Type: application/json');

// Verificar que sea una petición POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit();
}


// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();
require_once '../config/db.php';

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Verificar que sea administrador
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden crear productos']);
    exit();
}

// Obtener datos del POST
$nombre = trim($_POST['nombre'] ?? '');
$descripcion = trim($_POST['descripcion'] ?? '');
$precio_costo = $_POST['precio_costo'] ?? '';
$precio_venta = $_POST['precio_venta'] ?? '';
$categoria_id = $_POST['categoria_id'] ?? null;
$area = $_POST['area'] ?? '';
$disponible = isset($_POST['disponible']) ? 1 : 0;

// Validaciones mejoradas
if (empty($nombre)) {
    echo json_encode(['success' => false, 'message' => 'El nombre es requerido']);
    exit();
}

if (empty($precio_costo) || !is_numeric($precio_costo) || $precio_costo < 0) {
    echo json_encode(['success' => false, 'message' => 'El precio de costo debe ser un número válido mayor o igual a 0']);
    exit();
}

if (empty($precio_venta) || !is_numeric($precio_venta) || $precio_venta <= 0) {
    echo json_encode(['success' => false, 'message' => 'El precio de venta debe ser un número válido mayor a 0']);
    exit();
}

if (empty($area) || !in_array($area, ['cocina', 'bebidas'])) {
    echo json_encode(['success' => false, 'message' => 'Debe seleccionar un área válida (cocina o bebidas)']);
    exit();
}

if (strlen($nombre) > 100) {
    echo json_encode(['success' => false, 'message' => 'El nombre no puede exceder 100 caracteres']);
    exit();
}

if (strlen($descripcion) > 500) {
    echo json_encode(['success' => false, 'message' => 'La descripción no puede exceder 500 caracteres']);
    exit();
}

// Validar categoría si se proporciona
if (!empty($categoria_id)) {
    if (!is_numeric($categoria_id)) {
        echo json_encode(['success' => false, 'message' => 'ID de categoría inválido']);
        exit();
    }

    $stmt = $pdo->prepare("SELECT id FROM categorias WHERE id = ? AND activo = 1");
    $stmt->execute([$categoria_id]);
    if (!$stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'La categoría seleccionada no existe o está inactiva']);
        exit();
    }
} else {
    $categoria_id = null;
}

// Manejar subida de imagen
$nombre_imagen = null;
if (isset($_FILES['imagen']) && $_FILES['imagen']['error'] === UPLOAD_ERR_OK) {
    $archivo = $_FILES['imagen'];

    // Validar tipo de archivo
    $tipos_permitidos = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($archivo['type'], $tipos_permitidos)) {
        echo json_encode(['success' => false, 'message' => 'Formato de imagen no válido. Use JPG, PNG, GIF o WEBP']);
        exit();
    }

    // Validar tamaño (5MB máximo)
    if ($archivo['size'] > 5 * 1024 * 1024) {
        echo json_encode(['success' => false, 'message' => 'La imagen es demasiado grande. El tamaño máximo es 5MB']);
        exit();
    }

    // Crear directorio si no existe
    $upload_dir = '../assets/img/productos/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    // Generar nombre único para la imagen
    $extension = pathinfo($archivo['name'], PATHINFO_EXTENSION);
    $nombre_imagen = 'producto_' . time() . '_' . uniqid() . '.' . $extension;
    $ruta_destino = $upload_dir . $nombre_imagen;

    // Mover archivo
    if (!move_uploaded_file($archivo['tmp_name'], $ruta_destino)) {
        echo json_encode(['success' => false, 'message' => 'Error al subir la imagen']);
        exit();
    }
}

try {
    // Verificar que el nombre no exista
    $stmt = $pdo->prepare("SELECT id FROM productos WHERE nombre = ?");
    $stmt->execute([$nombre]);
    if ($stmt->fetch()) {
        // Si hay imagen subida, eliminarla
        if ($nombre_imagen && file_exists($upload_dir . $nombre_imagen)) {
            unlink($upload_dir . $nombre_imagen);
        }
        echo json_encode(['success' => false, 'message' => 'Ya existe un producto con ese nombre']);
        exit();
    }

    // Insertar el nuevo producto
    $stmt = $pdo->prepare("
        INSERT INTO productos (nombre, descripcion, precio_costo, precio_venta, categoria_id, area, imagen, disponible, fecha_creacion)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
    ");

    $stmt->execute([
        $nombre,
        $descripcion,
        $precio_costo,
        $precio_venta,
        $categoria_id,
        $area,
        $nombre_imagen,
        $disponible
    ]);

    $nuevo_id = $pdo->lastInsertId();

    echo json_encode([
        'success' => true,
        'message' => 'Producto creado exitosamente',
        'producto_id' => $nuevo_id,
        'nombre' => $nombre,
        'precio_costo' => $precio_costo,
        'precio_venta' => $precio_venta,
        'area' => $area,
        'imagen' => $nombre_imagen
    ]);

} catch (PDOException $e) {
    // Si hay error, eliminar imagen subida
    if ($nombre_imagen && file_exists($upload_dir . $nombre_imagen)) {
        unlink($upload_dir . $nombre_imagen);
    }

    error_log("Error en crear_producto.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
