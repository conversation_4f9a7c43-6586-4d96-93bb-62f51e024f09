<?php
// Configurar variables para el layout
$page_title = 'Órdenes de Cocina';
$breadcrumbs = [
    ['title' => 'Inicio', 'url' => '/Restaurante/'],
    ['title' => 'Cocina', 'url' => '/Restaurante/cocina/'],
    ['title' => 'Órdenes']
];

// Iniciar el buffer de contenido
ob_start();

// Conectar a la base de datos
session_start();
require_once '../config/db.php';

// Verificar que sea personal de cocina
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'cocina'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    echo '<div class="alert alert-danger">Acceso denegado. Solo personal de cocina puede acceder.</div>';
    $content = ob_get_clean();
    include '../includes/layout.php';
    exit();
}

// Obtener órdenes de cocina
try {
    $stmt = $pdo->query("
        SELECT d.*, o.mesa, o.fecha_hora, o.hash_mesa, p.nombre as producto_nombre,
               p.descripcion, u.nombre as mesero_nombre, m.numero_mesa
        FROM detalle_orden d
        INNER JOIN ordenes o ON d.orden_id = o.id
        INNER JOIN productos p ON d.producto_id = p.id
        INNER JOIN usuarios u ON o.mesero_id = u.id
        INNER JOIN mesas m ON o.hash_mesa = m.hash_mesa
        WHERE d.area = 'cocina' AND d.estado IN ('pendiente', 'preparando')
        ORDER BY d.estado ASC, o.fecha_hora ASC
    ");
    $ordenes_cocina = $stmt->fetchAll();
    
    // Agrupar por estado
    $pendientes = array_filter($ordenes_cocina, function($o) { return $o['estado'] === 'pendiente'; });
    $preparando = array_filter($ordenes_cocina, function($o) { return $o['estado'] === 'preparando'; });
    
} catch (PDOException $e) {
    $ordenes_cocina = [];
    $pendientes = [];
    $preparando = [];
}
?>

<!-- Estadísticas de Cocina -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="bg-secondary bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-clock-fill text-dark fs-4"></i>
                </div>
                <h3 class="text-dark mb-1"><?= count($pendientes) ?></h3>
                <p class="text-muted mb-0 small">Pendientes</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="bg-info bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-fire text-preparando fs-4"></i>
                </div>
                <h3 class="text-preparando mb-1"><?= count($preparando) ?></h3>
                <p class="text-muted mb-0 small">Preparando</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                    <i class="bi bi-check-circle-fill text-success fs-4"></i>
                </div>
                <h3 class="text-success mb-1" id="listos-count">0</h3>
                <p class="text-muted mb-0 small">Listos</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <button class="btn btn-outline-primary rounded-pill w-100" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    Actualizar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Órdenes Pendientes - Diseño Personalizado -->
<div class="ordenes-pendientes-container mb-4">
    <div class="ordenes-header">
        <div class="ordenes-title">
            <span class="ordenes-icon">⏰</span>
            <h4>Órdenes Pendientes (<?= count($pendientes) ?>)</h4>
        </div>
    </div>

    <div class="ordenes-content">
        <?php if (empty($pendientes)): ?>
            <div class="ordenes-empty">
                <span class="empty-icon">✅</span>
                <h5>¡Excelente! No hay órdenes pendientes</h5>
            </div>
        <?php else: ?>
            <div class="ordenes-grid">
                <?php foreach ($pendientes as $orden): ?>
                    <div class="orden-card">
                        <div class="orden-header">
                            <div class="mesa-info">
                                <span class="mesa-numero">Mesa #<?= $orden['numero_mesa'] ?></span>
                                <span class="mesero-nombre"><?= $orden['mesero_nombre'] ?></span>
                            </div>
                            <div class="orden-tiempo">
                                <span class="tiempo-badge">
                                    🕐 <?= date('H:i', strtotime($orden['fecha_hora'])) ?>
                                </span>
                            </div>
                        </div>

                        <div class="orden-producto">
                            <h5 class="producto-nombre"><?= htmlspecialchars($orden['producto_nombre']) ?></h5>
                            <p class="producto-descripcion"><?= htmlspecialchars($orden['descripcion']) ?></p>

                            <?php if (!empty($orden['notas'])): ?>
                                <div class="producto-notas">
                                    <span class="notas-badge">
                                        ⚠️ <?= htmlspecialchars($orden['notas']) ?>
                                    </span>
                                </div>
                            <?php endif; ?>

                            <div class="producto-cantidad">
                                <span class="cantidad-badge">📦 Cantidad: <?= $orden['cantidad'] ?></span>
                            </div>
                        </div>

                        <div class="orden-acciones">
                            <button class="btn-iniciar-preparacion iniciar-preparacion-btn"
                                    data-detalle-id="<?= $orden['id'] ?>"
                                    data-producto="<?= htmlspecialchars($orden['producto_nombre']) ?>">
                                ▶️ Iniciar Preparación
                            </button>
                            <button class="btn-ver-orden ver-orden-btn"
                                    data-orden-id="<?= $orden['orden_id'] ?>">
                                👁️ Ver Orden Completa
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* Estilos personalizados para órdenes pendientes */
.ordenes-pendientes-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.ordenes-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-bottom: 2px solid #dee2e6;
}

.ordenes-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.ordenes-icon {
    font-size: 24px;
}

.ordenes-title h4 {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.ordenes-content {
    padding: 20px;
}

.ordenes-empty {
    text-align: center;
    padding: 40px 20px;
}

.empty-icon {
    font-size: 48px;
    display: block;
    margin-bottom: 16px;
}

.ordenes-empty h5 {
    color: #6c757d;
    margin: 0;
}

.ordenes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.orden-card {
    background: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
}

.orden-card:hover {
    border-color: #6c757d;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.orden-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.mesa-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.mesa-numero {
    font-weight: 700;
    color: #212529;
    font-size: 16px;
}

.mesero-nombre {
    color: #6c757d;
    font-size: 14px;
}

.tiempo-badge {
    background: #f8f9fa;
    color: #495057;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    border: 1px solid #dee2e6;
}

.orden-producto {
    margin-bottom: 20px;
}

.producto-nombre {
    color: #212529;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 18px;
}

.producto-descripcion {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 12px;
    line-height: 1.4;
}

.producto-notas {
    margin-bottom: 12px;
}

.notas-badge {
    background: #fff3cd;
    color: #856404;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 14px;
    display: inline-block;
    border: 1px solid #ffeaa7;
}

.producto-cantidad {
    margin-bottom: 12px;
}

.cantidad-badge {
    background: #d1ecf1;
    color: #0c5460;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.orden-acciones {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.btn-iniciar-preparacion {
    background: #495057;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-iniciar-preparacion:hover {
    background: #343a40;
    transform: translateY(-1px);
}

.btn-ver-orden {
    background: transparent;
    color: #6c757d;
    border: 2px solid #dee2e6;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-ver-orden:hover {
    background: #f8f9fa;
    border-color: #6c757d;
    color: #495057;
}

@media (max-width: 768px) {
    .ordenes-grid {
        grid-template-columns: 1fr;
    }

    .orden-header {
        flex-direction: column;
        gap: 12px;
    }

    .orden-acciones {
        flex-direction: column;
    }
}
</style>

<!-- Órdenes en Preparación - Diseño Personalizado -->
<div class="preparacion-container">
    <div class="preparacion-header">
        <div class="preparacion-title">
            <span class="preparacion-icon">🔥</span>
            <h4>En Preparación (<?= count($preparando) ?>)</h4>
        </div>
    </div>

    <div class="preparacion-content">
        <?php if (empty($preparando)): ?>
            <div class="preparacion-empty">
                <span class="empty-icon">ℹ️</span>
                <h5>No hay órdenes en preparación</h5>
            </div>
        <?php else: ?>
            <div class="preparacion-grid">
                <?php foreach ($preparando as $orden): ?>
                    <div class="preparacion-card">
                        <div class="preparacion-card-header">
                            <div class="mesa-info">
                                <span class="mesa-numero">Mesa #<?= $orden['numero_mesa'] ?></span>
                                <span class="mesero-nombre"><?= $orden['mesero_nombre'] ?></span>
                            </div>
                            <div class="preparacion-tiempo">
                                <span class="tiempo-badge">
                                    🕐 <?= date('H:i', strtotime($orden['fecha_hora'])) ?>
                                </span>
                            </div>
                        </div>

                        <div class="preparacion-producto">
                            <h5 class="producto-nombre"><?= htmlspecialchars($orden['producto_nombre']) ?></h5>
                            <p class="producto-descripcion"><?= htmlspecialchars($orden['descripcion']) ?></p>

                            <?php if (!empty($orden['notas'])): ?>
                                <div class="producto-notas">
                                    <span class="notas-badge">
                                        ⚠️ <?= htmlspecialchars($orden['notas']) ?>
                                    </span>
                                </div>
                            <?php endif; ?>

                            <div class="producto-cantidad">
                                <span class="cantidad-badge">📦 Cantidad: <?= $orden['cantidad'] ?></span>
                            </div>
                        </div>

                        <div class="preparacion-acciones">
                            <button class="btn-marcar-listo marcar-listo-btn"
                                    data-detalle-id="<?= $orden['id'] ?>"
                                    data-producto="<?= htmlspecialchars($orden['producto_nombre']) ?>">
                                ✅ Marcar como Listo
                            </button>
                            <button class="btn-ver-orden-prep ver-orden-btn"
                                    data-orden-id="<?= $orden['orden_id'] ?>">
                                👁️ Ver Orden Completa
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* Estilos personalizados para órdenes en preparación */
.preparacion-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.preparacion-header {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    padding: 20px;
    border-bottom: 2px solid #ce93d8;
}

.preparacion-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.preparacion-icon {
    font-size: 24px;
}

.preparacion-title h4 {
    margin: 0;
    color: #7b1fa2;
    font-weight: 600;
}

.preparacion-content {
    padding: 20px;
}

.preparacion-empty {
    text-align: center;
    padding: 40px 20px;
}

.empty-icon {
    font-size: 48px;
    display: block;
    margin-bottom: 16px;
}

.preparacion-empty h5 {
    color: #6c757d;
    margin: 0;
}

.preparacion-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.preparacion-card {
    background: #ffffff;
    border: 2px solid #f3e5f5;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
}

.preparacion-card:hover {
    border-color: #9c27b0;
    box-shadow: 0 4px 12px rgba(156, 39, 176, 0.15);
    transform: translateY(-2px);
}

.preparacion-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.mesa-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.mesa-numero {
    font-weight: 700;
    color: #7b1fa2;
    font-size: 16px;
}

.mesero-nombre {
    color: #6c757d;
    font-size: 14px;
}

.tiempo-badge {
    background: #f3e5f5;
    color: #7b1fa2;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    border: 1px solid #ce93d8;
}

.preparacion-producto {
    margin-bottom: 20px;
}

.producto-nombre {
    color: #212529;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 18px;
}

.producto-descripcion {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 12px;
    line-height: 1.4;
}

.producto-notas {
    margin-bottom: 12px;
}

.notas-badge {
    background: #fff3cd;
    color: #856404;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 14px;
    display: inline-block;
    border: 1px solid #ffeaa7;
}

.producto-cantidad {
    margin-bottom: 12px;
}

.cantidad-badge {
    background: #f3e5f5;
    color: #7b1fa2;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.preparacion-acciones {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.btn-marcar-listo {
    background: #4caf50;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-marcar-listo:hover {
    background: #45a049;
    transform: translateY(-1px);
}

.btn-ver-orden-prep {
    background: transparent;
    color: #7b1fa2;
    border: 2px solid #ce93d8;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-ver-orden-prep:hover {
    background: #f3e5f5;
    border-color: #9c27b0;
    color: #7b1fa2;
}

@media (max-width: 768px) {
    .preparacion-grid {
        grid-template-columns: 1fr;
    }

    .preparacion-card-header {
        flex-direction: column;
        gap: 12px;
    }

    .preparacion-acciones {
        flex-direction: column;
    }
}
</style>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// JavaScript adicional
$extra_js = '
<script>
document.addEventListener("DOMContentLoaded", function() {
    // Auto-refresh cada 30 segundos
    setInterval(function() {
        location.reload();
    }, 30000);
    
    // Iniciar preparación
    document.querySelectorAll(".iniciar-preparacion-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const detalleId = this.dataset.detalleId;
            const producto = this.dataset.producto;

            cambiarEstadoDetalle(detalleId, "preparando");
        });
    });
    
    // Marcar como listo
    document.querySelectorAll(".marcar-listo-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const detalleId = this.dataset.detalleId;
            const producto = this.dataset.producto;

            cambiarEstadoDetalle(detalleId, "listo");
        });
    });

    // Ver orden completa
    document.querySelectorAll(".ver-orden-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            const ordenId = this.dataset.ordenId;
            verOrdenCompleta(ordenId);
        });
    });
});

function cambiarEstadoDetalle(detalleId, nuevoEstado) {
    fetch("/Restaurante/api/cambiar_estado_detalle.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            detalle_id: detalleId,
            nuevo_estado: nuevoEstado
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast("Estado actualizado correctamente", "success");
            setTimeout(() => location.reload(), 1500);
        } else {
            showToast("Error: " + data.message, "error");
        }
    })
    .catch(error => {
        console.error("Error:", error);
        showToast("Error de conexión", "error");
    });
}

function verOrdenCompleta(ordenId) {
    // Redirigir a la página de detalle de orden
    window.location.href = `/Restaurante/meseros/orden_detalle.php?id=${ordenId}`;
}

// Función para mostrar notificaciones toast
function showToast(message, type = "info") {
    // Crear contenedor de toasts si no existe
    let toastContainer = document.getElementById("toast-container");
    if (!toastContainer) {
        toastContainer = document.createElement("div");
        toastContainer.id = "toast-container";
        toastContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            display: flex;
            flex-direction: column;
            gap: 10px;
        `;
        document.body.appendChild(toastContainer);
    }

    // Crear elemento toast
    const toast = document.createElement("div");
    const bgColor = type === "success" ? "#d4edda" : type === "error" ? "#f8d7da" : "#d1ecf1";
    const textColor = type === "success" ? "#155724" : type === "error" ? "#721c24" : "#0c5460";
    const icon = type === "success" ? "✅" : type === "error" ? "❌" : "ℹ️";

    toast.style.cssText = `
        background: ${bgColor};
        color: ${textColor};
        padding: 12px 20px;
        border-radius: 8px;
        border: 1px solid ${type === "success" ? "#c3e6cb" : type === "error" ? "#f5c6cb" : "#bee5eb"};
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        animation: slideInRight 0.3s ease-out;
        font-weight: 500;
    `;

    toast.innerHTML = `${icon} ${message}`;

    // Agregar animación CSS
    if (!document.getElementById("toast-animations")) {
        const style = document.createElement("style");
        style.id = "toast-animations";
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    toastContainer.appendChild(toast);

    // Remover después de 4 segundos
    setTimeout(() => {
        toast.style.animation = "slideOutRight 0.3s ease-in";
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 4000);
}
</script>
';

// Incluir el layout
include '../includes/layout.php';
?>
