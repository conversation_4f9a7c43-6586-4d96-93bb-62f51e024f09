<?php

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();
require_once '../config/db.php';

header('Content-Type: application/json');

// Verificar que sea administrador
if (!isset($_SESSION['usuario_id'])) {
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

$stmt = $pdo->prepare("SELECT rol FROM usuarios WHERE id = ?");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario || $usuario['rol'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden ver detalles de compras']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit();
}

try {
    $compra_id = $_GET['id'] ?? '';
    
    if (empty($compra_id)) {
        throw new Exception('ID de compra requerido');
    }

    // Obtener detalles de la compra con información de productos
    $stmt = $pdo->prepare("
        SELECT 
            dc.*,
            p.nombre as producto_nombre,
            i.unidad_medida
        FROM detalle_compras dc
        LEFT JOIN productos p ON dc.producto_id = p.id
        LEFT JOIN inventario i ON dc.producto_id = i.producto_id
        WHERE dc.compra_id = ?
        ORDER BY p.nombre
    ");
    $stmt->execute([$compra_id]);
    $productos = $stmt->fetchAll();

    echo json_encode([
        'success' => true,
        'productos' => $productos
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
