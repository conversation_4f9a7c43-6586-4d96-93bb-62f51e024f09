<?php
header('Content-Type: application/json');

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();

// Verificar que el usuario esté logueado y sea admin
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

require_once '../config/db.php';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol = 'admin'");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo administradores pueden cambiar estado de empleados']);
    exit();
}

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);
$id = $input['id'] ?? null;
$activo = $input['activo'] ?? null;

// Validaciones
if (!$id || $activo === null) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Datos incompletos']);
    exit();
}

// No permitir desactivar al propio usuario
if ($id == $_SESSION['usuario_id']) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'No puedes cambiar tu propio estado']);
    exit();
}

try {
    // Verificar que el empleado existe
    $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ?");
    $stmt->execute([$id]);
    $empleado = $stmt->fetch();
    
    if (!$empleado) {
        echo json_encode(['success' => false, 'message' => 'Empleado no encontrado']);
        exit();
    }
    
    // Actualizar el estado
    $stmt = $pdo->prepare("UPDATE usuarios SET activo = ? WHERE id = ?");
    $stmt->execute([$activo, $id]);
    
    $accion = $activo ? 'activado' : 'desactivado';
    
    echo json_encode([
        'success' => true, 
        'message' => "Empleado {$accion} exitosamente",
        'empleado_id' => $id,
        'nombre' => $empleado['nombre'],
        'nuevo_estado' => $activo
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
