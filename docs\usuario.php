<?php
// Documentación pública - No requiere autenticación
// Permitir acceso sin login para facilitar la implementación del sistema

// Variables para el layout
$page_title = 'Manual de Usuario';
$breadcrumbs = [
    ['title' => 'Documentación', 'url' => '/Restaurante/docs/'],
    ['title' => 'Manual de Usuario']
];

// Iniciar captura de contenido
ob_start();
?>

<!-- Header -->
<div class="user-docs-header">
    <div class="user-docs-title">
        <span class="user-docs-icon">👤</span>
        <h2>Manual de Usuario</h2>
    </div>
    <p>Guía completa para usar el sistema según tu rol</p>
</div>

<!-- Navegación Interna -->
<div class="user-docs-nav">
    <h4>📋 Contenido del Manual</h4>
    <div class="nav-links">
        <a href="#introduccion" class="nav-link">🏠 Introducción</a>
        <a href="#meseros" class="nav-link">🍽️ Guía para Meseros</a>
        <a href="#cocina" class="nav-link">👨‍🍳 Guía para Cocina</a>
        <a href="#bebidas" class="nav-link">🥤 Guía para Bebidas</a>
        <a href="#admin" class="nav-link">⚙️ Guía para Administradores</a>
        <a href="#consejos" class="nav-link">💡 Consejos y Trucos</a>
    </div>
</div>

<!-- Introducción -->
<section id="introduccion" class="docs-section">
    <div class="section-header">
        <h3>🏠 Introducción al Sistema</h3>
    </div>
    
    <div class="section-content">
        <div class="intro-grid">
            <div class="intro-card">
                <h5>🎯 Objetivo del Sistema</h5>
                <p>El sistema de restaurante está diseñado para optimizar el flujo de trabajo desde la toma de órdenes hasta la entrega al cliente, con roles específicos para cada área.</p>
            </div>
            
            <div class="intro-card">
                <h5>👥 Roles del Sistema</h5>
                <ul>
                    <li><strong>Administrador:</strong> Gestión completa del sistema</li>
                    <li><strong>Mesero:</strong> Toma de órdenes y atención al cliente</li>
                    <li><strong>Cocina:</strong> Preparación de alimentos</li>
                    <li><strong>Bebidas:</strong> Preparación de bebidas</li>
                </ul>
            </div>
            
            <div class="intro-card">
                <h5>🔄 Flujo de Trabajo</h5>
                <ol>
                    <li>Mesero abre mesa y crea cuentas</li>
                    <li>Toma órdenes de los clientes</li>
                    <li>Cocina y bebidas preparan productos</li>
                    <li>Mesero sirve y cobra</li>
                    <li>Mesa se cierra automáticamente</li>
                </ol>
            </div>
            
            <div class="intro-card">
                <h5>📱 Acceso al Sistema</h5>
                <p>Ingresa con tu usuario y contraseña. El sistema te dirigirá automáticamente a tu panel según tu rol. Funciona en computadoras, tablets y móviles.</p>
            </div>
        </div>
    </div>
</section>

<!-- Guía para Meseros -->
<section id="meseros" class="docs-section">
    <div class="section-header">
        <h3>🍽️ Guía para Meseros</h3>
    </div>
    
    <div class="section-content">
        <div class="guide-step">
            <div class="step-number">1</div>
            <div class="step-content">
                <h5>📋 Tomar Orden</h5>
                <p>Desde el panel principal, selecciona "Tomar Orden" para ver las mesas disponibles.</p>
                <ul>
                    <li>🟢 <strong>Mesas libres:</strong> Puedes abrir y crear cuentas</li>
                    <li>🔴 <strong>Mesas ocupadas:</strong> Puedes agregar órdenes a cuentas existentes</li>
                </ul>
            </div>
        </div>
        
        <div class="guide-step">
            <div class="step-number">2</div>
            <div class="step-content">
                <h5>👤 Crear Cuentas</h5>
                <p>Al seleccionar una mesa libre, se abre automáticamente y puedes crear cuentas individuales:</p>
                <ul>
                    <li>Ingresa nombre y apellido del cliente</li>
                    <li>Cada cuenta es independiente para facilitar el pago</li>
                    <li>Puedes crear múltiples cuentas por mesa</li>
                </ul>
            </div>
        </div>
        
        <div class="guide-step">
            <div class="step-number">3</div>
            <div class="step-content">
                <h5>🛒 Agregar Productos</h5>
                <p>Selecciona productos del menú y agrégalos a la cuenta:</p>
                <ul>
                    <li>Usa las categorías para filtrar productos</li>
                    <li>Ajusta cantidades con los botones + y -</li>
                    <li>Agrega notas especiales (ej: "Sin cebolla")</li>
                    <li>Revisa el total antes de confirmar</li>
                </ul>
            </div>
        </div>
        
        <div class="guide-step">
            <div class="step-number">4</div>
            <div class="step-content">
                <h5>👁️ Seguimiento de Órdenes</h5>
                <p>Monitorea el estado de las órdenes en tiempo real:</p>
                <ul>
                    <li>⏳ <strong>Pendiente:</strong> Orden enviada a cocina/bebidas</li>
                    <li>🔄 <strong>Preparando:</strong> En proceso de preparación</li>
                    <li>✅ <strong>Listo:</strong> Producto terminado, listo para servir</li>
                    <li>🍽️ <strong>Servido:</strong> Entregado al cliente</li>
                </ul>
            </div>
        </div>
        
        <div class="guide-step">
            <div class="step-number">5</div>
            <div class="step-content">
                <h5>💳 Procesar Pagos</h5>
                <p>Cuando el cliente esté listo para pagar:</p>
                <ul>
                    <li>Ve a "Cuentas de Mesa" desde el menú</li>
                    <li>Genera ticket individual o paga cuenta completa</li>
                    <li>Opción de pagar mesa completa (todas las cuentas)</li>
                    <li>La mesa se cierra automáticamente al pagar todas las cuentas</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Guía para Cocina -->
<section id="cocina" class="docs-section">
    <div class="section-header">
        <h3>👨‍🍳 Guía para Cocina</h3>
    </div>
    
    <div class="section-content">
        <div class="guide-step">
            <div class="step-number">1</div>
            <div class="step-content">
                <h5>📋 Panel de Órdenes</h5>
                <p>Tu panel muestra solo productos de cocina:</p>
                <ul>
                    <li>Órdenes organizadas por mesa y tiempo</li>
                    <li>Información del cliente y mesero</li>
                    <li>Notas especiales del cliente</li>
                    <li>Prioridad por tiempo de orden</li>
                </ul>
            </div>
        </div>
        
        <div class="guide-step">
            <div class="step-number">2</div>
            <div class="step-content">
                <h5>🔄 Cambiar Estados</h5>
                <p>Actualiza el estado según el progreso:</p>
                <ul>
                    <li>⏳ → 🔄 <strong>Iniciar preparación:</strong> Cuando comiences a cocinar</li>
                    <li>🔄 → ✅ <strong>Marcar listo:</strong> Cuando el plato esté terminado</li>
                    <li>El mesero marcará como servido al entregar</li>
                </ul>
            </div>
        </div>
        
        <div class="guide-step">
            <div class="step-number">3</div>
            <div class="step-content">
                <h5>👁️ Vista de Preparación</h5>
                <p>Organiza tu trabajo eficientemente:</p>
                <ul>
                    <li>Ve todos los productos en preparación</li>
                    <li>Productos listos para entregar</li>
                    <li>Filtros por mesa o tipo de producto</li>
                    <li>Notificaciones de nuevas órdenes</li>
                </ul>
            </div>
        </div>
        
        <div class="guide-step">
            <div class="step-number">4</div>
            <div class="step-content">
                <h5>📝 Notas Importantes</h5>
                <p>Presta atención a las instrucciones especiales:</p>
                <ul>
                    <li>Alergias y restricciones alimentarias</li>
                    <li>Modificaciones del cliente</li>
                    <li>Nivel de cocción preferido</li>
                    <li>Ingredientes a omitir</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Guía para Bebidas -->
<section id="bebidas" class="docs-section">
    <div class="section-header">
        <h3>🥤 Guía para Bebidas</h3>
    </div>
    
    <div class="section-content">
        <div class="guide-step">
            <div class="step-number">1</div>
            <div class="step-content">
                <h5>🍹 Panel de Bebidas</h5>
                <p>Tu panel está optimizado para el área de bebidas:</p>
                <ul>
                    <li>Solo productos de bebidas y cocteles</li>
                    <li>Órdenes priorizadas por tiempo</li>
                    <li>Información de mesa y cliente</li>
                    <li>Instrucciones especiales</li>
                </ul>
            </div>
        </div>
        
        <div class="guide-step">
            <div class="step-number">2</div>
            <div class="step-content">
                <h5>⚡ Preparación Rápida</h5>
                <p>Las bebidas suelen ser más rápidas que la comida:</p>
                <ul>
                    <li>Prioriza bebidas frías y simples</li>
                    <li>Coordina con cocina para timing</li>
                    <li>Marca como listo cuando esté preparado</li>
                    <li>Comunica al mesero si hay demoras</li>
                </ul>
            </div>
        </div>
        
        <div class="guide-step">
            <div class="step-number">3</div>
            <div class="step-content">
                <h5>🧊 Instrucciones Especiales</h5>
                <p>Atiende las preferencias del cliente:</p>
                <ul>
                    <li>Nivel de hielo preferido</li>
                    <li>Endulzantes adicionales</li>
                    <li>Modificaciones de receta</li>
                    <li>Temperatura específica</li>
                </ul>
            </div>
        </div>
        
        <div class="guide-step">
            <div class="step-number">4</div>
            <div class="step-content">
                <h5>📊 Gestión de Inventario</h5>
                <p>Mantén control del stock:</p>
                <ul>
                    <li>Reporta productos agotados al admin</li>
                    <li>Sugiere alternativas similares</li>
                    <li>Coordina reposición de ingredientes</li>
                    <li>Mantén área de trabajo organizada</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Guía para Administradores -->
<section id="admin" class="docs-section">
    <div class="section-header">
        <h3>⚙️ Guía para Administradores</h3>
    </div>

    <div class="section-content">
        <div class="guide-step">
            <div class="step-number">1</div>
            <div class="step-content">
                <h5>📊 Dashboard Principal</h5>
                <p>El panel de administración te da una vista completa del restaurante:</p>
                <ul>
                    <li>Estadísticas de ventas del día</li>
                    <li>Estado de todas las mesas</li>
                    <li>Órdenes pendientes y en proceso</li>
                    <li>Empleados activos</li>
                    <li>Productos más vendidos</li>
                </ul>
            </div>
        </div>

        <div class="guide-step">
            <div class="step-number">2</div>
            <div class="step-content">
                <h5>👥 Gestión de Empleados</h5>
                <p>Administra el personal del restaurante:</p>
                <ul>
                    <li>Crear nuevos usuarios con roles específicos</li>
                    <li>Activar/desactivar empleados</li>
                    <li>Cambiar contraseñas (MD5 con "1234" por defecto)</li>
                    <li>Asignar permisos según el rol</li>
                    <li>Ver historial de actividad</li>
                </ul>
            </div>
        </div>

        <div class="guide-step">
            <div class="step-number">3</div>
            <div class="step-content">
                <h5>🍽️ Gestión de Productos</h5>
                <p>Controla el menú y los precios:</p>
                <ul>
                    <li>Agregar nuevos productos con fotos</li>
                    <li>Configurar precios de costo y venta</li>
                    <li>Organizar por categorías y áreas</li>
                    <li>Activar/desactivar disponibilidad</li>
                    <li>Ver márgenes de ganancia</li>
                </ul>
            </div>
        </div>

        <div class="guide-step">
            <div class="step-number">4</div>
            <div class="step-content">
                <h5>📋 Categorías</h5>
                <p>Organiza el menú con categorías personalizadas:</p>
                <ul>
                    <li>Crear categorías con iconos y colores</li>
                    <li>Asignar productos a categorías</li>
                    <li>Controlar visibilidad en el menú</li>
                    <li>Ordenar categorías por prioridad</li>
                </ul>
            </div>
        </div>

        <div class="guide-step">
            <div class="step-number">5</div>
            <div class="step-content">
                <h5>📊 Reportes y Análisis</h5>
                <p>Obtén insights del negocio:</p>
                <ul>
                    <li>Reportes de ventas por período</li>
                    <li>Productos más vendidos</li>
                    <li>Eficiencia de meseros</li>
                    <li>Análisis de categorías</li>
                    <li>Exportar datos a Excel</li>
                </ul>
            </div>
        </div>

        <div class="guide-step">
            <div class="step-number">6</div>
            <div class="step-content">
                <h5>⚙️ Configuración</h5>
                <p>Personaliza el sistema:</p>
                <ul>
                    <li>Información del restaurante</li>
                    <li>Número de mesas disponibles</li>
                    <li>Mensajes de cortesía para tickets</li>
                    <li>Configuración de impresión</li>
                    <li>Parámetros del sistema</li>
                </ul>
            </div>
        </div>

        <div class="guide-step">
            <div class="step-number">7</div>
            <div class="step-content">
                <h5>📋 Registro de Órdenes</h5>
                <p>Historial completo de todas las órdenes:</p>
                <ul>
                    <li>Filtrar por fecha, mesa, mesero o estado</li>
                    <li>Ver detalles completos de cada orden</li>
                    <li>Estadísticas generales del período</li>
                    <li>Exportar reportes detallados</li>
                    <li>Generar tickets de órdenes pasadas</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Consejos y Trucos -->
<section id="consejos" class="docs-section">
    <div class="section-header">
        <h3>💡 Consejos y Trucos</h3>
    </div>

    <div class="section-content">
        <div class="tips-grid">
            <div class="tip-card">
                <div class="tip-icon">⚡</div>
                <h5>Eficiencia en Órdenes</h5>
                <ul>
                    <li>Usa las categorías para encontrar productos rápido</li>
                    <li>Agrega notas detalladas para evitar errores</li>
                    <li>Confirma la orden antes de enviar</li>
                    <li>Mantén comunicación con cocina/bebidas</li>
                </ul>
            </div>

            <div class="tip-card">
                <div class="tip-icon">📱</div>
                <h5>Uso en Móviles</h5>
                <ul>
                    <li>El sistema funciona perfectamente en tablets</li>
                    <li>Usa orientación vertical para mejor experiencia</li>
                    <li>Los botones están optimizados para touch</li>
                    <li>Actualiza la página si hay problemas de conexión</li>
                </ul>
            </div>

            <div class="tip-card">
                <div class="tip-icon">🔄</div>
                <h5>Actualizaciones en Tiempo Real</h5>
                <ul>
                    <li>El sistema se actualiza automáticamente</li>
                    <li>Usa el botón "Actualizar" si necesitas refrescar</li>
                    <li>Los cambios de estado se ven inmediatamente</li>
                    <li>Las notificaciones aparecen en la esquina superior</li>
                </ul>
            </div>

            <div class="tip-card">
                <div class="tip-icon">🎯</div>
                <h5>Mejores Prácticas</h5>
                <ul>
                    <li>Cierra sesión al terminar tu turno</li>
                    <li>Reporta problemas técnicos inmediatamente</li>
                    <li>Mantén tu área de trabajo organizada</li>
                    <li>Comunica cambios importantes al equipo</li>
                </ul>
            </div>
        </div>

        <div class="help-section">
            <h5>🆘 ¿Necesitas Ayuda?</h5>
            <p>Si tienes problemas con el sistema:</p>
            <ol>
                <li>Verifica tu conexión a internet</li>
                <li>Actualiza la página (F5 o botón actualizar)</li>
                <li>Cierra y vuelve a abrir el navegador</li>
                <li>Contacta al administrador del sistema</li>
                <li>Revisa esta documentación para dudas específicas</li>
            </ol>
        </div>
    </div>
</section>

<style>
/* Estilos para documentación de usuario */
.user-docs-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    border-radius: 12px;
    text-align: center;
    margin-bottom: 24px;
}

.user-docs-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    margin-bottom: 12px;
}

.user-docs-icon {
    font-size: 48px;
}

.user-docs-title h2 {
    margin: 0;
    font-weight: 700;
}

.user-docs-nav {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.user-docs-nav h4 {
    margin-bottom: 16px;
    color: #2c3e50;
}

.nav-links {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.nav-link {
    background: #f8f9fa;
    color: #495057;
    padding: 8px 16px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background: #667eea;
    color: white;
    text-decoration: none;
}

.docs-section {
    background: white;
    border-radius: 12px;
    margin-bottom: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.section-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 24px;
}

.section-header h3 {
    margin: 0;
    font-weight: 600;
}

.section-content {
    padding: 24px;
}

.intro-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.intro-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.intro-card h5 {
    color: #2c3e50;
    margin-bottom: 12px;
    font-weight: 600;
}

.intro-card p, .intro-card ul, .intro-card ol {
    color: #495057;
    margin-bottom: 0;
}

.guide-step {
    display: flex;
    gap: 20px;
    margin-bottom: 24px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e9ecef;
}

.guide-step:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.step-number {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 18px;
    flex-shrink: 0;
}

.step-content h5 {
    color: #2c3e50;
    margin-bottom: 12px;
    font-weight: 600;
}

.step-content p {
    color: #495057;
    margin-bottom: 12px;
}

.step-content ul {
    color: #495057;
    margin-bottom: 0;
}

.step-content li {
    margin-bottom: 8px;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.tip-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #28a745;
    text-align: center;
}

.tip-icon {
    font-size: 32px;
    margin-bottom: 12px;
}

.tip-card h5 {
    color: #2c3e50;
    margin-bottom: 16px;
    font-weight: 600;
}

.tip-card ul {
    text-align: left;
    color: #495057;
    margin: 0;
}

.tip-card li {
    margin-bottom: 8px;
}

.help-section {
    background: #e3f2fd;
    padding: 24px;
    border-radius: 8px;
    border-left: 4px solid #2196f3;
}

.help-section h5 {
    color: #1565c0;
    margin-bottom: 16px;
    font-weight: 600;
}

.help-section p {
    color: #495057;
    margin-bottom: 12px;
}

.help-section ol {
    color: #495057;
    margin: 0;
}

.help-section li {
    margin-bottom: 8px;
}

@media (max-width: 768px) {
    .nav-links {
        flex-direction: column;
    }
    
    .intro-grid {
        grid-template-columns: 1fr;
    }
    
    .guide-step {
        flex-direction: column;
        text-align: center;
    }
    
    .step-number {
        align-self: center;
    }

    .tips-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php
// Capturar el contenido y asignarlo a la variable
$content = ob_get_clean();

// Incluir el layout público (sin autenticación)
include 'layout_public.php';
?>
