<?php
header('Content-Type: application/json');
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Verificar que sea mesero o admin
require_once '../config/db.php';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol IN ('mesero', 'admin')");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo meseros y administradores pueden aplicar ajustes']);
    exit();
}

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);

// Validar datos requeridos
$cuenta_id = $input['cuenta_id'] ?? null;
$propina_porcentaje = $input['propina_porcentaje'] ?? 10;
$propina_monto = $input['propina_monto'] ?? 0;
$descuento_tipo = $input['descuento_tipo'] ?? 'ninguno';
$descuento_valor = $input['descuento_valor'] ?? 0;
$descuento_monto = $input['descuento_monto'] ?? 0;
$descuento_motivo = $input['descuento_motivo'] ?? null;
$subtotal = $input['subtotal'] ?? 0;
$total_final = $input['total_final'] ?? 0;
$procesar_pago = $input['procesar_pago'] ?? false;

if (!$cuenta_id) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID de cuenta requerido']);
    exit();
}

try {
    // Verificar que la cuenta existe y pertenece al mesero (si no es admin)
    if ($usuario['rol'] === 'mesero') {
        $stmt = $pdo->prepare("SELECT * FROM cuentas WHERE id = ? AND mesero_id = ?");
        $stmt->execute([$cuenta_id, $usuario['id']]);
    } else {
        $stmt = $pdo->prepare("SELECT * FROM cuentas WHERE id = ?");
        $stmt->execute([$cuenta_id]);
    }
    
    $cuenta = $stmt->fetch();
    
    if (!$cuenta) {
        echo json_encode(['success' => false, 'message' => 'Cuenta no encontrada o no tienes permisos']);
        exit();
    }
    
    if ($cuenta['estado'] === 'pagada') {
        echo json_encode(['success' => false, 'message' => 'La cuenta ya está pagada']);
        exit();
    }
    
    // Validaciones de datos
    if ($propina_porcentaje < 0 || $propina_porcentaje > 100) {
        echo json_encode(['success' => false, 'message' => 'Porcentaje de propina inválido (0-100%)']);
        exit();
    }
    
    if ($descuento_monto > $subtotal) {
        echo json_encode(['success' => false, 'message' => 'El descuento no puede ser mayor al subtotal']);
        exit();
    }
    
    if ($total_final < 0) {
        echo json_encode(['success' => false, 'message' => 'El total final no puede ser negativo']);
        exit();
    }
    
    // Iniciar transacción
    $pdo->beginTransaction();
    
    // Actualizar la cuenta con los ajustes
    $stmt = $pdo->prepare("
        UPDATE cuentas SET 
            subtotal = ?,
            propina_porcentaje = ?,
            propina_monto = ?,
            descuento_tipo = ?,
            descuento_valor = ?,
            descuento_monto = ?,
            descuento_motivo = ?,
            total_final = ?,
            total = ?,
            fecha_actualizacion = NOW()
        WHERE id = ?
    ");
    
    $stmt->execute([
        $subtotal,
        $propina_porcentaje,
        $propina_monto,
        $descuento_tipo,
        $descuento_valor,
        $descuento_monto,
        $descuento_motivo,
        $total_final,
        $total_final, // Actualizar también el campo total original
        $cuenta_id
    ]);
    
    // Si se solicita procesar el pago
    if ($procesar_pago) {
        // Verificar que todos los productos estén servidos
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total,
                   SUM(CASE WHEN d.estado = 'servido' THEN 1 ELSE 0 END) as servidos
            FROM detalle_orden d
            INNER JOIN ordenes o ON d.orden_id = o.id
            WHERE o.cuenta_id = ?
        ");
        $stmt->execute([$cuenta_id]);
        $estado_productos = $stmt->fetch();
        
        if ($estado_productos['total'] > 0 && $estado_productos['total'] != $estado_productos['servidos']) {
            $pdo->rollBack();
            echo json_encode(['success' => false, 'message' => 'No se puede pagar: hay productos que no han sido servidos']);
            exit();
        }
        
        // Marcar cuenta como pagada
        $stmt = $pdo->prepare("UPDATE cuentas SET estado = 'pagada', fecha_actualizacion = NOW() WHERE id = ?");
        $stmt->execute([$cuenta_id]);
        
        // Finalizar todas las órdenes de esta cuenta
        $stmt = $pdo->prepare("UPDATE ordenes SET estado = 'finalizada' WHERE cuenta_id = ?");
        $stmt->execute([$cuenta_id]);
        
        // Marcar todos los productos como servidos (por si acaso)
        $stmt = $pdo->prepare("
            UPDATE detalle_orden 
            SET estado = 'servido' 
            WHERE orden_id IN (SELECT id FROM ordenes WHERE cuenta_id = ?) 
            AND estado != 'servido'
        ");
        $stmt->execute([$cuenta_id]);
        
        // Verificar si todas las cuentas de la mesa están pagadas para cerrar la mesa
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total_cuentas,
                   SUM(CASE WHEN estado = 'pagada' THEN 1 ELSE 0 END) as cuentas_pagadas
            FROM cuentas 
            WHERE hash_mesa = ?
        ");
        $stmt->execute([$cuenta['hash_mesa']]);
        $estado_mesa = $stmt->fetch();
        
        if ($estado_mesa['total_cuentas'] == $estado_mesa['cuentas_pagadas']) {
            // Cerrar la mesa automáticamente
            $stmt = $pdo->prepare("UPDATE mesas SET estado = 'libre', fecha_apertura = NULL, hash_mesa = NULL WHERE hash_mesa = ?");
            $stmt->execute([$cuenta['hash_mesa']]);
        }
    }
    
    // Confirmar transacción
    $pdo->commit();
    
    // Preparar respuesta
    $response = [
        'success' => true,
        'message' => $procesar_pago ? 'Ajustes aplicados y cuenta pagada exitosamente' : 'Ajustes aplicados correctamente',
        'cuenta_id' => $cuenta_id,
        'ajustes' => [
            'subtotal' => $subtotal,
            'propina_porcentaje' => $propina_porcentaje,
            'propina_monto' => $propina_monto,
            'descuento_tipo' => $descuento_tipo,
            'descuento_valor' => $descuento_valor,
            'descuento_monto' => $descuento_monto,
            'descuento_motivo' => $descuento_motivo,
            'total_final' => $total_final
        ],
        'pagada' => $procesar_pago
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Rollback en caso de error
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
