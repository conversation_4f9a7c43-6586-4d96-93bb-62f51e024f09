<?php
header('Content-Type: application/json');

// Incluir configuración de sesiones ANTES de session_start()
require_once '../config/session_config.php';
session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

// Verificar que sea un mesero o administrador
require_once '../config/db.php';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ? AND rol IN ('mesero', 'admin')");
$stmt->execute([$_SESSION['usuario_id']]);
$usuario = $stmt->fetch();

if (!$usuario) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Solo meseros y administradores pueden cerrar cuentas']);
    exit();
}

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);
$cuenta_id = $input['cuenta_id'] ?? '';
$accion = $input['accion'] ?? 'cerrar'; // 'cerrar' o 'pagar'

if (empty($cuenta_id)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID de cuenta requerido']);
    exit();
}

$acciones_validas = ['cerrar', 'pagar'];
if (!in_array($accion, $acciones_validas)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Acción inválida']);
    exit();
}

try {
    // Verificar que la cuenta existe
    $stmt = $pdo->prepare("SELECT * FROM cuentas WHERE id = ?");
    $stmt->execute([$cuenta_id]);
    $cuenta = $stmt->fetch();
    
    if (!$cuenta) {
        echo json_encode(['success' => false, 'message' => 'Cuenta no encontrada']);
        exit();
    }
    
    // Verificar que el mesero tenga acceso a esta cuenta (si no es admin)
    if ($usuario['rol'] === 'mesero' && $cuenta['mesero_id'] != $usuario['id']) {
        echo json_encode(['success' => false, 'message' => 'No tienes permisos para esta cuenta']);
        exit();
    }
    
    if ($cuenta['estado'] === 'pagada') {
        echo json_encode(['success' => false, 'message' => 'La cuenta ya está pagada']);
        exit();
    }
    
    // Verificar que todos los productos estén servidos (solo para pagar)
    if ($accion === 'pagar') {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total,
                   SUM(CASE WHEN d.estado = 'servido' THEN 1 ELSE 0 END) as servidos
            FROM detalle_orden d
            INNER JOIN ordenes o ON d.orden_id = o.id
            WHERE o.cuenta_id = ?
        ");
        $stmt->execute([$cuenta_id]);
        $estado_productos = $stmt->fetch();
        
        if ($estado_productos['total'] > 0 && $estado_productos['total'] != $estado_productos['servidos']) {
            echo json_encode(['success' => false, 'message' => 'No se puede pagar: hay productos que no han sido servidos']);
            exit();
        }
    }
    
    // Actualizar estado de la cuenta
    $nuevo_estado = ($accion === 'pagar') ? 'pagada' : 'cerrada';
    $stmt = $pdo->prepare("UPDATE cuentas SET estado = ?, fecha_actualizacion = NOW() WHERE id = ?");
    $stmt->execute([$nuevo_estado, $cuenta_id]);
    
    // Si se paga la cuenta, finalizar todas las órdenes
    if ($accion === 'pagar') {
        $stmt = $pdo->prepare("UPDATE ordenes SET estado = 'finalizada' WHERE cuenta_id = ?");
        $stmt->execute([$cuenta_id]);
    }

    // Verificar si todas las cuentas de la mesa están pagadas para cerrar automáticamente
    if ($accion === 'pagar') {
        $stmt = $pdo->prepare("SELECT COUNT(*) as cuentas_abiertas FROM cuentas WHERE hash_mesa = ? AND estado != 'pagada'");
        $stmt->execute([$cuenta['hash_mesa']]);
        $cuentas_result = $stmt->fetch();

        // Si no hay más cuentas abiertas, cerrar la mesa automáticamente
        if ($cuentas_result['cuentas_abiertas'] == 0) {
            $stmt = $pdo->prepare("UPDATE mesas SET estado = 'libre', mesero_id = NULL, fecha_apertura = NULL WHERE hash_mesa = ?");
            $stmt->execute([$cuenta['hash_mesa']]);
        }
    }

    $mensaje = ($accion === 'pagar') ? 'Cuenta pagada exitosamente' : 'Cuenta cerrada exitosamente';

    echo json_encode([
        'success' => true,
        'message' => $mensaje,
        'cuenta_id' => $cuenta_id,
        'cliente' => $cuenta['nombre_cliente'] . ' ' . $cuenta['apellido_cliente'],
        'total' => $cuenta['total'],
        'nuevo_estado' => $nuevo_estado
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
}
?>
